{"Logging": {"LogLevel": {"Default": "Information", "Microsoft.AspNetCore": "Warning"}}, "AllowedHosts": "*", "ConnectionStrings": {"TenantRegistryConnection": "Server=localhost;Database=TenantRegistry;Integrated Security=true;TrustServerCertificate=true;", "DefaultTenantConnection": "Server=localhost;Database=CA_Portal_kumar_associates;Integrated Security=true;TrustServerCertificate=true;", "CA_Portal_kumar_associatesConnection": "Server=localhost;Database=CA_Portal_kumar_associates;Integrated Security=true;TrustServerCertificate=true;"}, "TenantSettings": {"DefaultTenantKey": "kumar-associates", "DefaultTenantName": "Kumar & Associates CA", "DefaultDatabaseName": "CA_Portal_kumar_associates"}, "JwtSettings": {"Key": "super_secret_key_for_ca_portal_application_2024_minimum_32_characters", "Issuer": "CAPortalAPI", "Audience": "CAPortalUsers", "ExpiryInMinutes": 60, "RefreshTokenExpiryInDays": 7, "ClockSkewInMinutes": 5}, "ApiSettings": {"DefaultPageSize": 20, "MaxPageSize": 100, "EnableSwagger": true, "AllowedOrigins": ["http://localhost:3000", "http://localhost:8080", "https://localhost:3000", "https://localhost:8080"], "EnableDetailedErrors": true, "MaxFileUploadSize": 10485760, "AllowedFileTypes": [".pdf", ".doc", ".docx", ".xls", ".xlsx", ".jpg", ".jpeg", ".png"]}, "Features": {"EnableRealTimeNotifications": true, "EnableFileVersioning": true, "EnableAuditLogging": true, "EnableClientPortal": true}}