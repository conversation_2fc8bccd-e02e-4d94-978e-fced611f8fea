// Test Login API Endpoint
const https = require('https');

// Disable SSL certificate validation for localhost testing
process.env["NODE_TLS_REJECT_UNAUTHORIZED"] = 0;

const loginPayload = {
  email: "<EMAIL>",
  password: "password123",
  tenantKey: "kumar-associates"
};

const postData = JSON.stringify(loginPayload);

const options = {
  hostname: 'localhost',
  port: 7000,
  path: '/api/auth/login',
  method: 'POST',
  headers: {
    'Content-Type': 'application/json',
    'Content-Length': Buffer.byteLength(postData)
  }
};

console.log('🔐 Testing Login API...');
console.log('📤 Sending POST request to https://localhost:7000/api/auth/login');
console.log('📋 Payload:', JSON.stringify(loginPayload, null, 2));

const req = https.request(options, (res) => {
  console.log(`📊 Status Code: ${res.statusCode}`);
  console.log(`📋 Headers:`, res.headers);

  let data = '';
  res.on('data', (chunk) => {
    data += chunk;
  });

  res.on('end', () => {
    console.log('\n📥 Response:');
    try {
      const response = JSON.parse(data);
      console.log(JSON.stringify(response, null, 2));
      
      if (response.success) {
        console.log('\n✅ SUCCESS! Login successful!');
        console.log(`🎫 Token: ${response.data.token.substring(0, 50)}...`);
        console.log(`👤 User: ${response.data.user.firstName} ${response.data.user.lastName}`);
        console.log(`🏢 Tenant: ${response.data.tenant.name}`);
      } else {
        console.log('\n❌ ERROR: Login failed');
        console.log('Message:', response.message);
        console.log('Errors:', response.errors);
      }
    } catch (error) {
      console.log('\n❌ ERROR: Failed to parse JSON response');
      console.log('Raw response:', data);
    }
  });
});

req.on('error', (error) => {
  console.error('\n❌ ERROR: Request failed');
  console.error(error.message);
});

req.write(postData);
req.end();

console.log('\n⏳ Waiting for response...');
