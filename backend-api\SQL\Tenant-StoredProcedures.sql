-- =====================================================
-- Tenant Database - Required Stored Procedures
-- =====================================================
-- Run these stored procedures in your tenant database (e.g., CA_Portal_kumar_associates)

-- USE CA_Portal_kumar_associates;
-- GO

-- =====================================================
-- sp_AuthenticateUser - Authenticate user login
-- =====================================================
CREATE OR ALTER PROCEDURE sp_AuthenticateUser
    @JsonPayload NVARCHAR(MAX)
AS
BEGIN
    SET NOCOUNT ON;
    
    DECLARE @Email NVARCHAR(255);
    DECLARE @Password NVARCHAR(255);
    DECLARE @TenantId UNIQUEIDENTIFIER;
    
    -- Parse JSON input
    SELECT 
        @Email = JSON_VALUE(@JsonPayload, '$.Email'),
        @Password = JSON_VALUE(@JsonPayload, '$.Password'),
        @TenantId = JSON_VALUE(@JsonPayload, '$.TenantId');
    
    -- For demo purposes, we'll use a simple password check
    -- In production, use proper password hashing (bcrypt, etc.)
    SELECT 
        UserId,
        Email,
        FirstName,
        LastName,
        Role,
        IsActive,
        CreatedAt,
        LastLoginAt,
        TenantId,
        Phone,
        Department,
        EmployeeCode
    FROM Users 
    WHERE Email = @Email 
      AND IsActive = 1 
      AND TenantId = @TenantId
      AND (
          -- For demo: accept 'password123' for all users
          @Password = 'password123' 
          OR 
          -- Or check against actual password hash
          PasswordHash = @Password
      )
    FOR JSON PATH, WITHOUT_ARRAY_WRAPPER;
END
GO

-- =====================================================
-- sp_UpdateUserLastLogin - Update user's last login time
-- =====================================================
CREATE OR ALTER PROCEDURE sp_UpdateUserLastLogin
    @JsonPayload NVARCHAR(MAX)
AS
BEGIN
    SET NOCOUNT ON;
    
    DECLARE @UserId UNIQUEIDENTIFIER;
    DECLARE @LastLoginAt DATETIME2;
    
    -- Parse JSON input
    SELECT 
        @UserId = JSON_VALUE(@JsonPayload, '$.UserId'),
        @LastLoginAt = JSON_VALUE(@JsonPayload, '$.LastLoginAt');
    
    -- Update last login time
    UPDATE Users 
    SET LastLoginAt = @LastLoginAt
    WHERE UserId = @UserId;
    
    SELECT @@ROWCOUNT as RowsAffected;
END
GO

-- =====================================================
-- sp_GetUserById - Get user by ID
-- =====================================================
CREATE OR ALTER PROCEDURE sp_GetUserById
    @JsonPayload NVARCHAR(MAX)
AS
BEGIN
    SET NOCOUNT ON;
    
    DECLARE @UserId UNIQUEIDENTIFIER;
    
    -- Parse JSON input
    SELECT @UserId = JSON_VALUE(@JsonPayload, '$.UserId');
    
    -- Get user by ID
    SELECT 
        UserId,
        Email,
        FirstName,
        LastName,
        Role,
        IsActive,
        CreatedAt,
        LastLoginAt,
        TenantId,
        Phone,
        Department,
        EmployeeCode
    FROM Users 
    WHERE UserId = @UserId
    FOR JSON PATH, WITHOUT_ARRAY_WRAPPER;
END
GO

-- =====================================================
-- sp_GetUsers - Get paginated list of users
-- =====================================================
CREATE OR ALTER PROCEDURE sp_GetUsers
    @JsonPayload NVARCHAR(MAX)
AS
BEGIN
    SET NOCOUNT ON;
    
    DECLARE @PageNumber INT = 1;
    DECLARE @PageSize INT = 20;
    DECLARE @SearchTerm NVARCHAR(255);
    DECLARE @Role NVARCHAR(50);
    DECLARE @IsActive BIT;
    DECLARE @Department NVARCHAR(100);
    
    -- Parse JSON input
    SELECT 
        @PageNumber = ISNULL(CAST(JSON_VALUE(@JsonPayload, '$.PageNumber') AS INT), 1),
        @PageSize = ISNULL(CAST(JSON_VALUE(@JsonPayload, '$.PageSize') AS INT), 20),
        @SearchTerm = JSON_VALUE(@JsonPayload, '$.SearchTerm'),
        @Role = JSON_VALUE(@JsonPayload, '$.Role'),
        @IsActive = CAST(JSON_VALUE(@JsonPayload, '$.IsActive') AS BIT),
        @Department = JSON_VALUE(@JsonPayload, '$.Department');
    
    -- Calculate offset
    DECLARE @Offset INT = (@PageNumber - 1) * @PageSize;
    
    -- Get total count
    DECLARE @TotalCount INT;
    SELECT @TotalCount = COUNT(*)
    FROM Users 
    WHERE (@SearchTerm IS NULL OR 
           FirstName LIKE '%' + @SearchTerm + '%' OR 
           LastName LIKE '%' + @SearchTerm + '%' OR 
           Email LIKE '%' + @SearchTerm + '%')
      AND (@Role IS NULL OR Role = @Role)
      AND (@IsActive IS NULL OR IsActive = @IsActive)
      AND (@Department IS NULL OR Department = @Department);
    
    -- Get paginated users
    SELECT 
        (
            SELECT 
                UserId,
                Email,
                FirstName,
                LastName,
                Role,
                IsActive,
                CreatedAt,
                LastLoginAt,
                TenantId,
                Phone,
                Department,
                EmployeeCode
            FROM Users 
            WHERE (@SearchTerm IS NULL OR 
                   FirstName LIKE '%' + @SearchTerm + '%' OR 
                   LastName LIKE '%' + @SearchTerm + '%' OR 
                   Email LIKE '%' + @SearchTerm + '%')
              AND (@Role IS NULL OR Role = @Role)
              AND (@IsActive IS NULL OR IsActive = @IsActive)
              AND (@Department IS NULL OR Department = @Department)
            ORDER BY FirstName, LastName
            OFFSET @Offset ROWS
            FETCH NEXT @PageSize ROWS ONLY
            FOR JSON PATH
        ) AS Users,
        @TotalCount AS TotalCount,
        @PageNumber AS PageNumber,
        @PageSize AS PageSize,
        CEILING(CAST(@TotalCount AS FLOAT) / @PageSize) AS TotalPages
    FOR JSON PATH, WITHOUT_ARRAY_WRAPPER;
END
GO

-- =====================================================
-- sp_CreateUser - Create new user
-- =====================================================
CREATE OR ALTER PROCEDURE sp_CreateUser
    @JsonPayload NVARCHAR(MAX)
AS
BEGIN
    SET NOCOUNT ON;
    BEGIN TRY
        BEGIN TRANSACTION;
        
        DECLARE @Email NVARCHAR(255);
        DECLARE @FirstName NVARCHAR(100);
        DECLARE @LastName NVARCHAR(100);
        DECLARE @Role NVARCHAR(50);
        DECLARE @Phone NVARCHAR(20);
        DECLARE @Department NVARCHAR(100);
        DECLARE @EmployeeCode NVARCHAR(20);
        DECLARE @Password NVARCHAR(255);
        DECLARE @CreatedBy UNIQUEIDENTIFIER;
        DECLARE @TenantId UNIQUEIDENTIFIER;
        
        DECLARE @NewUserId UNIQUEIDENTIFIER = NEWID();
        
        -- Parse JSON input
        SELECT 
            @Email = JSON_VALUE(@JsonPayload, '$.Email'),
            @FirstName = JSON_VALUE(@JsonPayload, '$.FirstName'),
            @LastName = JSON_VALUE(@JsonPayload, '$.LastName'),
            @Role = JSON_VALUE(@JsonPayload, '$.Role'),
            @Phone = JSON_VALUE(@JsonPayload, '$.Phone'),
            @Department = JSON_VALUE(@JsonPayload, '$.Department'),
            @EmployeeCode = JSON_VALUE(@JsonPayload, '$.EmployeeCode'),
            @Password = JSON_VALUE(@JsonPayload, '$.Password'),
            @CreatedBy = JSON_VALUE(@JsonPayload, '$.CreatedBy'),
            @TenantId = JSON_VALUE(@JsonPayload, '$.TenantId');
        
        -- Check if email already exists
        IF EXISTS (SELECT 1 FROM Users WHERE Email = @Email)
        BEGIN
            THROW 50001, 'Email already exists', 1;
        END
        
        -- Insert new user (in production, hash the password properly)
        INSERT INTO Users (
            UserId, Email, PasswordHash, FirstName, LastName, Role,
            IsActive, CreatedAt, TenantId, Phone, Department, EmployeeCode
        )
        VALUES (
            @NewUserId, @Email, @Password, @FirstName, @LastName, @Role,
            1, GETUTCDATE(), @TenantId, @Phone, @Department, @EmployeeCode
        );
        
        COMMIT TRANSACTION;
        
        -- Return created user
        SELECT 
            UserId,
            Email,
            FirstName,
            LastName,
            Role,
            IsActive,
            CreatedAt,
            LastLoginAt,
            TenantId,
            Phone,
            Department,
            EmployeeCode
        FROM Users 
        WHERE UserId = @NewUserId
        FOR JSON PATH, WITHOUT_ARRAY_WRAPPER;
        
    END TRY
    BEGIN CATCH
        IF @@TRANCOUNT > 0
            ROLLBACK TRANSACTION;
        THROW;
    END CATCH
END
GO

-- =====================================================
-- sp_GetClients - Get paginated list of clients
-- =====================================================
CREATE OR ALTER PROCEDURE sp_GetClients
    @JsonPayload NVARCHAR(MAX)
AS
BEGIN
    SET NOCOUNT ON;
    
    DECLARE @PageNumber INT = 1;
    DECLARE @PageSize INT = 20;
    DECLARE @SearchTerm NVARCHAR(255);
    DECLARE @ClientType NVARCHAR(50);
    DECLARE @IsActive BIT;
    
    -- Parse JSON input
    SELECT 
        @PageNumber = ISNULL(CAST(JSON_VALUE(@JsonPayload, '$.PageNumber') AS INT), 1),
        @PageSize = ISNULL(CAST(JSON_VALUE(@JsonPayload, '$.PageSize') AS INT), 20),
        @SearchTerm = JSON_VALUE(@JsonPayload, '$.SearchTerm'),
        @ClientType = JSON_VALUE(@JsonPayload, '$.ClientType'),
        @IsActive = CAST(JSON_VALUE(@JsonPayload, '$.IsActive') AS BIT);
    
    -- Calculate offset
    DECLARE @Offset INT = (@PageNumber - 1) * @PageSize;
    
    -- Get total count
    DECLARE @TotalCount INT;
    SELECT @TotalCount = COUNT(*)
    FROM Clients 
    WHERE (@SearchTerm IS NULL OR 
           CompanyName LIKE '%' + @SearchTerm + '%' OR 
           ClientCode LIKE '%' + @SearchTerm + '%' OR 
           ContactPerson LIKE '%' + @SearchTerm + '%')
      AND (@ClientType IS NULL OR ClientType = @ClientType)
      AND (@IsActive IS NULL OR IsActive = @IsActive);
    
    -- Get paginated clients
    SELECT 
        (
            SELECT 
                ClientId,
                ClientCode,
                CompanyName,
                ContactPerson,
                Email,
                Phone,
                Address,
                City,
                State,
                PinCode,
                GSTNumber,
                PANNumber,
                ClientType,
                BusinessType,
                AnnualTurnover,
                IsActive,
                CreatedAt,
                CreatedBy,
                UpdatedAt,
                UpdatedBy
            FROM Clients 
            WHERE (@SearchTerm IS NULL OR 
                   CompanyName LIKE '%' + @SearchTerm + '%' OR 
                   ClientCode LIKE '%' + @SearchTerm + '%' OR 
                   ContactPerson LIKE '%' + @SearchTerm + '%')
              AND (@ClientType IS NULL OR ClientType = @ClientType)
              AND (@IsActive IS NULL OR IsActive = @IsActive)
            ORDER BY CompanyName
            OFFSET @Offset ROWS
            FETCH NEXT @PageSize ROWS ONLY
            FOR JSON PATH
        ) AS Clients,
        @TotalCount AS TotalCount,
        @PageNumber AS PageNumber,
        @PageSize AS PageSize,
        CEILING(CAST(@TotalCount AS FLOAT) / @PageSize) AS TotalPages
    FOR JSON PATH, WITHOUT_ARRAY_WRAPPER;
END
GO

PRINT 'Tenant database stored procedures created successfully!';
PRINT 'You can now test the authentication and user management endpoints.';
GO
