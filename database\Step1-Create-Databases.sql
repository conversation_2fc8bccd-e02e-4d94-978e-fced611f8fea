-- =============================================
-- Step 1: Create Databases Only
-- =============================================

-- Create TenantRegistry Database
IF NOT EXISTS (SELECT name FROM sys.databases WHERE name = 'TenantRegistry')
BEGIN
    CREATE DATABASE [TenantRegistry];
    PRINT 'TenantRegistry database created.';
END
ELSE
BEGIN
    PRINT 'TenantRegistry database already exists.';
END

-- Create KumarAssociatesDB Database
IF NOT EXISTS (SELECT name FROM sys.databases WHERE name = 'KumarAssociatesDB')
BEGIN
    CREATE DATABASE [KumarAssociatesDB];
    PRINT 'KumarAssociatesDB database created.';
END
ELSE
BEGIN
    PRINT 'KumarAssociatesDB database already exists.';
END

PRINT 'Database creation complete. Ready for table creation.';
