-- =============================================
-- Compliance Management Database Setup Script
-- =============================================

-- USE [YourTenantDatabase] -- Replace with actual tenant database name
-- Uncomment and replace with your actual database name before running
GO

-- =============================================
-- 1. Create Compliance Table
-- =============================================
IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='Compliance' AND xtype='U')
BEGIN
    CREATE TABLE [dbo].[Compliance] (
        [ComplianceId] UNIQUEIDENTIFIER PRIMARY KEY DEFAULT NEWID(),
        [ClientId] UNIQUEIDENTIFIER NOT NULL,
        [ComplianceType] NVARCHAR(100) NOT NULL,
        [SubType] NVARCHAR(100) NULL,
        [Description] NVARCHAR(MAX) NULL,
        [DueDate] DATETIME NOT NULL,
        [Status] NVARCHAR(50) NOT NULL DEFAULT 'Pending',
        [Priority] NVARCHAR(20) NOT NULL DEFAULT 'Medium',
        [AssignedTo] UNIQUEIDENTIFIER NULL,
        [CreatedAt] DATETIME NOT NULL DEFAULT GETUTCDATE(),
        [CreatedBy] UNIQUEIDENTIFIER NULL,
        [UpdatedAt] DATETIME NULL,
        [UpdatedBy] UNIQUEIDENTIFIER NULL,
        [CompletedAt] DATETIME NULL,
        [CompletedBy] UNIQUEIDENTIFIER NULL,
        [Notes] NVARCHAR(MAX) NULL,
        [ReminderSent] BIT NOT NULL DEFAULT 0,
        [LastReminderDate] DATETIME NULL,
        [IsActive] BIT NOT NULL DEFAULT 1,
        
        -- Foreign Key Constraints (adjust table names as needed)
        CONSTRAINT [FK_Compliance_Client] FOREIGN KEY ([ClientId]) REFERENCES [Clients]([ClientId]),
        CONSTRAINT [FK_Compliance_AssignedTo] FOREIGN KEY ([AssignedTo]) REFERENCES [Users]([UserId]),
        CONSTRAINT [FK_Compliance_CreatedBy] FOREIGN KEY ([CreatedBy]) REFERENCES [Users]([UserId]),
        CONSTRAINT [FK_Compliance_UpdatedBy] FOREIGN KEY ([UpdatedBy]) REFERENCES [Users]([UserId]),
        CONSTRAINT [FK_Compliance_CompletedBy] FOREIGN KEY ([CompletedBy]) REFERENCES [Users]([UserId])
    );
    
    PRINT 'Compliance table created successfully.';
END
ELSE
BEGIN
    PRINT 'Compliance table already exists.';
END

-- =============================================
-- 2. Create Indexes for Performance
-- =============================================
IF NOT EXISTS (SELECT * FROM sys.indexes WHERE name = 'IX_Compliance_ClientId')
BEGIN
    CREATE INDEX [IX_Compliance_ClientId] ON [Compliance]([ClientId]);
    PRINT 'Index IX_Compliance_ClientId created.';
END

IF NOT EXISTS (SELECT * FROM sys.indexes WHERE name = 'IX_Compliance_DueDate')
BEGIN
    CREATE INDEX [IX_Compliance_DueDate] ON [Compliance]([DueDate]);
    PRINT 'Index IX_Compliance_DueDate created.';
END

IF NOT EXISTS (SELECT * FROM sys.indexes WHERE name = 'IX_Compliance_Status')
BEGIN
    CREATE INDEX [IX_Compliance_Status] ON [Compliance]([Status]);
    PRINT 'Index IX_Compliance_Status created.';
END

IF NOT EXISTS (SELECT * FROM sys.indexes WHERE name = 'IX_Compliance_AssignedTo')
BEGIN
    CREATE INDEX [IX_Compliance_AssignedTo] ON [Compliance]([AssignedTo]);
    PRINT 'Index IX_Compliance_AssignedTo created.';
END

IF NOT EXISTS (SELECT * FROM sys.indexes WHERE name = 'IX_Compliance_ComplianceType')
BEGIN
    CREATE INDEX [IX_Compliance_ComplianceType] ON [Compliance]([ComplianceType]);
    PRINT 'Index IX_Compliance_ComplianceType created.';
END

-- =============================================
-- 3. Create Compliance History Table (Optional)
-- =============================================
IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='ComplianceHistory' AND xtype='U')
BEGIN
    CREATE TABLE [dbo].[ComplianceHistory] (
        [HistoryId] UNIQUEIDENTIFIER PRIMARY KEY DEFAULT NEWID(),
        [ComplianceId] UNIQUEIDENTIFIER NOT NULL,
        [Action] NVARCHAR(100) NOT NULL,
        [OldValue] NVARCHAR(MAX) NULL,
        [NewValue] NVARCHAR(MAX) NULL,
        [ChangedBy] UNIQUEIDENTIFIER NOT NULL,
        [ChangedAt] DATETIME NOT NULL DEFAULT GETUTCDATE(),
        [Notes] NVARCHAR(MAX) NULL,
        
        CONSTRAINT [FK_ComplianceHistory_Compliance] FOREIGN KEY ([ComplianceId]) REFERENCES [Compliance]([ComplianceId]),
        CONSTRAINT [FK_ComplianceHistory_ChangedBy] FOREIGN KEY ([ChangedBy]) REFERENCES [Users]([UserId])
    );
    
    CREATE INDEX [IX_ComplianceHistory_ComplianceId] ON [ComplianceHistory]([ComplianceId]);
    CREATE INDEX [IX_ComplianceHistory_ChangedAt] ON [ComplianceHistory]([ChangedAt]);
    
    PRINT 'ComplianceHistory table created successfully.';
END

-- =============================================
-- 4. Create Compliance Types Reference Table (Optional)
-- =============================================
IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='ComplianceTypes' AND xtype='U')
BEGIN
    CREATE TABLE [dbo].[ComplianceTypes] (
        [TypeId] UNIQUEIDENTIFIER PRIMARY KEY DEFAULT NEWID(),
        [Name] NVARCHAR(100) NOT NULL UNIQUE,
        [Description] NVARCHAR(MAX) NULL,
        [Category] NVARCHAR(50) NULL,
        [DefaultDurationDays] INT NULL,
        [DefaultPriority] NVARCHAR(20) NULL,
        [IsActive] BIT NOT NULL DEFAULT 1,
        [CreatedAt] DATETIME NOT NULL DEFAULT GETUTCDATE()
    );
    
    -- Insert default compliance types
    INSERT INTO [ComplianceTypes] ([Name], [Description], [Category], [DefaultDurationDays], [DefaultPriority])
    VALUES 
        ('Tax Filing', 'Income tax return filing and related tax compliance', 'Tax', 30, 'High'),
        ('GST Return', 'Goods and Services Tax return filing', 'Tax', 20, 'High'),
        ('TDS Return', 'Tax Deducted at Source return filing', 'Tax', 30, 'Medium'),
        ('Audit', 'Financial audit and compliance audit', 'Audit', 90, 'High'),
        ('ROC Filing', 'Registrar of Companies filing and compliance', 'Legal', 30, 'Medium'),
        ('Regulatory Compliance', 'Industry-specific regulatory compliance', 'Regulatory', 60, 'Medium'),
        ('Financial Reporting', 'Financial statements and reporting requirements', 'Financial', 45, 'Medium'),
        ('Legal Compliance', 'Legal documentation and compliance requirements', 'Legal', 30, 'Medium'),
        ('Environmental Compliance', 'Environmental regulations and compliance', 'Environmental', 60, 'Medium'),
        ('Safety Compliance', 'Workplace safety and health compliance', 'Safety', 30, 'High');
    
    PRINT 'ComplianceTypes table created and populated with default data.';
END

-- =============================================
-- 5. Insert Sample Data (for testing)
-- =============================================
-- Note: This assumes you have existing Clients and Users tables
-- Adjust the GUIDs and table references as needed for your system

DECLARE @SampleClientId UNIQUEIDENTIFIER = '123e4567-e89b-12d3-a456-************';
DECLARE @SampleUserId UNIQUEIDENTIFIER = '123e4567-e89b-12d3-a456-************';

-- Only insert sample data if the table is empty
IF NOT EXISTS (SELECT 1 FROM [Compliance])
BEGIN
    INSERT INTO [Compliance] (
        [ClientId], [ComplianceType], [SubType], [Description], [DueDate], 
        [Status], [Priority], [AssignedTo], [CreatedBy], [Notes]
    )
    VALUES 
        (@SampleClientId, 'Tax Filing', 'Annual Return', 'Annual income tax return filing for FY 2023-24', 
         DATEADD(DAY, 30, GETUTCDATE()), 'Pending', 'High', @SampleUserId, @SampleUserId, 
         'Client requires assistance with new tax regulations'),
        
        (@SampleClientId, 'GST Return', 'GSTR-1', 'Monthly GST return filing for December 2024', 
         DATEADD(DAY, 15, GETUTCDATE()), 'In Progress', 'High', @SampleUserId, @SampleUserId, 
         'Documents received, processing in progress'),
        
        (@SampleClientId, 'Audit', 'Internal Audit', 'Quarterly internal audit review', 
         DATEADD(DAY, 45, GETUTCDATE()), 'Pending', 'Medium', @SampleUserId, @SampleUserId, 
         'Scheduled for next month'),
        
        (@SampleClientId, 'ROC Filing', 'Annual Filing', 'Annual return filing with ROC', 
         DATEADD(DAY, -5, GETUTCDATE()), 'Overdue', 'High', @SampleUserId, @SampleUserId, 
         'Urgent: Filing deadline passed, need to file immediately'),
        
        (@SampleClientId, 'TDS Return', 'Quarterly Return', 'TDS return for Q3 FY 2023-24', 
         DATEADD(DAY, 20, GETUTCDATE()), 'Pending', 'Medium', @SampleUserId, @SampleUserId, 
         'Awaiting TDS certificates from client');
    
    PRINT 'Sample compliance data inserted successfully.';
END
ELSE
BEGIN
    PRINT 'Compliance table already contains data. Skipping sample data insertion.';
END

-- =============================================
-- 6. Create a view for easy compliance reporting
-- =============================================
IF EXISTS (SELECT * FROM sys.views WHERE name = 'vw_ComplianceReport')
BEGIN
    DROP VIEW [vw_ComplianceReport];
END

CREATE VIEW [vw_ComplianceReport] AS
SELECT 
    c.[ComplianceId],
    c.[ComplianceType],
    c.[SubType],
    c.[Description],
    c.[DueDate],
    c.[Status],
    c.[Priority],
    cl.[CompanyName] AS [ClientName],
    ISNULL(u.[FirstName] + ' ' + u.[LastName], 'Unassigned') AS [AssignedToName],
    c.[CreatedAt],
    c.[CompletedAt],
    c.[Notes],
    c.[ReminderSent],
    CASE 
        WHEN c.[Status] != 'Completed' AND c.[DueDate] < GETUTCDATE() THEN 1
        ELSE 0
    END AS [IsOverdue],
    DATEDIFF(DAY, GETUTCDATE(), c.[DueDate]) AS [DaysUntilDue],
    CASE 
        WHEN c.[Status] = 'Completed' THEN 'Completed'
        WHEN c.[DueDate] < GETUTCDATE() THEN 'Overdue'
        WHEN DATEDIFF(DAY, GETUTCDATE(), c.[DueDate]) <= 7 THEN 'Due Soon'
        ELSE 'On Track'
    END AS [StatusCategory]
FROM [Compliance] c
INNER JOIN [Clients] cl ON c.[ClientId] = cl.[ClientId]
LEFT JOIN [Users] u ON c.[AssignedTo] = u.[UserId]
WHERE c.[IsActive] = 1;

PRINT 'Compliance reporting view created successfully.';

-- =============================================
-- 7. Create a scheduled job to update overdue items (Optional)
-- =============================================
-- This would typically be done through SQL Server Agent
-- For now, we'll create a procedure that can be called manually or scheduled

IF EXISTS (SELECT * FROM sys.procedures WHERE name = 'sp_UpdateOverdueCompliance')
BEGIN
    DROP PROCEDURE [sp_UpdateOverdueCompliance];
END

GO

CREATE PROCEDURE [sp_UpdateOverdueCompliance]
AS
BEGIN
    SET NOCOUNT ON;
    
    UPDATE [Compliance]
    SET [Status] = 'Overdue'
    WHERE [IsActive] = 1
        AND [Status] NOT IN ('Completed', 'Cancelled')
        AND [DueDate] < GETUTCDATE()
        AND [Status] != 'Overdue';
    
    SELECT @@ROWCOUNT AS [UpdatedItems];
END

GO

PRINT 'Stored procedure sp_UpdateOverdueCompliance created successfully.';

-- =============================================
-- 8. Final Summary
-- =============================================
PRINT '';
PRINT '============================================================';
PRINT 'Compliance Management Database Setup Complete!';
PRINT '============================================================';
PRINT 'Tables Created:';
PRINT '  - Compliance (main table)';
PRINT '  - ComplianceHistory (audit trail)';
PRINT '  - ComplianceTypes (reference data)';
PRINT '';
PRINT 'Views Created:';
PRINT '  - vw_ComplianceReport (reporting view)';
PRINT '';
PRINT 'Procedures Created:';
PRINT '  - sp_UpdateOverdueCompliance (maintenance)';
PRINT '';
PRINT 'Next Steps:';
PRINT '  1. Run the compliance stored procedures script';
PRINT '  2. Update connection strings in the API';
PRINT '  3. Test the API endpoints with real data';
PRINT '  4. Set up scheduled jobs for maintenance';
PRINT '============================================================';
