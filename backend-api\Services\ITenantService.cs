using CAPortalAPI.Models.DTOs;

namespace CAPortalAPI.Services
{
    public interface ITenantService
    {
        /// <summary>
        /// Resolves tenant information by domain or subdomain
        /// </summary>
        /// <param name="domain">Domain or subdomain to resolve</param>
        /// <returns>Tenant information if found</returns>
        Task<TenantDto?> ResolveTenantByDomainAsync(string domain);

        /// <summary>
        /// Resolves tenant information by tenant key
        /// </summary>
        /// <param name="tenantKey">Tenant key to resolve</param>
        /// <returns>Tenant information if found</returns>
        Task<TenantDto?> ResolveTenantByKeyAsync(string tenantKey);

        /// <summary>
        /// Gets the connection string for a specific tenant from TenantRegistry database
        /// </summary>
        /// <param name="tenantKey">Tenant key</param>
        /// <returns>Connection string for the tenant database</returns>
        Task<string?> GetTenantConnectionStringAsync(string tenantKey);

        /// <summary>
        /// Gets tenant information with connection string by tenant key
        /// </summary>
        /// <param name="tenantKey">Tenant key</param>
        /// <returns>Tenant information including connection string</returns>
        Task<TenantDto?> GetTenantWithConnectionStringAsync(string tenantKey);

        /// <summary>
        /// Validates if a tenant is active and accessible
        /// </summary>
        /// <param name="tenantKey">Tenant key to validate</param>
        /// <returns>True if tenant is active, false otherwise</returns>
        Task<bool> ValidateTenantAsync(string tenantKey);

        /// <summary>
        /// Gets all active tenants (for admin purposes)
        /// </summary>
        /// <returns>List of active tenants</returns>
        Task<List<TenantDto>> GetActiveTenantsAsync();

        /// <summary>
        /// Creates a new tenant
        /// </summary>
        /// <param name="request">Tenant creation request</param>
        /// <returns>Created tenant information</returns>
        Task<TenantDto?> CreateTenantAsync(CreateTenantRequestDto request);

        /// <summary>
        /// Updates tenant information
        /// </summary>
        /// <param name="request">Tenant update request</param>
        /// <returns>Updated tenant information</returns>
        Task<TenantDto?> UpdateTenantAsync(UpdateTenantRequestDto request);

        /// <summary>
        /// Gets tenant usage statistics
        /// </summary>
        /// <param name="tenantKey">Tenant key</param>
        /// <returns>Tenant usage information</returns>
        Task<TenantUsageDto?> GetTenantUsageAsync(string tenantKey);
    }
}
