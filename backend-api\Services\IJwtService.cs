using CAPortalAPI.Models.DTOs;

namespace CAPortalAPI.Services
{
    public interface IJwtService
    {
        /// <summary>
        /// Generates a JWT token for the authenticated user
        /// </summary>
        /// <param name="user">User information</param>
        /// <param name="tenant">Tenant information</param>
        /// <returns>JWT token string</returns>
        string GenerateToken(UserDto user, TenantDto tenant);

        /// <summary>
        /// Generates a refresh token
        /// </summary>
        /// <returns>Refresh token string</returns>
        string GenerateRefreshToken();

        /// <summary>
        /// Validates a JWT token and returns the user ID if valid
        /// </summary>
        /// <param name="token">JWT token to validate</param>
        /// <returns>User ID if token is valid, null otherwise</returns>
        Guid? ValidateToken(string token);

        /// <summary>
        /// Gets the expiry time for JWT tokens
        /// </summary>
        /// <returns>Token expiry time</returns>
        DateTime GetTokenExpiry();

        /// <summary>
        /// Extracts user ID from JWT token without validation
        /// </summary>
        /// <param name="token">JWT token</param>
        /// <returns>User ID if found, null otherwise</returns>
        Guid? GetUserIdFromToken(string token);

        /// <summary>
        /// Extracts tenant ID from JWT token without validation
        /// </summary>
        /// <param name="token">JWT token</param>
        /// <returns>Tenant ID if found, null otherwise</returns>
        Guid? GetTenantIdFromToken(string token);

        /// <summary>
        /// Extracts tenant key from JWT token without validation
        /// </summary>
        /// <param name="token">JWT token</param>
        /// <returns>Tenant key if found, null otherwise</returns>
        string? GetTenantKeyFromToken(string token);
    }
}
