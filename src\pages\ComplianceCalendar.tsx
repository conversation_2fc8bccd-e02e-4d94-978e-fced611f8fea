
import { useState, useEffect } from "react";
import { Calendar, Plus, Filter, Bell, CheckCircle, AlertCircle, Edit, Trash2, BarChart3, CalendarDays } from "lucide-react";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Badge } from "@/components/ui/badge";
import { Sidebar } from "@/components/Sidebar";
import { useToast } from "@/hooks/use-toast";
import { complianceService } from "@/services/complianceService";

interface ComplianceItem {
  complianceId: string;
  clientId: string;
  clientName: string;
  complianceType: string;
  subType?: string;
  dueDate: string;
  description: string;
  status: 'Pending' | 'In Progress' | 'Completed' | 'Overdue' | 'Cancelled';
  priority: 'Low' | 'Medium' | 'High' | 'Critical';
  assignedTo: string;
  assignedToName: string;
  createdAt: string;
  completedAt?: string;
  notes: string;
  reminderSent: boolean;
  lastReminderDate?: string;
}

interface ComplianceDashboard {
  totalItems: number;
  completedItems: number;
  pendingItems: number;
  overdueItems: number;
}

const ComplianceCalendar = () => {
  const [selectedFilter, setSelectedFilter] = useState('all');
  const [searchTerm, setSearchTerm] = useState('');
  const [showAddDialog, setShowAddDialog] = useState(false);
  const [editingItem, setEditingItem] = useState<ComplianceItem | null>(null);
  const [loading, setLoading] = useState(false);
  const [dashboardData, setDashboardData] = useState<ComplianceDashboard>({
    totalItems: 0,
    completedItems: 0,
    pendingItems: 0,
    overdueItems: 0
  });
  const [complianceItems, setComplianceItems] = useState<ComplianceItem[]>([]);
  const [activeTab, setActiveTab] = useState('dashboard');
  const { toast } = useToast();

  useEffect(() => {
    loadComplianceData();
  }, []);

  const loadComplianceData = async () => {
    try {
      setLoading(true);

      // Load dashboard stats
      const stats = await complianceService.getComplianceStats();
      setDashboardData(stats);

      // Load compliance items
      const itemsResponse = await complianceService.getComplianceItems('', 1, 50);
      setComplianceItems(itemsResponse.complianceItems);

    } catch (error: any) {
      toast({
        title: "Error",
        description: error.message || "Failed to load compliance data",
        variant: "destructive"
      });
    } finally {
      setLoading(false);
    }
  };

  const [newItem, setNewItem] = useState<Partial<ComplianceItem>>({
    clientId: "",
    clientName: "",
    complianceType: "Tax Filing",
    subType: "",
    dueDate: "",
    description: "",
    priority: "Medium",
    assignedTo: "",
    assignedToName: "",
    notes: ""
  });

  const staff = ["Priya Sharma", "Amit Patel", "Anjali Gupta", "Vikash Singh"];
  const complianceTypes = ["Tax Filing", "Audit", "Regulatory Compliance", "Financial Reporting", "Legal Compliance"];
  const priorities = ["Low", "Medium", "High", "Critical"];
  const statuses = ["Pending", "In Progress", "Completed", "Overdue", "Cancelled"];

  const handleAddItem = async () => {
    if (!newItem.clientId || !newItem.complianceType || !newItem.dueDate || !newItem.assignedTo) {
      toast({
        title: "Error",
        description: "Please fill in all required fields",
        variant: "destructive"
      });
      return;
    }

    try {
      setLoading(true);

      const createRequest = {
        clientId: newItem.clientId!,
        complianceType: newItem.complianceType!,
        subType: newItem.subType || "",
        description: newItem.description || "",
        dueDate: newItem.dueDate!,
        priority: newItem.priority!,
        assignedTo: newItem.assignedTo!,
        notes: newItem.notes || ""
      };

      await complianceService.createComplianceItem(createRequest);

      // Reset form
      setNewItem({
        clientId: "",
        clientName: "",
        complianceType: "Tax Filing",
        subType: "",
        dueDate: "",
        description: "",
        priority: "Medium",
        assignedTo: "",
        assignedToName: "",
        notes: ""
      });
      setShowAddDialog(false);

      // Reload data
      await loadComplianceData();

      toast({
        title: "Success",
        description: "Compliance item added successfully"
      });
    } catch (error: any) {
      toast({
        title: "Error",
        description: error.message || "Failed to add compliance item",
        variant: "destructive"
      });
    } finally {
      setLoading(false);
    }
  };

  const handleEditItem = async () => {
    if (!editingItem) return;

    try {
      setLoading(true);

      const updateRequest = {
        complianceId: editingItem.complianceId,
        clientId: editingItem.clientId,
        complianceType: editingItem.complianceType,
        subType: editingItem.subType || "",
        description: editingItem.description,
        dueDate: editingItem.dueDate,
        status: editingItem.status,
        priority: editingItem.priority,
        assignedTo: editingItem.assignedTo,
        notes: editingItem.notes
      };

      await complianceService.updateComplianceItem(editingItem.complianceId, updateRequest);
      setEditingItem(null);
      await loadComplianceData();

      toast({
        title: "Success",
        description: "Compliance item updated successfully"
      });
    } catch (error: any) {
      toast({
        title: "Error",
        description: error.message || "Failed to update compliance item",
        variant: "destructive"
      });
    } finally {
      setLoading(false);
    }
  };

  const handleDeleteItem = async (id: string) => {
    try {
      setLoading(true);
      await complianceService.deleteComplianceItem(id);
      await loadComplianceData();

      toast({
        title: "Success",
        description: "Compliance item deleted successfully"
      });
    } catch (error: any) {
      toast({
        title: "Error",
        description: error.message || "Failed to delete compliance item",
        variant: "destructive"
      });
    } finally {
      setLoading(false);
    }
  };

  const handleMarkComplete = async (id: string) => {
    try {
      setLoading(true);
      await complianceService.markCompleted(id, "Marked as completed");
      await loadComplianceData();

      toast({
        title: "Success",
        description: "Compliance item marked as completed"
      });
    } catch (error: any) {
      toast({
        title: "Error",
        description: error.message || "Failed to mark as completed",
        variant: "destructive"
      });
    } finally {
      setLoading(false);
    }
  };

  const handleSendReminder = (item: ComplianceItem) => {
    toast({
      title: "Reminder Sent",
      description: `Reminder sent to ${item.assignedToName} for ${item.complianceType}`
    });
  };

  const getStatusColor = (status: string) => {
    switch (status.toLowerCase()) {
      case 'completed': return 'bg-green-100 text-green-800';
      case 'overdue': return 'bg-red-100 text-red-800';
      case 'in progress': return 'bg-blue-100 text-blue-800';
      case 'cancelled': return 'bg-gray-100 text-gray-800';
      default: return 'bg-yellow-100 text-yellow-800';
    }
  };

  const getPriorityColor = (priority: string) => {
    switch (priority.toLowerCase()) {
      case 'critical': return 'bg-red-100 text-red-800';
      case 'high': return 'bg-orange-100 text-orange-800';
      case 'medium': return 'bg-blue-100 text-blue-800';
      case 'low': return 'bg-green-100 text-green-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const filteredItems = complianceItems.filter(item => {
    const matchesFilter = selectedFilter === 'all' || item.status.toLowerCase() === selectedFilter.toLowerCase();
    const matchesSearch = item.clientName.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         item.complianceType.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         item.description.toLowerCase().includes(searchTerm.toLowerCase());
    return matchesFilter && matchesSearch;
  });

  const FormFields = ({ item, setItem, isEdit = false }: {
    item: Partial<ComplianceItem>,
    setItem: (item: Partial<ComplianceItem>) => void,
    isEdit?: boolean
  }) => (
    <div className="space-y-4">
      <div>
        <Label htmlFor="clientName">Client *</Label>
        <Input
          id="clientName"
          value={item.clientName || ""}
          onChange={(e) => setItem({ ...item, clientName: e.target.value, clientId: e.target.value })}
          placeholder="Client name"
        />
      </div>
      <div className="grid grid-cols-2 gap-4">
        <div>
          <Label htmlFor="complianceType">Compliance Type *</Label>
          <Select value={item.complianceType} onValueChange={(value) => setItem({ ...item, complianceType: value })}>
            <SelectTrigger>
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              {complianceTypes.map(type => (
                <SelectItem key={type} value={type}>{type}</SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>
        <div>
          <Label htmlFor="subType">Sub Type</Label>
          <Input
            id="subType"
            value={item.subType || ""}
            onChange={(e) => setItem({ ...item, subType: e.target.value })}
            placeholder="e.g., GSTR-1, Annual Return"
          />
        </div>
      </div>
      <div className="grid grid-cols-2 gap-4">
        <div>
          <Label htmlFor="priority">Priority</Label>
          <Select value={item.priority} onValueChange={(value) => setItem({ ...item, priority: value as any })}>
            <SelectTrigger>
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              {priorities.map(priority => (
                <SelectItem key={priority} value={priority}>{priority}</SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>
        <div>
          <Label htmlFor="dueDate">Due Date *</Label>
          <Input
            id="dueDate"
            type="date"
            value={item.dueDate || ""}
            onChange={(e) => setItem({ ...item, dueDate: e.target.value })}
          />
        </div>
      </div>
      <div>
        <Label htmlFor="assignedTo">Assigned To *</Label>
        <Select value={item.assignedTo} onValueChange={(value) => setItem({ ...item, assignedTo: value, assignedToName: value })}>
          <SelectTrigger>
            <SelectValue placeholder="Select staff" />
          </SelectTrigger>
          <SelectContent>
            {staff.map(member => (
              <SelectItem key={member} value={member}>{member}</SelectItem>
            ))}
          </SelectContent>
        </Select>
      </div>
      {isEdit && (
        <div>
          <Label htmlFor="status">Status</Label>
          <Select value={item.status} onValueChange={(value) => setItem({ ...item, status: value as any })}>
            <SelectTrigger>
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              {statuses.map(status => (
                <SelectItem key={status} value={status}>{status}</SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>
      )}
      <div>
        <Label htmlFor="description">Description</Label>
        <Textarea
          id="description"
          value={item.description || ""}
          onChange={(e) => setItem({ ...item, description: e.target.value })}
          placeholder="Additional details..."
        />
      </div>
      <div>
        <Label htmlFor="notes">Notes</Label>
        <Textarea
          id="notes"
          value={item.notes || ""}
          onChange={(e) => setItem({ ...item, notes: e.target.value })}
          placeholder="Internal notes..."
        />
      </div>
    </div>
  );

// Calendar View Component
const ComplianceCalendarView = ({ complianceItems, onItemClick, loading }: {
  complianceItems: ComplianceItem[];
  onItemClick: (item: ComplianceItem) => void;
  loading: boolean;
}) => {
  const [currentDate, setCurrentDate] = useState(new Date());

  // Group compliance items by date
  const getItemsForDate = (date: Date) => {
    const dateStr = date.toISOString().split('T')[0];
    return complianceItems.filter(item =>
      item.dueDate.split('T')[0] === dateStr
    );
  };

  // Generate calendar days for current month
  const generateCalendarDays = () => {
    const year = currentDate.getFullYear();
    const month = currentDate.getMonth();

    const firstDay = new Date(year, month, 1);
    const lastDay = new Date(year, month + 1, 0);
    const startDate = new Date(firstDay);
    startDate.setDate(startDate.getDate() - firstDay.getDay());

    const days = [];
    const currentDay = new Date(startDate);

    for (let i = 0; i < 42; i++) { // 6 weeks * 7 days
      days.push(new Date(currentDay));
      currentDay.setDate(currentDay.getDate() + 1);
    }

    return days;
  };

  const calendarDays = generateCalendarDays();
  const monthNames = [
    'January', 'February', 'March', 'April', 'May', 'June',
    'July', 'August', 'September', 'October', 'November', 'December'
  ];

  const navigateMonth = (direction: 'prev' | 'next') => {
    const newDate = new Date(currentDate);
    newDate.setMonth(newDate.getMonth() + (direction === 'next' ? 1 : -1));
    setCurrentDate(newDate);
  };

  const isToday = (date: Date) => {
    const today = new Date();
    return date.toDateString() === today.toDateString();
  };

  const isCurrentMonth = (date: Date) => {
    return date.getMonth() === currentDate.getMonth();
  };

  if (loading) {
    return (
      <div className="text-center py-12">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4"></div>
        <p className="text-gray-600">Loading calendar...</p>
      </div>
    );
  }

  return (
    <div className="space-y-4">
      {/* Calendar Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <h3 className="text-lg font-semibold">
            {monthNames[currentDate.getMonth()]} {currentDate.getFullYear()}
          </h3>
          <div className="flex space-x-1">
            <Button
              variant="outline"
              size="sm"
              onClick={() => navigateMonth('prev')}
            >
              ←
            </Button>
            <Button
              variant="outline"
              size="sm"
              onClick={() => setCurrentDate(new Date())}
            >
              Today
            </Button>
            <Button
              variant="outline"
              size="sm"
              onClick={() => navigateMonth('next')}
            >
              →
            </Button>
          </div>
        </div>
      </div>

      {/* Calendar Grid */}
      <div className="border rounded-lg overflow-hidden">
        {/* Day Headers */}
        <div className="grid grid-cols-7 bg-gray-50">
          {['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat'].map(day => (
            <div key={day} className="p-3 text-center text-sm font-medium text-gray-700 border-r border-gray-200 last:border-r-0">
              {day}
            </div>
          ))}
        </div>

        {/* Calendar Days */}
        <div className="grid grid-cols-7">
          {calendarDays.map((date, index) => {
            const items = getItemsForDate(date);
            const isCurrentMonthDay = isCurrentMonth(date);
            const isTodayDate = isToday(date);

            return (
              <div
                key={index}
                className={`min-h-[120px] p-2 border-r border-b border-gray-200 last:border-r-0 ${
                  !isCurrentMonthDay ? 'bg-gray-50 text-gray-400' : 'bg-white'
                } ${isTodayDate ? 'bg-blue-50' : ''}`}
              >
                <div className={`text-sm font-medium mb-1 ${
                  isTodayDate ? 'text-blue-600' : isCurrentMonthDay ? 'text-gray-900' : 'text-gray-400'
                }`}>
                  {date.getDate()}
                </div>

                <div className="space-y-1">
                  {items.slice(0, 3).map((item, itemIndex) => (
                    <div
                      key={itemIndex}
                      onClick={() => onItemClick(item)}
                      className={`text-xs p-1 rounded cursor-pointer hover:opacity-80 ${
                        item.status === 'Completed' ? 'bg-green-100 text-green-800' :
                        item.status === 'Overdue' ? 'bg-red-100 text-red-800' :
                        item.status === 'In Progress' ? 'bg-blue-100 text-blue-800' :
                        'bg-yellow-100 text-yellow-800'
                      }`}
                      title={`${item.complianceType} - ${item.clientName}`}
                    >
                      <div className="font-medium truncate">{item.complianceType}</div>
                      <div className="truncate">{item.clientName}</div>
                    </div>
                  ))}
                  {items.length > 3 && (
                    <div className="text-xs text-gray-500 text-center">
                      +{items.length - 3} more
                    </div>
                  )}
                </div>
              </div>
            );
          })}
        </div>
      </div>

      {/* Legend */}
      <div className="flex items-center justify-center space-x-6 text-sm">
        <div className="flex items-center space-x-2">
          <div className="w-3 h-3 bg-yellow-100 border border-yellow-300 rounded"></div>
          <span>Pending</span>
        </div>
        <div className="flex items-center space-x-2">
          <div className="w-3 h-3 bg-blue-100 border border-blue-300 rounded"></div>
          <span>In Progress</span>
        </div>
        <div className="flex items-center space-x-2">
          <div className="w-3 h-3 bg-green-100 border border-green-300 rounded"></div>
          <span>Completed</span>
        </div>
        <div className="flex items-center space-x-2">
          <div className="w-3 h-3 bg-red-100 border border-red-300 rounded"></div>
          <span>Overdue</span>
        </div>
      </div>
    </div>
  );
};

  return (
    <div className="flex min-h-screen bg-gray-50">
      <Sidebar />

      <main className="flex-1 ml-64 p-8">
        <div className="max-w-7xl mx-auto">
          {/* Header */}
          <div className="flex justify-between items-center mb-8">
            <div>
              <h1 className="text-3xl font-bold text-gray-900 flex items-center">
                <Calendar className="w-8 h-8 text-blue-600 mr-3" />
                Compliance Management
              </h1>
              <p className="text-gray-600 mt-2">Comprehensive compliance tracking and management system</p>
            </div>
            <Dialog open={showAddDialog} onOpenChange={setShowAddDialog}>
              <DialogTrigger asChild>
                <Button className="bg-blue-600 hover:bg-blue-700" disabled={loading}>
                  <Plus className="w-4 h-4 mr-2" />
                  Add Compliance Item
                </Button>
              </DialogTrigger>
              <DialogContent className="max-w-2xl">
                <DialogHeader>
                  <DialogTitle>Add New Compliance Item</DialogTitle>
                  <DialogDescription>
                    Create a new compliance deadline for tracking
                  </DialogDescription>
                </DialogHeader>
                <FormFields item={newItem} setItem={setNewItem} />
                <div className="flex justify-end space-x-2 mt-6">
                  <Button variant="outline" onClick={() => setShowAddDialog(false)}>
                    Cancel
                  </Button>
                  <Button onClick={handleAddItem} disabled={loading}>
                    {loading ? "Adding..." : "Add Item"}
                  </Button>
                </div>
              </DialogContent>
            </Dialog>
          </div>

          {/* Tabs */}
          <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-6">
            <TabsList className="grid w-full grid-cols-3">
              <TabsTrigger value="dashboard" className="flex items-center gap-2">
                <BarChart3 className="w-4 h-4" />
                Dashboard
              </TabsTrigger>
              <TabsTrigger value="calendar" className="flex items-center gap-2">
                <CalendarDays className="w-4 h-4" />
                Calendar View
              </TabsTrigger>
              <TabsTrigger value="list" className="flex items-center gap-2">
                <CheckCircle className="w-4 h-4" />
                List View
              </TabsTrigger>
            </TabsList>

            {/* Dashboard Tab */}
            <TabsContent value="dashboard" className="space-y-6">
              {/* Stats Summary */}
              <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
                <Card>
                  <CardContent className="p-6">
                    <div className="flex items-center">
                      <div className="p-2 bg-blue-100 rounded-lg">
                        <Calendar className="h-6 w-6 text-blue-600" />
                      </div>
                      <div className="ml-4">
                        <p className="text-sm font-medium text-gray-600">Total Items</p>
                        <p className="text-2xl font-bold text-gray-900">{dashboardData.totalItems}</p>
                      </div>
                    </div>
                  </CardContent>
                </Card>

                <Card>
                  <CardContent className="p-6">
                    <div className="flex items-center">
                      <div className="p-2 bg-yellow-100 rounded-lg">
                        <AlertCircle className="h-6 w-6 text-yellow-600" />
                      </div>
                      <div className="ml-4">
                        <p className="text-sm font-medium text-gray-600">Pending</p>
                        <p className="text-2xl font-bold text-gray-900">{dashboardData.pendingItems}</p>
                      </div>
                    </div>
                  </CardContent>
                </Card>

                <Card>
                  <CardContent className="p-6">
                    <div className="flex items-center">
                      <div className="p-2 bg-red-100 rounded-lg">
                        <AlertCircle className="h-6 w-6 text-red-600" />
                      </div>
                      <div className="ml-4">
                        <p className="text-sm font-medium text-gray-600">Overdue</p>
                        <p className="text-2xl font-bold text-gray-900">{dashboardData.overdueItems}</p>
                      </div>
                    </div>
                  </CardContent>
                </Card>

                <Card>
                  <CardContent className="p-6">
                    <div className="flex items-center">
                      <div className="p-2 bg-green-100 rounded-lg">
                        <CheckCircle className="h-6 w-6 text-green-600" />
                      </div>
                      <div className="ml-4">
                        <p className="text-sm font-medium text-gray-600">Completed</p>
                        <p className="text-2xl font-bold text-gray-900">{dashboardData.completedItems}</p>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </div>
            </TabsContent>

            {/* Calendar Tab */}
            <TabsContent value="calendar" className="space-y-6">
              <Card>
                <CardHeader>
                  <CardTitle>Compliance Calendar</CardTitle>
                  <CardDescription>Visual calendar view of all compliance deadlines</CardDescription>
                </CardHeader>
                <CardContent>
                  <ComplianceCalendarView
                    complianceItems={complianceItems}
                    onItemClick={(item) => setEditingItem(item)}
                    loading={loading}
                  />
                </CardContent>
              </Card>
            </TabsContent>

            {/* List Tab */}
            <TabsContent value="list" className="space-y-6">
              {/* Filters and Search */}
              <Card>
                <CardContent className="p-6">
                  <div className="flex flex-col md:flex-row gap-4">
                    <div className="flex-1">
                      <Input
                        placeholder="Search clients, compliance types, or descriptions..."
                        value={searchTerm}
                        onChange={(e) => setSearchTerm(e.target.value)}
                        className="w-full"
                      />
                    </div>
                    <div className="flex gap-2">
                      {['all', 'pending', 'completed', 'overdue'].map(filter => (
                        <Button
                          key={filter}
                          variant={selectedFilter === filter ? "default" : "outline"}
                          onClick={() => setSelectedFilter(filter)}
                          className="capitalize"
                          size="sm"
                        >
                          {filter}
                        </Button>
                      ))}
                    </div>
                  </div>
                </CardContent>
              </Card>

              {/* Compliance Items List */}
              <div className="space-y-4">
                {loading ? (
                  <Card>
                    <CardContent className="p-12 text-center">
                      <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4"></div>
                      <p className="text-gray-600">Loading compliance items...</p>
                    </CardContent>
                  </Card>
                ) : filteredItems.length === 0 ? (
                  <Card>
                    <CardContent className="p-12 text-center">
                      <Calendar className="w-16 h-16 text-gray-400 mx-auto mb-4" />
                      <h3 className="text-lg font-medium text-gray-900 mb-2">No compliance items found</h3>
                      <p className="text-gray-600 mb-4">
                        {searchTerm || selectedFilter !== 'all'
                          ? 'Try adjusting your search or filters.'
                          : 'Get started by adding your first compliance item.'}
                      </p>
                      <Button onClick={() => setShowAddDialog(true)}>
                        <Plus className="w-4 h-4 mr-2" />
                        Add Compliance Item
                      </Button>
                    </CardContent>
                  </Card>
                ) : (
                  filteredItems.map((item) => (
                    <Card key={item.complianceId}>
                      <CardContent className="p-6">
                        <div className="flex items-center justify-between">
                          <div className="flex-1">
                            <div className="flex items-center gap-3 mb-2">
                              <h3 className="text-lg font-semibold text-gray-900">{item.complianceType}</h3>
                              {item.subType && (
                                <Badge variant="outline">{item.subType}</Badge>
                              )}
                              <Badge className={getStatusColor(item.status)}>
                                {item.status}
                              </Badge>
                              <Badge className={getPriorityColor(item.priority)}>
                                {item.priority}
                              </Badge>
                            </div>
                            <p className="text-gray-600 mb-1 font-medium">{item.clientName}</p>
                            <p className="text-sm text-gray-500 mb-2">{item.description}</p>
                            <div className="flex items-center gap-4 text-sm text-gray-500">
                              <span>Due: {new Date(item.dueDate).toLocaleDateString()}</span>
                              <span>Assigned to: {item.assignedToName}</span>
                              <span>Created: {new Date(item.createdAt).toLocaleDateString()}</span>
                            </div>
                            {item.notes && (
                              <p className="text-sm text-gray-600 mt-2 italic">Notes: {item.notes}</p>
                            )}
                          </div>
                          <div className="flex items-center gap-2 ml-4">
                            <Button size="sm" variant="outline" onClick={() => handleSendReminder(item)}>
                              <Bell className="w-4 h-4 mr-1" />
                              Remind
                            </Button>
                            <Button size="sm" variant="outline" onClick={() => setEditingItem(item)}>
                              <Edit className="w-4 h-4 mr-1" />
                              Edit
                            </Button>
                            <Button
                              size="sm"
                              variant="outline"
                              onClick={() => handleDeleteItem(item.complianceId)}
                              disabled={loading}
                            >
                              <Trash2 className="w-4 h-4 mr-1" />
                              Delete
                            </Button>
                            {item.status === 'Pending' && (
                              <Button
                                size="sm"
                                className="bg-green-600 hover:bg-green-700"
                                onClick={() => handleMarkComplete(item.complianceId)}
                                disabled={loading}
                              >
                                <CheckCircle className="w-4 h-4 mr-1" />
                                Complete
                              </Button>
                            )}
                          </div>
                        </div>
                      </CardContent>
                    </Card>
                  ))
                )}
              </div>
            </TabsContent>
          </Tabs>

          {/* Edit Dialog */}
          <Dialog open={!!editingItem} onOpenChange={() => setEditingItem(null)}>
            <DialogContent className="max-w-2xl">
              <DialogHeader>
                <DialogTitle>Edit Compliance Item</DialogTitle>
                <DialogDescription>
                  Update compliance deadline details
                </DialogDescription>
              </DialogHeader>
              {editingItem && (
                <FormFields
                  item={editingItem}
                  setItem={(updatedItem) => setEditingItem({ ...editingItem, ...updatedItem })}
                  isEdit={true}
                />
              )}
              <div className="flex justify-end space-x-2 mt-6">
                <Button variant="outline" onClick={() => setEditingItem(null)}>
                  Cancel
                </Button>
                <Button onClick={handleEditItem} disabled={loading}>
                  {loading ? "Updating..." : "Update Item"}
                </Button>
              </div>
            </DialogContent>
          </Dialog>
        </div>
      </main>
    </div>
  );
};

export default ComplianceCalendar;
