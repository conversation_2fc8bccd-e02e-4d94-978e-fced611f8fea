-- =============================================
-- Stored Procedures for CA_Portal_kumar_associates Database
-- =============================================

USE [CA_Portal_kumar_associates];

PRINT '============================================================';
PRINT 'Creating Stored Procedures for Compliance Calendar';
PRINT '============================================================';

-- =============================================
-- 1. sp_GetComplianceStats - Dashboard statistics
-- =============================================
CREATE PROCEDURE sp_GetComplianceStats
    @Payload NVARCHAR(MAX)
AS
BEGIN
    SET NOCOUNT ON;
    
    DECLARE @ClientId UNIQUEIDENTIFIER
    SELECT @ClientId = JSON_VALUE(@Payload, '$.ClientId')
    
    SELECT 
        COUNT(*) AS TotalItems,
        SUM(CASE WHEN Status = 'Completed' THEN 1 ELSE 0 END) AS CompletedItems,
        SUM(CASE WHEN Status = 'Pending' THEN 1 ELSE 0 END) AS PendingItems,
        SUM(CASE WHEN Status = 'Overdue' THEN 1 ELSE 0 END) AS OverdueItems,
        SUM(CASE WHEN Status = 'In Progress' THEN 1 ELSE 0 END) AS InProgressItems
    FROM Compliance
    WHERE IsActive = 1
        AND (@ClientId IS NULL OR ClientId = @ClientId)
    
    FOR JSON PATH, WITHOUT_ARRAY_WRAPPER
END

PRINT '✅ sp_GetComplianceStats created';

-- =============================================
-- 2. sp_GetComplianceItems - Main list with pagination
-- =============================================
CREATE PROCEDURE sp_GetComplianceItems
    @Payload NVARCHAR(MAX)
AS
BEGIN
    SET NOCOUNT ON;
    
    DECLARE @ClientId NVARCHAR(50)
    DECLARE @PageNumber INT
    DECLARE @PageSize INT
    DECLARE @Status NVARCHAR(50)
    DECLARE @Priority NVARCHAR(20)
    
    -- Parse JSON payload
    SELECT 
        @ClientId = JSON_VALUE(@Payload, '$.ClientId'),
        @PageNumber = ISNULL(JSON_VALUE(@Payload, '$.PageNumber'), 1),
        @PageSize = ISNULL(JSON_VALUE(@Payload, '$.PageSize'), 20),
        @Status = JSON_VALUE(@Payload, '$.Status'),
        @Priority = JSON_VALUE(@Payload, '$.Priority')
    
    DECLARE @Offset INT = (@PageNumber - 1) * @PageSize
    
    -- Get compliance items
    SELECT 
        c.ComplianceId,
        c.ClientId,
        cl.CompanyName AS ClientName,
        c.ComplianceType,
        c.SubType,
        c.Description,
        c.DueDate,
        c.Status,
        c.Priority,
        c.AssignedTo,
        ISNULL(u.FirstName + ' ' + u.LastName, 'Unassigned') AS AssignedToName,
        c.CreatedAt,
        c.CompletedAt,
        c.Notes,
        c.ReminderSent,
        c.LastReminderDate,
        c.EstimatedHours,
        c.ActualHours,
        c.ComplianceYear,
        c.RegulatoryBody
    FROM Compliance c
    INNER JOIN Clients cl ON c.ClientId = cl.ClientId
    LEFT JOIN Users u ON c.AssignedTo = u.UserId
    WHERE c.IsActive = 1
        AND (@ClientId IS NULL OR @ClientId = '' OR c.ClientId = @ClientId)
        AND (@Status IS NULL OR @Status = '' OR c.Status = @Status)
        AND (@Priority IS NULL OR @Priority = '' OR c.Priority = @Priority)
    ORDER BY 
        CASE WHEN c.Status = 'Overdue' THEN 1
             WHEN c.Status = 'Pending' THEN 2
             WHEN c.Status = 'In Progress' THEN 3
             ELSE 4 END,
        c.DueDate ASC
    OFFSET @Offset ROWS
    FETCH NEXT @PageSize ROWS ONLY
    
    FOR JSON PATH
END

PRINT '✅ sp_GetComplianceItems created';

-- =============================================
-- 3. sp_GetComplianceCalendar - Calendar view data
-- =============================================
CREATE PROCEDURE sp_GetComplianceCalendar
    @Payload NVARCHAR(MAX)
AS
BEGIN
    SET NOCOUNT ON;
    
    DECLARE @StartDate DATETIME
    DECLARE @EndDate DATETIME
    DECLARE @ClientId UNIQUEIDENTIFIER
    
    SELECT 
        @StartDate = JSON_VALUE(@Payload, '$.StartDate'),
        @EndDate = JSON_VALUE(@Payload, '$.EndDate'),
        @ClientId = JSON_VALUE(@Payload, '$.ClientId')
    
    -- Set default date range if not provided
    IF @StartDate IS NULL
        SET @StartDate = DATEADD(DAY, -30, GETUTCDATE())
    
    IF @EndDate IS NULL
        SET @EndDate = DATEADD(DAY, 90, GETUTCDATE())
    
    SELECT 
        c.ComplianceId,
        c.ComplianceType AS Title,
        cl.CompanyName AS ClientName,
        c.ComplianceType,
        c.DueDate,
        c.Status,
        c.Priority,
        ISNULL(u.FirstName + ' ' + u.LastName, 'Unassigned') AS AssignedToName,
        CASE 
            WHEN c.Status != 'Completed' AND c.DueDate < GETUTCDATE() THEN 1
            ELSE 0
        END AS IsOverdue,
        DATEDIFF(DAY, GETUTCDATE(), c.DueDate) AS DaysUntilDue
    FROM Compliance c
    INNER JOIN Clients cl ON c.ClientId = cl.ClientId
    LEFT JOIN Users u ON c.AssignedTo = u.UserId
    WHERE c.IsActive = 1
        AND c.DueDate BETWEEN @StartDate AND @EndDate
        AND (@ClientId IS NULL OR c.ClientId = @ClientId)
    ORDER BY c.DueDate ASC
    
    FOR JSON PATH
END

PRINT '✅ sp_GetComplianceCalendar created';

-- =============================================
-- 4. sp_CreateComplianceItem - Create new item
-- =============================================
CREATE PROCEDURE sp_CreateComplianceItem
    @Payload NVARCHAR(MAX)
AS
BEGIN
    SET NOCOUNT ON;
    
    DECLARE @ComplianceId UNIQUEIDENTIFIER = NEWID()
    DECLARE @ClientId UNIQUEIDENTIFIER
    DECLARE @ComplianceType NVARCHAR(100)
    DECLARE @SubType NVARCHAR(100)
    DECLARE @Description NVARCHAR(MAX)
    DECLARE @DueDate DATETIME
    DECLARE @Priority NVARCHAR(20)
    DECLARE @AssignedTo UNIQUEIDENTIFIER
    DECLARE @Notes NVARCHAR(MAX)
    DECLARE @CreatedBy UNIQUEIDENTIFIER
    DECLARE @EstimatedHours DECIMAL(5,2)
    
    -- Parse JSON payload
    SELECT 
        @ClientId = JSON_VALUE(@Payload, '$.ClientId'),
        @ComplianceType = JSON_VALUE(@Payload, '$.ComplianceType'),
        @SubType = JSON_VALUE(@Payload, '$.SubType'),
        @Description = JSON_VALUE(@Payload, '$.Description'),
        @DueDate = JSON_VALUE(@Payload, '$.DueDate'),
        @Priority = ISNULL(JSON_VALUE(@Payload, '$.Priority'), 'Medium'),
        @AssignedTo = JSON_VALUE(@Payload, '$.AssignedTo'),
        @Notes = JSON_VALUE(@Payload, '$.Notes'),
        @CreatedBy = JSON_VALUE(@Payload, '$.CreatedBy'),
        @EstimatedHours = JSON_VALUE(@Payload, '$.EstimatedHours')
    
    -- Insert new compliance item
    INSERT INTO Compliance (
        ComplianceId, ClientId, ComplianceType, SubType, Description,
        DueDate, Priority, AssignedTo, Notes, CreatedBy, CreatedAt, 
        EstimatedHours, ComplianceYear, Status
    )
    VALUES (
        @ComplianceId, @ClientId, @ComplianceType, @SubType, @Description,
        @DueDate, @Priority, @AssignedTo, @Notes, @CreatedBy, GETUTCDATE(), 
        @EstimatedHours, YEAR(GETUTCDATE()), 'Pending'
    )
    
    -- Return the created item
    SELECT 
        c.ComplianceId,
        c.ClientId,
        cl.CompanyName AS ClientName,
        c.ComplianceType,
        c.SubType,
        c.Description,
        c.DueDate,
        c.Status,
        c.Priority,
        c.AssignedTo,
        ISNULL(u.FirstName + ' ' + u.LastName, 'Unassigned') AS AssignedToName,
        c.CreatedAt,
        c.Notes,
        c.EstimatedHours
    FROM Compliance c
    INNER JOIN Clients cl ON c.ClientId = cl.ClientId
    LEFT JOIN Users u ON c.AssignedTo = u.UserId
    WHERE c.ComplianceId = @ComplianceId
    
    FOR JSON PATH, WITHOUT_ARRAY_WRAPPER
END

PRINT '✅ sp_CreateComplianceItem created';

-- =============================================
-- 5. sp_UpdateComplianceStatus - Update status
-- =============================================
CREATE PROCEDURE sp_UpdateComplianceStatus
    @Payload NVARCHAR(MAX)
AS
BEGIN
    SET NOCOUNT ON;
    
    DECLARE @ComplianceId UNIQUEIDENTIFIER
    DECLARE @Status NVARCHAR(50)
    DECLARE @UpdatedBy UNIQUEIDENTIFIER
    DECLARE @Notes NVARCHAR(MAX)
    
    SELECT 
        @ComplianceId = JSON_VALUE(@Payload, '$.ComplianceId'),
        @Status = JSON_VALUE(@Payload, '$.Status'),
        @UpdatedBy = JSON_VALUE(@Payload, '$.UpdatedBy'),
        @Notes = JSON_VALUE(@Payload, '$.Notes')
    
    UPDATE Compliance
    SET 
        Status = @Status,
        UpdatedBy = @UpdatedBy,
        UpdatedAt = GETUTCDATE(),
        CompletedAt = CASE WHEN @Status = 'Completed' THEN GETUTCDATE() ELSE CompletedAt END,
        CompletedBy = CASE WHEN @Status = 'Completed' THEN @UpdatedBy ELSE CompletedBy END,
        Notes = CASE 
            WHEN @Notes IS NOT NULL THEN 
                CASE WHEN Notes IS NULL OR Notes = '' THEN @Notes
                     ELSE Notes + CHAR(13) + CHAR(10) + 'Status Update: ' + @Notes
                END
            ELSE Notes
        END
    WHERE ComplianceId = @ComplianceId AND IsActive = 1
    
    SELECT @@ROWCOUNT AS RowsAffected
END

PRINT '✅ sp_UpdateComplianceStatus created';

PRINT '';
PRINT '============================================================';
PRINT 'All Stored Procedures Created Successfully!';
PRINT '============================================================';
