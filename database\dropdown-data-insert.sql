-- =====================================================
-- Dropdown Tables Data Insert Script
-- =====================================================
-- This script inserts all dropdown options identified from the React codebase

-- =====================================================
-- 1. Insert User Roles
-- =====================================================
INSERT INTO UserRoles (Name, DisplayName, Description, SortOrder)
VALUES 
('admin', 'Admin', 'Full system access and management capabilities', 1),
('senior-manager', 'Senior Manager', 'Senior level management with advanced permissions', 2),
('manager', 'Manager', 'Management level with moderate permissions', 3),
('junior-associate', 'Junior Associate', 'Junior level staff with basic permissions', 4),
('intern', 'Intern', 'Intern level with limited permissions', 5);

-- =====================================================
-- 2. Insert Task Statuses
-- =====================================================
INSERT INTO TaskStatuses (Name, DisplayName, ColorCode, SortOrder)
VALUES 
('pending', 'Pending', '#f59e0b', 1),
('in-progress', 'In Progress', '#3b82f6', 2),
('completed', 'Completed', '#10b981', 3),
('cancelled', 'Cancelled', '#ef4444', 4),
('on-hold', 'On Hold', '#6b7280', 5);

-- =====================================================
-- 3. Insert Task Priorities
-- =====================================================
INSERT INTO TaskPriorities (Name, DisplayName, ColorCode, SortOrder)
VALUES 
('low', 'Low', '#10b981', 1),
('medium', 'Medium', '#f59e0b', 2),
('high', 'High', '#ef4444', 3),
('critical', 'Critical', '#dc2626', 4);

-- =====================================================
-- 4. Insert Compliance Types
-- =====================================================
INSERT INTO ComplianceTypes (Name, DisplayName, Description, Category, SortOrder)
VALUES 
('gst', 'GST', 'Goods and Services Tax compliance', 'Tax', 1),
('tds', 'TDS', 'Tax Deducted at Source compliance', 'Tax', 2),
('roc', 'ROC', 'Registrar of Companies compliance', 'Regulatory', 3),
('itr', 'ITR', 'Income Tax Return filing', 'Tax', 4),
('audit', 'Audit', 'Audit Report and compliance', 'Audit', 5),
('advance-tax', 'Advance Tax', 'Advance tax payment compliance', 'Tax', 6),
('esi', 'ESI', 'Employee State Insurance compliance', 'Regulatory', 7),
('pf', 'PF', 'Provident Fund compliance', 'Regulatory', 8),
('tcs', 'TCS', 'Tax Collected at Source compliance', 'Tax', 9);

-- =====================================================
-- 5. Insert Compliance Sub-Types
-- =====================================================
INSERT INTO ComplianceSubTypes (Name, DisplayName, Description, SortOrder)
VALUES 
('gstr-1', 'GSTR-1', 'Outward Supplies return', 1),
('gstr-3b', 'GSTR-3B', 'Monthly return and payment', 2),
('gstr-9', 'GSTR-9', 'Annual return', 3),
('tds-quarterly', 'TDS Quarterly', 'Quarterly TDS return', 4),
('tds-annual', 'TDS Annual', 'Annual TDS return', 5),
('itr-1', 'ITR-1', 'Individual Income Tax Return', 6),
('itr-4', 'ITR-4', 'Presumptive Income Tax Return', 7),
('itr-5', 'ITR-5', 'Partnership/LLP Income Tax Return', 8),
('itr-6', 'ITR-6', 'Company Income Tax Return', 9);

-- =====================================================
-- 6. Insert Client Types
-- =====================================================
INSERT INTO ClientTypes (Name, DisplayName, Description, SortOrder)
VALUES 
('individual', 'Individual', 'Individual taxpayer or person', 1),
('company', 'Company', 'Private or Public Limited Company', 2),
('partnership', 'Partnership', 'Partnership firm', 3),
('llp', 'LLP', 'Limited Liability Partnership', 4),
('trust', 'Trust', 'Trust or Foundation', 5),
('huf', 'HUF', 'Hindu Undivided Family', 6),
('proprietorship', 'Proprietorship', 'Sole Proprietorship', 7);

-- =====================================================
-- 7. Insert Client Groups
-- =====================================================
INSERT INTO ClientGroups (Name, DisplayName, Description, SortOrder)
VALUES 
('all', 'All Clients', 'All clients in the system', 1),
('corporate', 'Corporate Clients', 'Corporate and company clients', 2),
('individual', 'Individual Clients', 'Individual taxpayers', 3),
('msme', 'MSME Clients', 'Micro, Small and Medium Enterprise clients', 4),
('high-value', 'High Value Clients', 'High value or premium clients', 5),
('startup', 'Startup Clients', 'Startup companies and entrepreneurs', 6);

-- =====================================================
-- 8. Insert Document Types
-- =====================================================
INSERT INTO DocumentTypes (Name, DisplayName, Category, FileExtensions, SortOrder)
VALUES 
('form16', 'Form 16', 'Tax', '.pdf,.jpg,.png', 1),
('gstr', 'GST Return', 'Tax', '.pdf,.xlsx,.xls', 2),
('tds-cert', 'TDS Certificate', 'Tax', '.pdf,.jpg,.png', 3),
('bank-statement', 'Bank Statement', 'Financial', '.pdf,.xlsx,.xls', 4),
('pan-card', 'PAN Card', 'Identity', '.pdf,.jpg,.png', 5),
('audit-report', 'Audit Report', 'Audit', '.pdf,.docx,.doc', 6),
('itr', 'ITR', 'Tax', '.pdf,.xlsx,.xls', 7),
('balance-sheet', 'Balance Sheet', 'Financial', '.pdf,.xlsx,.xls', 8),
('p-l-statement', 'P&L Statement', 'Financial', '.pdf,.xlsx,.xls', 9),
('invoice', 'Invoice', 'Financial', '.pdf,.xlsx,.xls', 10),
('receipt', 'Receipt', 'Financial', '.pdf,.jpg,.png', 11),
('agreement', 'Agreement', 'Legal', '.pdf,.docx,.doc', 12),
('other', 'Other', 'Miscellaneous', '.pdf,.docx,.doc,.xlsx,.xls,.jpg,.png', 13);

-- =====================================================
-- 9. Insert Document Categories
-- =====================================================
INSERT INTO DocumentCategories (Name, DisplayName, Description, SortOrder)
VALUES 
('income-tax', 'Income Tax', 'Income tax related documents', 1),
('gst', 'GST', 'GST related documents', 2),
('tds', 'TDS', 'TDS related documents', 3),
('banking', 'Banking', 'Bank statements and financial documents', 4),
('identity', 'Identity', 'Identity and KYC documents', 5),
('audit', 'Audit', 'Audit reports and related documents', 6),
('financial', 'Financial', 'Financial statements and reports', 7),
('legal', 'Legal', 'Legal documents and agreements', 8),
('compliance', 'Compliance', 'Regulatory compliance documents', 9),
('miscellaneous', 'Miscellaneous', 'Other documents', 10);

-- =====================================================
-- 10. Insert Communication Types
-- =====================================================
INSERT INTO CommunicationTypes (Name, DisplayName, Description, SortOrder)
VALUES 
('email', 'Email', 'Email communication', 1),
('whatsapp', 'WhatsApp', 'WhatsApp messaging', 2),
('sms', 'SMS', 'SMS text messaging', 3),
('phone', 'Phone', 'Phone call communication', 4),
('meeting', 'Meeting', 'In-person or virtual meeting', 5),
('both', 'Both WhatsApp & Email', 'Combined WhatsApp and Email communication', 6);

-- =====================================================
-- 11. Insert Communication Statuses
-- =====================================================
INSERT INTO CommunicationStatuses (Name, DisplayName, ColorCode, SortOrder)
VALUES 
('sent', 'Sent', '#3b82f6', 1),
('delivered', 'Delivered', '#10b981', 2),
('read', 'Read', '#059669', 3),
('pending', 'Pending', '#f59e0b', 4),
('failed', 'Failed', '#ef4444', 5),
('scheduled', 'Scheduled', '#8b5cf6', 6);

-- =====================================================
-- 12. Insert Report Types
-- =====================================================
INSERT INTO ReportTypes (Name, DisplayName, Description, Category, SortOrder)
VALUES 
('monthly-kpi', 'Monthly KPI Report', 'Monthly Key Performance Indicators', 'Financial', 1),
('quarterly-summary', 'Quarterly Summary', 'Quarterly business summary report', 'Financial', 2),
('tax-summary', 'Tax Summary Report', 'Tax compliance and payment summary', 'Tax', 3),
('financial-overview', 'Financial Overview', 'Overall financial performance report', 'Financial', 4),
('compliance-report', 'Compliance Report', 'Regulatory compliance status report', 'Compliance', 5),
('audit-report', 'Audit Report', 'Internal or external audit report', 'Audit', 6),
('client-analysis', 'Client Analysis', 'Client portfolio analysis report', 'Business', 7);

-- =====================================================
-- 13. Insert Filing Return Types
-- =====================================================
INSERT INTO FilingReturnTypes (Name, DisplayName, Description, Category, SortOrder)
VALUES 
('gstr-1', 'GSTR-1 (Outward Supplies)', 'GST return for outward supplies', 'GST', 1),
('gstr-3b', 'GSTR-3B (Monthly Return)', 'Monthly GST return and payment', 'GST', 2),
('gstr-9', 'GSTR-9 (Annual Return)', 'Annual GST return', 'GST', 3),
('tds-quarterly', 'TDS Quarterly Return', 'Quarterly TDS return filing', 'TDS', 4),
('tds-annual', 'TDS Annual Return', 'Annual TDS return filing', 'TDS', 5),
('tcs-return', 'TCS Return', 'Tax Collected at Source return', 'TCS', 6);

-- =====================================================
-- 14. Insert Reminder Types
-- =====================================================
INSERT INTO ReminderTypes (Name, DisplayName, Description, SortOrder)
VALUES 
('whatsapp', 'WhatsApp', 'WhatsApp reminder message', 1),
('email', 'Email', 'Email reminder notification', 2),
('both', 'Both WhatsApp & Email', 'Combined reminder via both channels', 3),
('sms', 'SMS', 'SMS text reminder', 4);

-- =====================================================
-- 15. Insert Staff Modules
-- =====================================================
INSERT INTO StaffModules (Name, DisplayName, Description, ModuleRoute, SortOrder)
VALUES 
('compliance-calendar', 'Compliance Calendar', 'Centralized compliance calendar management', '/compliance-calendar', 1),
('client-reminders', 'Client Reminders', 'Automated client reminder system', '/client-reminders', 2),
('report-generator', 'Report Generator', 'Financial and compliance report generation', '/report-generator', 3),
('filing-assistant', 'Filing Assistant', 'Return filing assistance and automation', '/filing-assistant', 4),
('client-history', 'Client History', 'Client interaction and filing history', '/client-history', 5),
('document-vault', 'Document Vault', 'Secure document storage and management', '/document-vault', 6),
('task-management', 'Task Management', 'Task assignment and tracking system', '/task-management', 7),
('communication-hub', 'Communication Hub', 'Client communication management', '/communication-hub', 8),
('admin-staff', 'Staff Management', 'Staff and user management (Admin only)', '/admin/staff', 9);

-- =====================================================
-- 16. Insert Staff Statuses
-- =====================================================
INSERT INTO StaffStatuses (Name, DisplayName, ColorCode, SortOrder)
VALUES 
('active', 'Active', '#10b981', 1),
('inactive', 'Inactive', '#ef4444', 2),
('suspended', 'Suspended', '#f59e0b', 3),
('on-leave', 'On Leave', '#6b7280', 4);

-- =====================================================
-- 17. Insert Client Statuses
-- =====================================================
INSERT INTO ClientStatuses (Name, DisplayName, ColorCode, SortOrder)
VALUES 
('active', 'Active', '#10b981', 1),
('inactive', 'Inactive', '#ef4444', 2),
('pending', 'Pending', '#f59e0b', 3),
('suspended', 'Suspended', '#6b7280', 4),
('prospect', 'Prospect', '#8b5cf6', 5);

PRINT '=====================================================';
PRINT 'DROPDOWN DATA INSERTED SUCCESSFULLY';
PRINT '=====================================================';
PRINT 'Summary of inserted dropdown data:';
PRINT '- User Roles: 5 entries';
PRINT '- Task Statuses: 5 entries';
PRINT '- Task Priorities: 4 entries';
PRINT '- Compliance Types: 9 entries';
PRINT '- Compliance Sub-Types: 9 entries';
PRINT '- Client Types: 7 entries';
PRINT '- Client Groups: 6 entries';
PRINT '- Document Types: 13 entries';
PRINT '- Document Categories: 10 entries';
PRINT '- Communication Types: 6 entries';
PRINT '- Communication Statuses: 6 entries';
PRINT '- Report Types: 7 entries';
PRINT '- Filing Return Types: 6 entries';
PRINT '- Reminder Types: 4 entries';
PRINT '- Staff Modules: 9 entries';
PRINT '- Staff Statuses: 4 entries';
PRINT '- Client Statuses: 5 entries';
PRINT '=====================================================';

GO
