-- =====================================================
-- Master Database Schema - TenantRegistry
-- =====================================================
-- This script creates the master database for managing all tenants

-- Create the master database
CREATE DATABASE TenantRegistry;
GO

USE TenantRegistry;
GO

-- =====================================================
-- Organizations Table (Main Tenant Registry)
-- =====================================================
CREATE TABLE Organizations (
    OrganizationId UNIQUEIDENTIFIER PRIMARY KEY DEFAULT NEWID(),
    TenantKey NVARCHAR(50) UNIQUE NOT NULL, -- URL-safe identifier (e.g., 'kumar-associates')
    OrganizationName NVARCHAR(255) NOT NULL,
    Domain NVARCHAR(255) UNIQUE NOT NULL, -- Primary domain (e.g., 'kumar-ca.com')
    DatabaseName NVARCHAR(128) NOT NULL, -- Physical database name
    ConnectionString NVARCHAR(1000) NOT NULL, -- Database connection string
    IsActive BIT DEFAULT 1,
    CreatedAt DATETIME2 DEFAULT GETUTCDATE(),
    UpdatedAt DATETIME2 DEFAULT GETUTCDATE(),
    
    -- Subscription and Limits
    SubscriptionTier NVARCHAR(50) DEFAULT 'Basic', -- Basic, Standard, Premium, Enterprise, Trial
    MaxUsers INT DEFAULT 10,
    StorageLimit BIGINT DEFAULT 5368709120, -- 5GB in bytes
    
    -- Contact Information
    ContactEmail NVARCHAR(255),
    ContactPhone NVARCHAR(20),
    ContactPerson NVARCHAR(255),
    
    -- Billing Information
    BillingAddress NVARCHAR(500),
    BillingCity NVARCHAR(100),
    BillingState NVARCHAR(100),
    BillingPinCode NVARCHAR(10),
    BillingCountry NVARCHAR(100) DEFAULT 'India',
    
    -- Subscription Details
    SubscriptionStartDate DATE,
    SubscriptionEndDate DATE,
    LastBillingDate DATE,
    NextBillingDate DATE,
    MonthlyFee DECIMAL(10,2),
    
    -- Status and Metadata
    Status NVARCHAR(50) DEFAULT 'Active', -- Active, Suspended, Cancelled, Trial
    Notes NVARCHAR(1000),
    CreatedBy NVARCHAR(255),
    UpdatedBy NVARCHAR(255)
);

-- =====================================================
-- Organization Domains Table (Multiple domains per org)
-- =====================================================
CREATE TABLE OrganizationDomains (
    DomainId UNIQUEIDENTIFIER PRIMARY KEY DEFAULT NEWID(),
    OrganizationId UNIQUEIDENTIFIER NOT NULL REFERENCES Organizations(OrganizationId) ON DELETE CASCADE,
    Domain NVARCHAR(255) NOT NULL,
    DomainType NVARCHAR(50) DEFAULT 'Custom', -- Primary, Custom, Subdomain
    IsVerified BIT DEFAULT 0,
    VerificationToken NVARCHAR(255),
    VerifiedAt DATETIME2,
    CreatedAt DATETIME2 DEFAULT GETUTCDATE(),
    
    UNIQUE(Domain)
);

-- =====================================================
-- Database Templates Table
-- =====================================================
CREATE TABLE DatabaseTemplates (
    TemplateId UNIQUEIDENTIFIER PRIMARY KEY DEFAULT NEWID(),
    TemplateName NVARCHAR(100) NOT NULL,
    Version NVARCHAR(20) NOT NULL,
    Description NVARCHAR(500),
    SchemaScript NVARCHAR(MAX) NOT NULL, -- SQL script to create database schema
    SeedDataScript NVARCHAR(MAX), -- SQL script to insert default data
    IsActive BIT DEFAULT 1,
    CreatedAt DATETIME2 DEFAULT GETUTCDATE(),
    UpdatedAt DATETIME2 DEFAULT GETUTCDATE(),
    CreatedBy NVARCHAR(255),
    
    -- Template metadata
    TargetSubscriptionTier NVARCHAR(50), -- Which subscription tiers can use this template
    Features NVARCHAR(1000), -- JSON string of features included
    MinimumVersion NVARCHAR(20) -- Minimum application version required
);

-- =====================================================
-- Tenant Usage Statistics
-- =====================================================
CREATE TABLE TenantUsageStats (
    UsageId UNIQUEIDENTIFIER PRIMARY KEY DEFAULT NEWID(),
    OrganizationId UNIQUEIDENTIFIER NOT NULL REFERENCES Organizations(OrganizationId) ON DELETE CASCADE,
    RecordDate DATE NOT NULL,
    
    -- User Statistics
    ActiveUsers INT DEFAULT 0,
    TotalLogins INT DEFAULT 0,
    
    -- Data Statistics
    TotalClients INT DEFAULT 0,
    TotalDocuments INT DEFAULT 0,
    StorageUsedBytes BIGINT DEFAULT 0,
    
    -- Activity Statistics
    ComplianceItemsCreated INT DEFAULT 0,
    TasksCompleted INT DEFAULT 0,
    CommunicationsSent INT DEFAULT 0,
    ReportsGenerated INT DEFAULT 0,
    
    -- Performance Metrics
    AvgResponseTimeMs INT DEFAULT 0,
    ErrorCount INT DEFAULT 0,
    
    CreatedAt DATETIME2 DEFAULT GETUTCDATE(),
    
    UNIQUE(OrganizationId, RecordDate)
);

-- =====================================================
-- System Configuration
-- =====================================================
CREATE TABLE SystemConfiguration (
    ConfigId UNIQUEIDENTIFIER PRIMARY KEY DEFAULT NEWID(),
    ConfigKey NVARCHAR(100) UNIQUE NOT NULL,
    ConfigValue NVARCHAR(MAX) NOT NULL,
    Description NVARCHAR(500),
    Category NVARCHAR(100), -- Database, Security, Features, etc.
    IsEncrypted BIT DEFAULT 0,
    CreatedAt DATETIME2 DEFAULT GETUTCDATE(),
    UpdatedAt DATETIME2 DEFAULT GETUTCDATE(),
    UpdatedBy NVARCHAR(255)
);

-- =====================================================
-- Audit Log for Tenant Operations
-- =====================================================
CREATE TABLE TenantAuditLog (
    AuditId UNIQUEIDENTIFIER PRIMARY KEY DEFAULT NEWID(),
    OrganizationId UNIQUEIDENTIFIER REFERENCES Organizations(OrganizationId),
    Action NVARCHAR(100) NOT NULL, -- Created, Updated, Suspended, Deleted, etc.
    EntityType NVARCHAR(100), -- Organization, Domain, User, etc.
    EntityId NVARCHAR(255),
    OldValues NVARCHAR(MAX), -- JSON of old values
    NewValues NVARCHAR(MAX), -- JSON of new values
    PerformedBy NVARCHAR(255) NOT NULL,
    PerformedAt DATETIME2 DEFAULT GETUTCDATE(),
    IPAddress NVARCHAR(45),
    UserAgent NVARCHAR(500),
    Notes NVARCHAR(1000)
);

-- =====================================================
-- Feature Flags per Tenant
-- =====================================================
CREATE TABLE TenantFeatures (
    FeatureId UNIQUEIDENTIFIER PRIMARY KEY DEFAULT NEWID(),
    OrganizationId UNIQUEIDENTIFIER NOT NULL REFERENCES Organizations(OrganizationId) ON DELETE CASCADE,
    FeatureName NVARCHAR(100) NOT NULL,
    IsEnabled BIT DEFAULT 1,
    Configuration NVARCHAR(MAX), -- JSON configuration for the feature
    EnabledAt DATETIME2 DEFAULT GETUTCDATE(),
    EnabledBy NVARCHAR(255),
    
    UNIQUE(OrganizationId, FeatureName)
);

-- =====================================================
-- Create Indexes for Performance
-- =====================================================

-- Organizations indexes
CREATE INDEX IX_Organizations_TenantKey ON Organizations(TenantKey);
CREATE INDEX IX_Organizations_Domain ON Organizations(Domain);
CREATE INDEX IX_Organizations_Status ON Organizations(Status);
CREATE INDEX IX_Organizations_SubscriptionTier ON Organizations(SubscriptionTier);
CREATE INDEX IX_Organizations_CreatedAt ON Organizations(CreatedAt);

-- Organization Domains indexes
CREATE INDEX IX_OrganizationDomains_OrganizationId ON OrganizationDomains(OrganizationId);
CREATE INDEX IX_OrganizationDomains_Domain ON OrganizationDomains(Domain);
CREATE INDEX IX_OrganizationDomains_IsVerified ON OrganizationDomains(IsVerified);

-- Database Templates indexes
CREATE INDEX IX_DatabaseTemplates_TemplateName ON DatabaseTemplates(TemplateName);
CREATE INDEX IX_DatabaseTemplates_IsActive ON DatabaseTemplates(IsActive);
CREATE INDEX IX_DatabaseTemplates_TargetSubscriptionTier ON DatabaseTemplates(TargetSubscriptionTier);

-- Usage Stats indexes
CREATE INDEX IX_TenantUsageStats_OrganizationId ON TenantUsageStats(OrganizationId);
CREATE INDEX IX_TenantUsageStats_RecordDate ON TenantUsageStats(RecordDate);

-- Audit Log indexes
CREATE INDEX IX_TenantAuditLog_OrganizationId ON TenantAuditLog(OrganizationId);
CREATE INDEX IX_TenantAuditLog_PerformedAt ON TenantAuditLog(PerformedAt);
CREATE INDEX IX_TenantAuditLog_Action ON TenantAuditLog(Action);
CREATE INDEX IX_TenantAuditLog_PerformedBy ON TenantAuditLog(PerformedBy);

-- System Configuration indexes
CREATE INDEX IX_SystemConfiguration_ConfigKey ON SystemConfiguration(ConfigKey);
CREATE INDEX IX_SystemConfiguration_Category ON SystemConfiguration(Category);

-- Tenant Features indexes
CREATE INDEX IX_TenantFeatures_OrganizationId ON TenantFeatures(OrganizationId);
CREATE INDEX IX_TenantFeatures_FeatureName ON TenantFeatures(FeatureName);

-- =====================================================
-- Insert Default System Configuration
-- =====================================================
INSERT INTO SystemConfiguration (ConfigKey, ConfigValue, Description, Category)
VALUES 
('DefaultSubscriptionTier', 'Basic', 'Default subscription tier for new tenants', 'Subscription'),
('MaxTenantsPerServer', '100', 'Maximum number of tenants per database server', 'Database'),
('DefaultStorageLimit', '5368709120', 'Default storage limit in bytes (5GB)', 'Storage'),
('DefaultMaxUsers', '10', 'Default maximum users per tenant', 'Users'),
('TrialPeriodDays', '30', 'Trial period duration in days', 'Subscription'),
('BackupRetentionDays', '30', 'Number of days to retain database backups', 'Database'),
('SessionTimeoutMinutes', '60', 'User session timeout in minutes', 'Security'),
('PasswordMinLength', '8', 'Minimum password length', 'Security'),
('EnableTwoFactorAuth', 'false', 'Enable two-factor authentication', 'Security'),
('MaintenanceMode', 'false', 'System maintenance mode flag', 'System');

-- =====================================================
-- Create Views for Common Queries
-- =====================================================

-- Active tenants with domain information
CREATE VIEW vw_ActiveTenants AS
SELECT 
    o.OrganizationId,
    o.TenantKey,
    o.OrganizationName,
    o.Domain AS PrimaryDomain,
    o.SubscriptionTier,
    o.Status,
    o.MaxUsers,
    CAST(o.StorageLimit / 1073741824.0 AS DECIMAL(10,1)) AS StorageLimitGB,
    o.CreatedAt,
    COUNT(od.DomainId) AS TotalDomains,
    SUM(CASE WHEN od.IsVerified = 1 THEN 1 ELSE 0 END) AS VerifiedDomains
FROM Organizations o
LEFT JOIN OrganizationDomains od ON o.OrganizationId = od.OrganizationId
WHERE o.IsActive = 1 AND o.Status = 'Active'
GROUP BY o.OrganizationId, o.TenantKey, o.OrganizationName, o.Domain, 
         o.SubscriptionTier, o.Status, o.MaxUsers, o.StorageLimit, o.CreatedAt;

-- Tenant usage summary
CREATE VIEW vw_TenantUsageSummary AS
SELECT 
    o.OrganizationId,
    o.TenantKey,
    o.OrganizationName,
    o.SubscriptionTier,
    ISNULL(us.ActiveUsers, 0) AS CurrentActiveUsers,
    ISNULL(us.StorageUsedBytes, 0) AS StorageUsedBytes,
    CAST(ISNULL(us.StorageUsedBytes, 0) / 1073741824.0 AS DECIMAL(10,2)) AS StorageUsedGB,
    CAST(o.StorageLimit / 1073741824.0 AS DECIMAL(10,1)) AS StorageLimitGB,
    CAST((ISNULL(us.StorageUsedBytes, 0) * 100.0) / o.StorageLimit AS DECIMAL(5,2)) AS StorageUsagePercent,
    us.RecordDate AS LastStatsDate
FROM Organizations o
LEFT JOIN TenantUsageStats us ON o.OrganizationId = us.OrganizationId 
    AND us.RecordDate = (
        SELECT MAX(RecordDate) 
        FROM TenantUsageStats 
        WHERE OrganizationId = o.OrganizationId
    )
WHERE o.IsActive = 1;

PRINT '=====================================================';
PRINT 'MASTER DATABASE SCHEMA CREATED SUCCESSFULLY';
PRINT '=====================================================';
PRINT 'Database: TenantRegistry';
PRINT 'Tables Created: 7';
PRINT 'Indexes Created: 20+';
PRINT 'Views Created: 2';
PRINT 'Default Configuration: Inserted';
PRINT '';
PRINT 'Next Steps:';
PRINT '1. Run dummy-data-insert.sql to add sample tenants';
PRINT '2. Create individual tenant databases';
PRINT '3. Configure application connection strings';
PRINT '=====================================================';

GO
