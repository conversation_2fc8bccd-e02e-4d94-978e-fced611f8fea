-- Fix sp_GetComplianceItems to support status filtering
USE [CA_Portal_kumar_associates];
GO

-- Drop if exists
IF EXISTS (SELECT * FROM sys.procedures WHERE name = 'sp_GetComplianceItems')
    DROP PROCEDURE sp_GetComplianceItems;
GO

-- <PERSON><PERSON> improved sp_GetComplianceItems
CREATE PROCEDURE sp_GetComplianceItems
    @Payload NVARCHAR(MAX)
AS
BEGIN
    SET NOCOUNT ON;
    
    DECLARE @ClientId NVARCHAR(50)
    DECLARE @PageNumber INT
    DECLARE @PageSize INT
    DECLARE @Status NVARCHAR(50)
    
    -- Parse JSON payload
    SELECT 
        @ClientId = JSON_VALUE(@Payload, '$.ClientId'),
        @PageNumber = ISNULL(JSON_VALUE(@Payload, '$.PageNumber'), 1),
        @PageSize = ISNULL(JSON_VALUE(@Payload, '$.PageSize'), 20),
        @Status = JSON_VALUE(@Payload, '$.Status')
    
    DECLARE @Offset INT = (@PageNumber - 1) * @PageSize
    
    -- Get total count
    DECLARE @TotalCount INT
    SELECT @TotalCount = COUNT(*)
    FROM Compliance c
    INNER JOIN Clients cl ON c.ClientId = cl.ClientId
    LEFT JOIN Users u ON c.AssignedTo = u.UserId
    WHERE c.IsActive = 1
        AND (@ClientId IS NULL OR @ClientId = '' OR c.ClientId = @ClientId)
        AND (@Status IS NULL OR @Status = '' OR c.Status = @Status)
    
    -- Get compliance items
    SELECT 
        c.ComplianceId,
        c.ClientId,
        cl.CompanyName AS ClientName,
        c.ComplianceType,
        c.SubType,
        c.Description,
        c.DueDate,
        c.Status,
        c.Priority,
        c.AssignedTo,
        ISNULL(u.FirstName + ' ' + u.LastName, 'Unassigned') AS AssignedToName,
        c.CreatedAt,
        c.CompletedAt,
        c.Notes,
        c.ReminderSent,
        c.LastReminderDate,
        @TotalCount AS TotalCount,
        @PageNumber AS PageNumber,
        @PageSize AS PageSize,
        CEILING(CAST(@TotalCount AS FLOAT) / @PageSize) AS TotalPages
    FROM Compliance c
    INNER JOIN Clients cl ON c.ClientId = cl.ClientId
    LEFT JOIN Users u ON c.AssignedTo = u.UserId
    WHERE c.IsActive = 1
        AND (@ClientId IS NULL OR @ClientId = '' OR c.ClientId = @ClientId)
        AND (@Status IS NULL OR @Status = '' OR c.Status = @Status)
    ORDER BY 
        CASE WHEN c.Status = 'Overdue' THEN 1
             WHEN c.Status = 'Pending' THEN 2
             WHEN c.Status = 'In Progress' THEN 3
             ELSE 4 END,
        c.DueDate ASC
    OFFSET @Offset ROWS
    FETCH NEXT @PageSize ROWS ONLY
    
    FOR JSON PATH
END
GO

PRINT '✅ sp_GetComplianceItems updated successfully!';
PRINT 'Now supports status filtering';