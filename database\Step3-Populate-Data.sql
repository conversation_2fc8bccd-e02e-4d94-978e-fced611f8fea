-- =============================================
-- Step 3: Populate Sample Data
-- =============================================

USE [KumarAssociatesDB];

-- =============================================
-- 1. Populate Users
-- =============================================
IF NOT EXISTS (SELECT 1 FROM Users)
BEGIN
    INSERT INTO [Users] (Email, FirstName, LastName, PasswordHash, Role)
    VALUES 
        ('<EMAIL>', 'Admin', 'User', 'hashed_password_admin', 'Admin'),
        ('<EMAIL>', 'Compliance', 'Manager', 'hashed_password_manager', 'Manager'),
        ('<EMAIL>', 'Senior', 'Associate', 'hashed_password_senior', 'Senior'),
        ('<EMAIL>', 'Rahul', '<PERSON>', 'hashed_password_junior1', 'Staff'),
        ('<EMAIL>', 'Pooja', 'Gupta', 'hashed_password_junior2', 'Staff'),
        ('<EMAIL>', 'Arjun', 'Patel', 'hashed_password_junior3', 'Staff'),
        ('<EMAIL>', 'Intern', 'Trainee', 'hashed_password_intern', 'Intern');
    
    PRINT 'Users populated: ' + CAST(@@ROWCOUNT AS NVARCHAR) + ' records';
END

-- =============================================
-- 2. Populate Clients
-- =============================================
IF NOT EXISTS (SELECT 1 FROM Clients)
BEGIN
    INSERT INTO [Clients] (CompanyName, ContactPerson, Email, Phone, Address)
    VALUES 
        ('ABC Manufacturing Ltd', 'Rajesh Kumar', '<EMAIL>', '+91-9876543210', 'Plot 123, Industrial Area, Gurgaon'),
        ('XYZ Services Pvt Ltd', 'Priya Sharma', '<EMAIL>', '+91-9876543211', 'Office 456, Cyber City, Bangalore'),
        ('Tech Solutions Inc', 'Amit Patel', '<EMAIL>', '+91-9876543212', 'Tower A, IT Park, Pune'),
        ('Global Exports Ltd', 'Sunita Gupta', '<EMAIL>', '+91-9876543213', 'Warehouse 789, MIDC, Mumbai'),
        ('Green Energy Corp', 'Vikram Singh', '<EMAIL>', '+91-9876543214', 'Solar Park, Sector 15, Noida'),
        ('Food Processing Co', 'Meera Joshi', '<EMAIL>', '+91-9876543215', 'Factory Road, Indore'),
        ('Textile Mills Ltd', 'Ravi Agarwal', '<EMAIL>', '+91-9876543216', 'Mill Area, Coimbatore'),
        ('Construction Corp', 'Deepak Verma', '<EMAIL>', '+91-9876543217', 'Project Site, Ghaziabad'),
        ('Pharma Industries', 'Kavita Reddy', '<EMAIL>', '+91-9876543218', 'Pharma City, Hyderabad'),
        ('Auto Components Ltd', 'Suresh Yadav', '<EMAIL>', '+91-9876543219', 'Auto Hub, Chennai');
    
    PRINT 'Clients populated: ' + CAST(@@ROWCOUNT AS NVARCHAR) + ' records';
END

-- =============================================
-- 3. Populate ComplianceTypes
-- =============================================
IF NOT EXISTS (SELECT 1 FROM ComplianceTypes)
BEGIN
    INSERT INTO [ComplianceTypes] (
        [Name], [Description], [Category], [RegulatoryBody], [DefaultDurationDays], 
        [DefaultPriority], [DefaultEstimatedHours], [SortOrder], [Color], [Icon]
    )
    VALUES 
        ('Income Tax Return', 'Annual income tax return filing', 'Tax', 'Income Tax Department', 30, 'High', 8.0, 1, '#FF6B6B', 'receipt-tax'),
        ('GST Return - GSTR1', 'Monthly GST return for outward supplies', 'GST', 'GST Council', 11, 'High', 4.0, 2, '#4ECDC4', 'file-text'),
        ('GST Return - GSTR3B', 'Monthly GST return summary', 'GST', 'GST Council', 20, 'High', 6.0, 3, '#45B7D1', 'file-check'),
        ('TDS Return', 'Tax Deducted at Source quarterly return', 'Tax', 'Income Tax Department', 30, 'Medium', 5.0, 4, '#96CEB4', 'minus-circle'),
        ('ESI Return', 'Employee State Insurance monthly return', 'Labor', 'ESIC', 21, 'Medium', 2.0, 5, '#FFEAA7', 'users'),
        ('PF Return', 'Provident Fund monthly return', 'Labor', 'EPFO', 15, 'Medium', 3.0, 6, '#DDA0DD', 'shield'),
        ('ROC Annual Filing', 'Registrar of Companies annual return', 'Corporate', 'MCA', 60, 'High', 12.0, 7, '#98D8C8', 'building'),
        ('Audit Report', 'Annual financial audit report', 'Audit', 'Various', 90, 'Critical', 40.0, 8, '#F7DC6F', 'search'),
        ('VAT Return', 'Value Added Tax return filing', 'Tax', 'State Tax Department', 20, 'Medium', 4.0, 9, '#BB8FCE', 'percent'),
        ('Professional Tax', 'Professional tax payment and return', 'Tax', 'State Government', 10, 'Low', 1.0, 10, '#85C1E9', 'credit-card'),
        ('Labor License Renewal', 'Annual labor license renewal', 'Labor', 'Labor Department', 30, 'Medium', 6.0, 11, '#F8C471', 'award'),
        ('Environmental Clearance', 'Environmental compliance certificate', 'Environmental', 'Pollution Board', 45, 'High', 15.0, 12, '#82E0AA', 'leaf'),
        ('Fire Safety Certificate', 'Annual fire safety compliance', 'Safety', 'Fire Department', 30, 'High', 8.0, 13, '#EC7063', 'shield-alt'),
        ('Trade License Renewal', 'Municipal trade license renewal', 'Legal', 'Municipal Corporation', 30, 'Medium', 4.0, 14, '#AED6F1', 'store'),
        ('FSSAI License Renewal', 'Food safety license renewal', 'Food Safety', 'FSSAI', 30, 'Medium', 5.0, 15, '#A9DFBF', 'utensils');
    
    PRINT 'ComplianceTypes populated: ' + CAST(@@ROWCOUNT AS NVARCHAR) + ' records';
END

-- =============================================
-- 4. Populate Compliance Items (50+ items)
-- =============================================
DECLARE @ClientIds TABLE (ClientId UNIQUEIDENTIFIER, CompanyName NVARCHAR(200), RowNum INT);
DECLARE @UserIds TABLE (UserId UNIQUEIDENTIFIER, FirstName NVARCHAR(100), RowNum INT);
DECLARE @TypeNames TABLE (Name NVARCHAR(100), Priority NVARCHAR(20), RowNum INT);

-- Get client IDs with row numbers
INSERT INTO @ClientIds 
SELECT ClientId, CompanyName, ROW_NUMBER() OVER (ORDER BY CompanyName)
FROM Clients;

-- Get user IDs with row numbers
INSERT INTO @UserIds 
SELECT UserId, FirstName, ROW_NUMBER() OVER (ORDER BY FirstName)
FROM Users WHERE Role IN ('Manager', 'Senior', 'Staff');

-- Get compliance types with row numbers
INSERT INTO @TypeNames 
SELECT Name, DefaultPriority, ROW_NUMBER() OVER (ORDER BY SortOrder)
FROM ComplianceTypes;

-- Clear existing compliance data
DELETE FROM ComplianceHistory;
DELETE FROM ComplianceReminders;
DELETE FROM Compliance;

-- Insert 60 compliance items (6 clients × 10 compliance types)
INSERT INTO [Compliance] (
    [ClientId], [ComplianceType], [SubType], [Description], [DueDate], [Status], [Priority], 
    [AssignedTo], [CreatedBy], [Notes], [EstimatedHours], [ComplianceYear], [RegulatoryBody], [IsRecurring]
)
SELECT 
    c.ClientId,
    t.Name,
    CASE 
        WHEN t.Name LIKE '%GST%' THEN 'Monthly Filing'
        WHEN t.Name LIKE '%Tax%' THEN 'Annual Filing'
        WHEN t.Name LIKE '%Return%' THEN 'Quarterly Filing'
        ELSE 'Standard Compliance'
    END,
    t.Name + ' for ' + c.CompanyName,
    -- Spread due dates across the year
    CASE 
        WHEN (c.RowNum + t.RowNum) % 15 = 1 THEN DATEADD(DAY, 5, GETUTCDATE())
        WHEN (c.RowNum + t.RowNum) % 15 = 2 THEN DATEADD(DAY, 15, GETUTCDATE())
        WHEN (c.RowNum + t.RowNum) % 15 = 3 THEN DATEADD(DAY, -3, GETUTCDATE()) -- Overdue
        WHEN (c.RowNum + t.RowNum) % 15 = 4 THEN DATEADD(DAY, 30, GETUTCDATE())
        WHEN (c.RowNum + t.RowNum) % 15 = 5 THEN DATEADD(DAY, 7, GETUTCDATE())
        WHEN (c.RowNum + t.RowNum) % 15 = 6 THEN DATEADD(DAY, -7, GETUTCDATE()) -- Overdue
        WHEN (c.RowNum + t.RowNum) % 15 = 7 THEN DATEADD(DAY, 45, GETUTCDATE())
        WHEN (c.RowNum + t.RowNum) % 15 = 8 THEN DATEADD(DAY, 60, GETUTCDATE())
        WHEN (c.RowNum + t.RowNum) % 15 = 9 THEN DATEADD(DAY, 21, GETUTCDATE())
        WHEN (c.RowNum + t.RowNum) % 15 = 10 THEN DATEADD(DAY, -1, GETUTCDATE()) -- Overdue
        WHEN (c.RowNum + t.RowNum) % 15 = 11 THEN DATEADD(DAY, 90, GETUTCDATE())
        WHEN (c.RowNum + t.RowNum) % 15 = 12 THEN DATEADD(DAY, 14, GETUTCDATE())
        WHEN (c.RowNum + t.RowNum) % 15 = 13 THEN DATEADD(DAY, 28, GETUTCDATE())
        WHEN (c.RowNum + t.RowNum) % 15 = 14 THEN DATEADD(DAY, -14, GETUTCDATE()) -- Overdue
        ELSE DATEADD(DAY, 35, GETUTCDATE())
    END,
    -- Status distribution
    CASE 
        WHEN (c.RowNum + t.RowNum) % 8 = 1 THEN 'Completed'
        WHEN (c.RowNum + t.RowNum) % 8 = 2 THEN 'In Progress'
        WHEN (c.RowNum + t.RowNum) % 8 = 3 THEN 'Pending'
        WHEN (c.RowNum + t.RowNum) % 8 = 4 THEN 'Pending'
        WHEN (c.RowNum + t.RowNum) % 8 = 5 THEN 'In Progress'
        WHEN (c.RowNum + t.RowNum) % 8 = 6 THEN 'Completed'
        WHEN (c.RowNum + t.RowNum) % 8 = 7 THEN 'Pending'
        ELSE 'In Progress'
    END,
    ISNULL(t.Priority, 'Medium'),
    -- Assign to different users
    (SELECT TOP 1 UserId FROM @UserIds WHERE RowNum = ((c.RowNum + t.RowNum) % 4) + 1),
    (SELECT TOP 1 UserId FROM @UserIds WHERE FirstName = 'Compliance'),
    -- Notes
    CASE 
        WHEN (c.RowNum + t.RowNum) % 5 = 1 THEN 'All documents received. Processing in progress.'
        WHEN (c.RowNum + t.RowNum) % 5 = 2 THEN 'Awaiting client documentation. Follow-up scheduled.'
        WHEN (c.RowNum + t.RowNum) % 5 = 3 THEN 'Urgent filing required. Penalty risk if delayed.'
        WHEN (c.RowNum + t.RowNum) % 5 = 4 THEN 'Standard compliance item. On track for completion.'
        ELSE 'New requirement. Client briefing completed.'
    END,
    -- Estimated hours based on compliance type
    CASE 
        WHEN t.Name LIKE '%Audit%' THEN 40.0
        WHEN t.Name LIKE '%Income Tax%' THEN 8.0
        WHEN t.Name LIKE '%GST%' THEN 4.0
        WHEN t.Name LIKE '%ROC%' THEN 12.0
        WHEN t.Name LIKE '%Environmental%' THEN 15.0
        ELSE 3.0
    END,
    YEAR(GETUTCDATE()),
    CASE 
        WHEN t.Name LIKE '%GST%' THEN 'GST Council'
        WHEN t.Name LIKE '%Income Tax%' OR t.Name LIKE '%TDS%' THEN 'Income Tax Department'
        WHEN t.Name LIKE '%ESI%' THEN 'ESIC'
        WHEN t.Name LIKE '%PF%' THEN 'EPFO'
        WHEN t.Name LIKE '%ROC%' THEN 'MCA'
        ELSE 'Various'
    END,
    CASE WHEN t.Name LIKE '%Monthly%' OR t.Name LIKE '%GST%' OR t.Name LIKE '%ESI%' OR t.Name LIKE '%PF%' THEN 1 ELSE 0 END
FROM @ClientIds c
CROSS JOIN @TypeNames t
WHERE c.RowNum <= 6 AND t.RowNum <= 10; -- 6 clients × 10 types = 60 items

PRINT 'Compliance items populated: ' + CAST(@@ROWCOUNT AS NVARCHAR) + ' records';

-- =============================================
-- 5. Update Completed Items
-- =============================================
UPDATE Compliance 
SET CompletedAt = DATEADD(DAY, -ABS(CHECKSUM(NEWID()) % 30), GETUTCDATE()),
    CompletedBy = CreatedBy,
    ActualHours = EstimatedHours * (0.8 + (ABS(CHECKSUM(NEWID()) % 40) / 100.0))
WHERE Status = 'Completed';

-- =============================================
-- 6. Update Overdue Status
-- =============================================
UPDATE Compliance 
SET Status = 'Overdue'
WHERE DueDate < GETUTCDATE() AND Status NOT IN ('Completed', 'Cancelled');

PRINT 'Status updates completed.';

-- =============================================
-- 7. Summary Report
-- =============================================
PRINT '';
PRINT '============================================================';
PRINT 'Sample Data Population Complete!';
PRINT '============================================================';

SELECT 
    'Total Compliance Items' AS [Metric],
    COUNT(*) AS [Count]
FROM Compliance
UNION ALL
SELECT 'Pending Items', COUNT(*) FROM Compliance WHERE Status = 'Pending'
UNION ALL
SELECT 'In Progress Items', COUNT(*) FROM Compliance WHERE Status = 'In Progress'
UNION ALL
SELECT 'Completed Items', COUNT(*) FROM Compliance WHERE Status = 'Completed'
UNION ALL
SELECT 'Overdue Items', COUNT(*) FROM Compliance WHERE Status = 'Overdue'
UNION ALL
SELECT 'High Priority Items', COUNT(*) FROM Compliance WHERE Priority = 'High'
UNION ALL
SELECT 'Critical Priority Items', COUNT(*) FROM Compliance WHERE Priority = 'Critical';

PRINT '';
PRINT 'Database ready for API integration!';
PRINT '============================================================';
