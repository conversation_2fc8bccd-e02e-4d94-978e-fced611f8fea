# CA Portal API - .NET Core Backend

A multi-tenant .NET Core Web API backend for the CA Portal application using ADO.NET and stored procedures.

## 🏗️ Architecture Overview

- **.NET Core 6.0** Web API
- **ADO.NET** for database operations (no Entity Framework)
- **JWT Bearer Authentication** with multi-tenant support
- **JSON-based** communication with stored procedures
- **Multi-tenant** architecture with tenant isolation
- **Swagger/OpenAPI** documentation

## 📁 Project Structure

```
backend-api/
├── Controllers/
│   ├── AuthController.cs          # Authentication endpoints
│   ├── UsersController.cs         # User management
│   └── ClientsController.cs       # Client management
├── Models/
│   ├── DTOs/                      # Data Transfer Objects
│   │   ├── AuthDtos.cs
│   │   ├── UserDto.cs
│   │   ├── TenantDto.cs
│   │   ├── ClientDto.cs
│   │   └── ComplianceDto.cs
│   └── Common/
│       └── ApiResponse.cs         # Standard API response wrapper
├── Services/
│   ├── IDbService.cs              # Database service interface
│   ├── DbService.cs               # ADO.NET database operations
│   ├── IJwtService.cs             # JWT service interface
│   ├── JwtService.cs              # JWT token management
│   ├── ITenantService.cs          # Tenant service interface
│   └── TenantService.cs           # Tenant resolution logic
├── Helpers/
│   └── TenantResolutionMiddleware.cs  # Multi-tenant middleware
├── appsettings.json               # Configuration
├── appsettings.Development.json   # Development configuration
├── Program.cs                     # Application startup
└── CAPortalAPI.csproj            # Project file
```

## 🚀 Getting Started

### Prerequisites

- .NET 6.0 SDK or later
- SQL Server (LocalDB, Express, or Full)
- Visual Studio 2022 or VS Code

### Installation

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd backend-api
   ```

2. **Restore NuGet packages**
   ```bash
   dotnet restore
   ```

3. **Update connection strings**
   
   Edit `appsettings.json` and `appsettings.Development.json`:
   ```json
   {
     "ConnectionStrings": {
       "TenantRegistryConnection": "Server=localhost;Database=TenantRegistry;Integrated Security=true;TrustServerCertificate=true;",
       "CA_Portal_kumar_associatesConnection": "Server=localhost;Database=CA_Portal_kumar_associates;Integrated Security=true;TrustServerCertificate=true;"
     }
   }
   ```

4. **Set up databases**
   
   Run the SQL scripts from the `Database-Schema` folder:
   - `master-database-schema.sql` - Creates TenantRegistry database
   - `dummy-data-insert.sql` - Inserts sample tenant data
   - `tenant-database-template.sql` - Creates tenant database schema
   - `tenant-sample-data.sql` - Inserts sample tenant data

5. **Run the application**
   ```bash
   dotnet run
   ```

6. **Access Swagger UI**
   
   Navigate to `https://localhost:7000` (or the port shown in console)

## 🔧 Configuration

### JWT Settings

```json
{
  "JwtSettings": {
    "Key": "super_secret_key_for_ca_portal_application_2024_minimum_32_characters",
    "Issuer": "CAPortalAPI",
    "Audience": "CAPortalUsers",
    "ExpiryInMinutes": 60
  }
}
```

### Database Connections

The API supports multiple tenant databases. Each tenant has its own isolated database:

- **Master Database**: `TenantRegistry` - Stores tenant metadata
- **Tenant Databases**: `CA_Portal_{tenant_key}` - Individual tenant data

## 🔐 Authentication & Authorization

### Login Flow

1. **POST** `/api/auth/login`
   ```json
   {
     "email": "<EMAIL>",
     "password": "password123",
     "tenantKey": "kumar-associates"
   }
   ```

2. **Response**
   ```json
   {
     "success": true,
     "data": {
       "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
       "refreshToken": "refresh_token_here",
       "expiresAt": "2024-01-01T12:00:00Z",
       "user": { ... },
       "tenant": { ... }
     }
   }
   ```

3. **Use token in subsequent requests**
   ```
   Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
   ```

### Multi-Tenant Resolution

The API resolves tenants using multiple strategies:

1. **JWT Token** - Extracts tenant from authenticated user's token
2. **Custom Header** - `X-Tenant-ID: kumar-associates`
3. **Subdomain** - `kumar-associates.yourapp.com`
4. **Custom Domain** - `kumar-ca.com`
5. **Query Parameter** - `?tenant=kumar-associates` (development only)

## 📊 Database Operations

### Stored Procedure Pattern

All database operations use stored procedures with JSON payloads:

```csharp
// Example: Create a client
var payload = JsonConvert.SerializeObject(new
{
    ClientCode = "CLI001",
    CompanyName = "ABC Company",
    Email = "<EMAIL>"
});

var result = await _dbService.ExecuteStoredProcedureAsync<ClientDto>(
    "sp_CreateClient", 
    payload, 
    "CA_Portal_kumar_associatesConnection"
);
```

### Required Stored Procedures

Each tenant database needs these stored procedures:

**Authentication:**
- `sp_AuthenticateUser` - Validates user credentials
- `sp_UpdateUserLastLogin` - Updates last login timestamp

**User Management:**
- `sp_GetUsers` - Gets paginated user list
- `sp_GetUserById` - Gets user by ID
- `sp_CreateUser` - Creates new user
- `sp_UpdateUser` - Updates user information
- `sp_DeleteUser` - Soft deletes user
- `sp_UpdateUserStatus` - Activates/deactivates user

**Client Management:**
- `sp_GetClients` - Gets paginated client list
- `sp_GetClientById` - Gets client by ID
- `sp_CreateClient` - Creates new client
- `sp_UpdateClient` - Updates client information
- `sp_DeleteClient` - Soft deletes client
- `sp_UpdateClientStatus` - Activates/deactivates client
- `sp_SearchClients` - Searches clients by term

**Tenant Management (Master DB):**
- `sp_GetTenantByDomain` - Resolves tenant by domain
- `sp_GetTenantByKey` - Resolves tenant by key
- `sp_GetActiveTenants` - Gets all active tenants
- `sp_CreateTenant` - Creates new tenant
- `sp_UpdateTenant` - Updates tenant information
- `sp_GetTenantUsage` - Gets tenant usage statistics

## 🌐 API Endpoints

### Authentication
- `POST /api/auth/login` - User login
- `POST /api/auth/refresh-token` - Refresh JWT token
- `POST /api/auth/change-password` - Change password
- `POST /api/auth/forgot-password` - Forgot password
- `POST /api/auth/reset-password` - Reset password
- `POST /api/auth/logout` - User logout
- `GET /api/auth/me` - Get current user info

### Users
- `GET /api/users` - Get users (paginated)
- `GET /api/users/{id}` - Get user by ID
- `POST /api/users` - Create user (Admin/Manager only)
- `PUT /api/users/{id}` - Update user (Admin/Manager only)
- `DELETE /api/users/{id}` - Delete user (Admin only)
- `POST /api/users/{id}/activate` - Activate user
- `POST /api/users/{id}/deactivate` - Deactivate user

### Clients
- `GET /api/clients` - Get clients (paginated)
- `GET /api/clients/{id}` - Get client by ID
- `POST /api/clients` - Create client
- `PUT /api/clients/{id}` - Update client
- `DELETE /api/clients/{id}` - Delete client (Admin/Manager only)
- `POST /api/clients/{id}/activate` - Activate client
- `POST /api/clients/{id}/deactivate` - Deactivate client
- `GET /api/clients/search` - Search clients

## 🧪 Testing

### Using Swagger UI

1. Start the application
2. Navigate to the Swagger UI
3. Use the "Authorize" button to add your JWT token
4. Test endpoints directly from the UI

### Using Postman/curl

```bash
# Login
curl -X POST "https://localhost:7000/api/auth/login" \
  -H "Content-Type: application/json" \
  -d '{
    "email": "<EMAIL>",
    "password": "password123",
    "tenantKey": "kumar-associates"
  }'

# Use the returned token for subsequent requests
curl -X GET "https://localhost:7000/api/users" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN_HERE" \
  -H "X-Tenant-ID: kumar-associates"
```

## 🔍 Logging

The application uses built-in .NET logging. Logs include:

- Authentication attempts
- Database operations
- Tenant resolution
- Error details
- Performance metrics

## 🚀 Deployment

### Development
```bash
dotnet run --environment Development
```

### Production
```bash
dotnet publish -c Release
dotnet run --environment Production
```

### Docker (Optional)
```dockerfile
FROM mcr.microsoft.com/dotnet/aspnet:6.0 AS base
WORKDIR /app
EXPOSE 80
EXPOSE 443

FROM mcr.microsoft.com/dotnet/sdk:6.0 AS build
WORKDIR /src
COPY ["CAPortalAPI.csproj", "."]
RUN dotnet restore "./CAPortalAPI.csproj"
COPY . .
WORKDIR "/src/."
RUN dotnet build "CAPortalAPI.csproj" -c Release -o /app/build

FROM build AS publish
RUN dotnet publish "CAPortalAPI.csproj" -c Release -o /app/publish

FROM base AS final
WORKDIR /app
COPY --from=publish /app/publish .
ENTRYPOINT ["dotnet", "CAPortalAPI.dll"]
```

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests if applicable
5. Submit a pull request

## 📝 License

This project is licensed under the MIT License.
