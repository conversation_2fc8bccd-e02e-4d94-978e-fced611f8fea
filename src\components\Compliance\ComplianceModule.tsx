import React, { useState } from 'react';
import { Tabs, Modal, message } from 'antd';
import { 
  DashboardOutlined, 
  CalendarOutlined, 
  FileTextOutlined,
  BarChartOutlined 
} from '@ant-design/icons';
import ComplianceDashboard from './ComplianceDashboard';
import ComplianceCalendar from './ComplianceCalendar';
import ComplianceForm from './ComplianceForm';
import { ComplianceItem, ComplianceCalendarItem } from '../../services/complianceService';

const { TabPane } = Tabs;

interface ComplianceModuleProps {
  defaultActiveTab?: string;
}

const ComplianceModule: React.FC<ComplianceModuleProps> = ({
  defaultActiveTab = 'dashboard'
}) => {
  const [activeTab, setActiveTab] = useState(defaultActiveTab);
  const [formModalVisible, setFormModalVisible] = useState(false);
  const [selectedCompliance, setSelectedCompliance] = useState<ComplianceItem | undefined>();
  const [formMode, setFormMode] = useState<'create' | 'edit'>('create');
  const [refreshTrigger, setRefreshTrigger] = useState(0);

  const handleCreateCompliance = () => {
    setSelectedCompliance(undefined);
    setFormMode('create');
    setFormModalVisible(true);
  };

  const handleEditCompliance = (compliance: ComplianceItem) => {
    setSelectedCompliance(compliance);
    setFormMode('edit');
    setFormModalVisible(true);
  };

  const handleViewCompliance = (compliance: ComplianceItem | ComplianceCalendarItem) => {
    // Convert ComplianceCalendarItem to ComplianceItem if needed
    const complianceItem: ComplianceItem = {
      complianceId: compliance.complianceId,
      clientId: '', // Will be populated from API
      clientName: compliance.clientName,
      complianceType: compliance.complianceType,
      subType: '',
      dueDate: compliance.dueDate,
      description: '',
      status: compliance.status,
      priority: compliance.priority,
      assignedTo: '',
      assignedToName: compliance.assignedToName,
      createdAt: '',
      completedAt: null,
      notes: '',
      reminderSent: false,
      lastReminderDate: null
    };

    handleEditCompliance(complianceItem);
  };

  const handleFormSave = (compliance: ComplianceItem) => {
    setFormModalVisible(false);
    setRefreshTrigger(prev => prev + 1);
    message.success(
      formMode === 'create' 
        ? 'Compliance item created successfully' 
        : 'Compliance item updated successfully'
    );
  };

  const handleFormCancel = () => {
    setFormModalVisible(false);
    setSelectedCompliance(undefined);
  };

  const tabItems = [
    {
      key: 'dashboard',
      label: (
        <span>
          <DashboardOutlined />
          Dashboard
        </span>
      ),
      children: (
        <ComplianceDashboard
          key={`dashboard-${refreshTrigger}`}
          onCreateCompliance={handleCreateCompliance}
          onViewCompliance={handleViewCompliance}
        />
      )
    },
    {
      key: 'calendar',
      label: (
        <span>
          <CalendarOutlined />
          Calendar
        </span>
      ),
      children: (
        <ComplianceCalendar
          key={`calendar-${refreshTrigger}`}
          onViewCompliance={handleViewCompliance}
        />
      )
    },
    {
      key: 'list',
      label: (
        <span>
          <FileTextOutlined />
          All Items
        </span>
      ),
      children: (
        <div style={{ padding: '24px', textAlign: 'center' }}>
          <h3>Compliance List View</h3>
          <p>This will show a detailed list view of all compliance items with advanced filtering and sorting options.</p>
          <p style={{ color: '#666' }}>Coming soon...</p>
        </div>
      )
    },
    {
      key: 'reports',
      label: (
        <span>
          <BarChartOutlined />
          Reports
        </span>
      ),
      children: (
        <div style={{ padding: '24px', textAlign: 'center' }}>
          <h3>Compliance Reports</h3>
          <p>This will show compliance analytics, reports, and insights.</p>
          <p style={{ color: '#666' }}>Coming soon...</p>
        </div>
      )
    }
  ];

  return (
    <div style={{ height: '100%' }}>
      <Tabs
        activeKey={activeTab}
        onChange={setActiveTab}
        items={tabItems}
        style={{ height: '100%' }}
        tabBarStyle={{ 
          margin: 0, 
          paddingLeft: '24px',
          paddingRight: '24px',
          borderBottom: '1px solid #f0f0f0'
        }}
      />

      {/* Compliance Form Modal */}
      <Modal
        title={formMode === 'create' ? 'Create Compliance Item' : 'Edit Compliance Item'}
        open={formModalVisible}
        onCancel={handleFormCancel}
        footer={null}
        width={800}
        destroyOnClose
      >
        <ComplianceForm
          compliance={selectedCompliance}
          mode={formMode}
          onSave={handleFormSave}
          onCancel={handleFormCancel}
        />
      </Modal>
    </div>
  );
};

export default ComplianceModule;
