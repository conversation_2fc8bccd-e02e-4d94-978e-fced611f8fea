// Test Create Compliance with Correct Payload
const https = require('https');
const fs = require('fs');

// Disable SSL certificate validation for localhost testing
process.env["NODE_TLS_REJECT_UNAUTHORIZED"] = 0;

// Read the correct payload
const payload = JSON.parse(fs.readFileSync('test-correct-payload.json', 'utf8'));
const postData = JSON.stringify(payload);

const options = {
  hostname: 'localhost',
  port: 7000,
  path: '/api/compliance',
  method: 'POST',
  headers: {
    'Content-Type': 'application/json',
    'Content-Length': Buffer.byteLength(postData)
  }
};

console.log('🧪 Testing Create Compliance with Correct Payload...');
console.log('📤 Sending POST request to https://localhost:7000/api/compliance');
console.log('📋 Payload:', JSON.stringify(payload, null, 2));

const req = https.request(options, (res) => {
  console.log(`📊 Status Code: ${res.statusCode}`);

  let data = '';
  res.on('data', (chunk) => {
    data += chunk;
  });

  res.on('end', () => {
    console.log('\n📥 Response:');
    try {
      const response = JSON.parse(data);
      console.log(JSON.stringify(response, null, 2));
      
      if (res.statusCode === 201 && response.success) {
        console.log('\n✅ SUCCESS! Compliance item created successfully!');
        console.log(`🆔 New Compliance ID: ${response.data.complianceId}`);
        console.log(`📝 Type: ${response.data.complianceType}`);
        console.log(`👤 Client: ${response.data.clientName}`);
        console.log(`📅 Due Date: ${response.data.dueDate}`);
        console.log(`⚡ Status: ${response.data.status}`);
        console.log(`🔥 Priority: ${response.data.priority}`);
        console.log(`👨‍💼 Assigned To: ${response.data.assignedToName}`);
      } else {
        console.log('\n❌ ERROR: API returned failure response');
        if (response.errors) {
          console.log('Validation Errors:', response.errors);
        }
      }
    } catch (error) {
      console.log('\n❌ ERROR: Failed to parse JSON response');
      console.log('Raw response:', data);
    }
  });
});

req.on('error', (error) => {
  console.error('\n❌ ERROR: Request failed');
  console.error(error.message);
});

req.write(postData);
req.end();

console.log('\n⏳ Waiting for response...');
