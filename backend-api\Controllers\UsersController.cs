using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using CAPortalAPI.Models.DTOs;
using CAPortalAPI.Models.Common;
using CAPortalAPI.Services;
using CAPortalAPI.Authorization;
using Newtonsoft.Json;

namespace CAPortalAPI.Controllers
{
    [ApiController]
    [Route("api/[controller]")]
    [Authorize]
    public class UsersController : ControllerBase
    {
        private readonly IDbService _dbService;
        private readonly ILogger<UsersController> _logger;

        public UsersController(IDbService dbService, ILogger<UsersController> logger)
        {
            _dbService = dbService;
            _logger = logger;
        }

        [HttpGet]
        [Authorize(Policy = AuthorizationPolicies.ManagerOrAdmin)]
        public async Task<IActionResult> GetUsers([FromQuery] UserListRequestDto request)
        {
            try
            {
                var tenantKey = GetCurrentTenantKey();
                if (string.IsNullOrEmpty(tenantKey))
                {
                    return BadRequest(ApiResponse.ErrorResponse("Tenant context required"));
                }

                var payload = JsonConvert.SerializeObject(request);
                var connectionName = $"CA_Portal_{tenantKey.Replace("-", "_")}Connection";

                var result = await _dbService.ExecuteStoredProcedureAsync<UserListResponseDto>(
                    "sp_GetUsers", 
                    payload, 
                    connectionName);

                if (result == null)
                {
                    return Ok(ApiResponse<UserListResponseDto>.SuccessResponse(new UserListResponseDto()));
                }

                return Ok(ApiResponse<UserListResponseDto>.SuccessResponse(result));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting users");
                return StatusCode(500, ApiResponse.ErrorResponse("An error occurred while retrieving users"));
            }
        }

        [HttpGet("{id}")]
        public async Task<IActionResult> GetUser(Guid id)
        {
            try
            {
                var tenantKey = GetCurrentTenantKey();
                if (string.IsNullOrEmpty(tenantKey))
                {
                    return BadRequest(ApiResponse.ErrorResponse("Tenant context required"));
                }

                var payload = JsonConvert.SerializeObject(new { UserId = id });
                var connectionName = $"CA_Portal_{tenantKey.Replace("-", "_")}Connection";

                var user = await _dbService.ExecuteStoredProcedureAsync<UserDto>(
                    "sp_GetUserById", 
                    payload, 
                    connectionName);

                if (user == null)
                {
                    return NotFound(ApiResponse.ErrorResponse("User not found"));
                }

                return Ok(ApiResponse<UserDto>.SuccessResponse(user));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting user {UserId}", id);
                return StatusCode(500, ApiResponse.ErrorResponse("An error occurred while retrieving user"));
            }
        }

        [HttpPost]
        [Authorize(Policy = AuthorizationPolicies.UserManagement)]
        public async Task<IActionResult> CreateUser([FromBody] CreateUserRequestDto request)
        {
            try
            {
                if (!ModelState.IsValid)
                {
                    return BadRequest(ApiResponse.ErrorResponse("Invalid request data",
                        ModelState.Values.SelectMany(v => v.Errors.Select(e => e.ErrorMessage)).ToList()));
                }

                var tenantKey = GetCurrentTenantKey();
                var currentUserId = GetCurrentUserId();
                
                if (string.IsNullOrEmpty(tenantKey) || currentUserId == null)
                {
                    return BadRequest(ApiResponse.ErrorResponse("Invalid context"));
                }

                var payload = JsonConvert.SerializeObject(new
                {
                    request.Email,
                    request.FirstName,
                    request.LastName,
                    request.Role,
                    request.Phone,
                    request.Department,
                    request.EmployeeCode,
                    request.Password,
                    CreatedBy = currentUserId,
                    TenantId = GetCurrentTenantId()
                });

                var connectionName = $"CA_Portal_{tenantKey.Replace("-", "_")}Connection";

                var result = await _dbService.ExecuteStoredProcedureAsync<UserDto>(
                    "sp_CreateUser", 
                    payload, 
                    connectionName);

                if (result == null)
                {
                    return BadRequest(ApiResponse.ErrorResponse("Failed to create user"));
                }

                _logger.LogInformation("User created successfully: {Email} by {CreatedBy}", request.Email, currentUserId);
                return CreatedAtAction(nameof(GetUser), new { id = result.UserId }, 
                    ApiResponse<UserDto>.SuccessResponse(result, "User created successfully"));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error creating user {Email}", request.Email);
                return StatusCode(500, ApiResponse.ErrorResponse("An error occurred while creating user"));
            }
        }

        [HttpPut("{id}")]
        [Authorize(Policy = AuthorizationPolicies.UserManagement)]
        public async Task<IActionResult> UpdateUser(Guid id, [FromBody] UpdateUserRequestDto request)
        {
            try
            {
                if (id != request.UserId)
                {
                    return BadRequest(ApiResponse.ErrorResponse("User ID mismatch"));
                }

                if (!ModelState.IsValid)
                {
                    return BadRequest(ApiResponse.ErrorResponse("Invalid request data",
                        ModelState.Values.SelectMany(v => v.Errors.Select(e => e.ErrorMessage)).ToList()));
                }

                var tenantKey = GetCurrentTenantKey();
                var currentUserId = GetCurrentUserId();
                
                if (string.IsNullOrEmpty(tenantKey) || currentUserId == null)
                {
                    return BadRequest(ApiResponse.ErrorResponse("Invalid context"));
                }

                var payload = JsonConvert.SerializeObject(new
                {
                    request.UserId,
                    request.Email,
                    request.FirstName,
                    request.LastName,
                    request.Role,
                    request.IsActive,
                    request.Phone,
                    request.Department,
                    request.EmployeeCode,
                    UpdatedBy = currentUserId
                });

                var connectionName = $"CA_Portal_{tenantKey.Replace("-", "_")}Connection";

                var result = await _dbService.ExecuteStoredProcedureAsync<UserDto>(
                    "sp_UpdateUser", 
                    payload, 
                    connectionName);

                if (result == null)
                {
                    return NotFound(ApiResponse.ErrorResponse("User not found"));
                }

                _logger.LogInformation("User updated successfully: {UserId} by {UpdatedBy}", id, currentUserId);
                return Ok(ApiResponse<UserDto>.SuccessResponse(result, "User updated successfully"));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating user {UserId}", id);
                return StatusCode(500, ApiResponse.ErrorResponse("An error occurred while updating user"));
            }
        }

        [HttpDelete("{id}")]
        [Authorize(Policy = AuthorizationPolicies.AdminOnly)]
        public async Task<IActionResult> DeleteUser(Guid id)
        {
            try
            {
                var tenantKey = GetCurrentTenantKey();
                var currentUserId = GetCurrentUserId();
                
                if (string.IsNullOrEmpty(tenantKey) || currentUserId == null)
                {
                    return BadRequest(ApiResponse.ErrorResponse("Invalid context"));
                }

                // Prevent self-deletion
                if (id == currentUserId)
                {
                    return BadRequest(ApiResponse.ErrorResponse("Cannot delete your own account"));
                }

                var payload = JsonConvert.SerializeObject(new
                {
                    UserId = id,
                    DeletedBy = currentUserId
                });

                var connectionName = $"CA_Portal_{tenantKey.Replace("-", "_")}Connection";

                var rowsAffected = await _dbService.ExecuteStoredProcedureNonQueryAsync(
                    "sp_DeleteUser", 
                    payload, 
                    connectionName);

                if (rowsAffected == 0)
                {
                    return NotFound(ApiResponse.ErrorResponse("User not found"));
                }

                _logger.LogInformation("User deleted successfully: {UserId} by {DeletedBy}", id, currentUserId);
                return Ok(ApiResponse.SuccessResponse("User deleted successfully"));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error deleting user {UserId}", id);
                return StatusCode(500, ApiResponse.ErrorResponse("An error occurred while deleting user"));
            }
        }

        [HttpPost("{id}/activate")]
        [Authorize(Policy = AuthorizationPolicies.UserManagement)]
        public async Task<IActionResult> ActivateUser(Guid id)
        {
            try
            {
                var tenantKey = GetCurrentTenantKey();
                var currentUserId = GetCurrentUserId();
                
                if (string.IsNullOrEmpty(tenantKey) || currentUserId == null)
                {
                    return BadRequest(ApiResponse.ErrorResponse("Invalid context"));
                }

                var payload = JsonConvert.SerializeObject(new
                {
                    UserId = id,
                    IsActive = true,
                    UpdatedBy = currentUserId
                });

                var connectionName = $"CA_Portal_{tenantKey.Replace("-", "_")}Connection";

                var rowsAffected = await _dbService.ExecuteStoredProcedureNonQueryAsync(
                    "sp_UpdateUserStatus", 
                    payload, 
                    connectionName);

                if (rowsAffected == 0)
                {
                    return NotFound(ApiResponse.ErrorResponse("User not found"));
                }

                _logger.LogInformation("User activated: {UserId} by {UpdatedBy}", id, currentUserId);
                return Ok(ApiResponse.SuccessResponse("User activated successfully"));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error activating user {UserId}", id);
                return StatusCode(500, ApiResponse.ErrorResponse("An error occurred while activating user"));
            }
        }

        [HttpPost("{id}/deactivate")]
        [Authorize(Policy = AuthorizationPolicies.UserManagement)]
        public async Task<IActionResult> DeactivateUser(Guid id)
        {
            try
            {
                var tenantKey = GetCurrentTenantKey();
                var currentUserId = GetCurrentUserId();
                
                if (string.IsNullOrEmpty(tenantKey) || currentUserId == null)
                {
                    return BadRequest(ApiResponse.ErrorResponse("Invalid context"));
                }

                // Prevent self-deactivation
                if (id == currentUserId)
                {
                    return BadRequest(ApiResponse.ErrorResponse("Cannot deactivate your own account"));
                }

                var payload = JsonConvert.SerializeObject(new
                {
                    UserId = id,
                    IsActive = false,
                    UpdatedBy = currentUserId
                });

                var connectionName = $"CA_Portal_{tenantKey.Replace("-", "_")}Connection";

                var rowsAffected = await _dbService.ExecuteStoredProcedureNonQueryAsync(
                    "sp_UpdateUserStatus", 
                    payload, 
                    connectionName);

                if (rowsAffected == 0)
                {
                    return NotFound(ApiResponse.ErrorResponse("User not found"));
                }

                _logger.LogInformation("User deactivated: {UserId} by {UpdatedBy}", id, currentUserId);
                return Ok(ApiResponse.SuccessResponse("User deactivated successfully"));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error deactivating user {UserId}", id);
                return StatusCode(500, ApiResponse.ErrorResponse("An error occurred while deactivating user"));
            }
        }

        private Guid? GetCurrentUserId()
        {
            var userIdClaim = User.FindFirst(System.Security.Claims.ClaimTypes.NameIdentifier)?.Value;
            return Guid.TryParse(userIdClaim, out var userId) ? userId : null;
        }

        private string? GetCurrentTenantKey()
        {
            return User.FindFirst("TenantKey")?.Value;
        }

        private Guid? GetCurrentTenantId()
        {
            var tenantIdClaim = User.FindFirst("TenantId")?.Value;
            return Guid.TryParse(tenantIdClaim, out var tenantId) ? tenantId : null;
        }
    }
}
