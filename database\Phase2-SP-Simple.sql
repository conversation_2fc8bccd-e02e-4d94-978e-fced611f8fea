-- =============================================
-- Phase 2: Simple Stored Procedures for API
-- =============================================

USE [KumarAssociatesDB];

-- =============================================
-- 1. Get Compliance Stats
-- =============================================
CREATE PROCEDURE sp_GetComplianceStats
    @Payload NVARCHAR(MAX)
AS
BEGIN
    SET NOCOUNT ON;
    
    SELECT 
        COUNT(*) AS TotalItems,
        SUM(CASE WHEN Status = 'Completed' THEN 1 ELSE 0 END) AS CompletedItems,
        SUM(CASE WHEN Status = 'Pending' THEN 1 ELSE 0 END) AS PendingItems,
        SUM(CASE WHEN Status = 'Overdue' THEN 1 ELSE 0 END) AS OverdueItems,
        SUM(CASE WHEN Status = 'In Progress' THEN 1 ELSE 0 END) AS InProgressItems
    FROM Compliance
    WHERE IsActive = 1
    
    FOR JSON PATH, WITHOUT_ARRAY_WRAPPER
END

PRINT 'sp_GetComplianceStats created';

-- =============================================
-- 2. Get Compliance Items
-- =============================================
CREATE PROCEDURE sp_GetComplianceItems
    @Payload NVARCHAR(MAX)
AS
BEGIN
    SET NOCOUNT ON;
    
    SELECT TOP 20
        c.ComplianceId,
        c.ClientId,
        cl.CompanyName AS ClientName,
        c.ComplianceType,
        c.SubType,
        c.Description,
        c.DueDate,
        c.Status,
        c.Priority,
        c.AssignedTo,
        ISNULL(u.FirstName + ' ' + u.LastName, 'Unassigned') AS AssignedToName,
        c.CreatedAt,
        c.CompletedAt,
        c.Notes,
        c.EstimatedHours
    FROM Compliance c
    INNER JOIN Clients cl ON c.ClientId = cl.ClientId
    LEFT JOIN Users u ON c.AssignedTo = u.UserId
    WHERE c.IsActive = 1
    ORDER BY c.DueDate ASC
    
    FOR JSON PATH
END

PRINT 'sp_GetComplianceItems created';

-- =============================================
-- 3. Get Compliance Calendar
-- =============================================
CREATE PROCEDURE sp_GetComplianceCalendar
    @Payload NVARCHAR(MAX)
AS
BEGIN
    SET NOCOUNT ON;
    
    SELECT 
        c.ComplianceId,
        c.ComplianceType AS Title,
        cl.CompanyName AS ClientName,
        c.ComplianceType,
        c.DueDate,
        c.Status,
        c.Priority,
        ISNULL(u.FirstName + ' ' + u.LastName, 'Unassigned') AS AssignedToName
    FROM Compliance c
    INNER JOIN Clients cl ON c.ClientId = cl.ClientId
    LEFT JOIN Users u ON c.AssignedTo = u.UserId
    WHERE c.IsActive = 1
        AND c.DueDate >= DATEADD(DAY, -30, GETUTCDATE())
        AND c.DueDate <= DATEADD(DAY, 90, GETUTCDATE())
    ORDER BY c.DueDate ASC
    
    FOR JSON PATH
END

PRINT 'sp_GetComplianceCalendar created';

-- =============================================
-- 4. Create Compliance Item
-- =============================================
CREATE PROCEDURE sp_CreateComplianceItem
    @Payload NVARCHAR(MAX)
AS
BEGIN
    SET NOCOUNT ON;
    
    DECLARE @ComplianceId UNIQUEIDENTIFIER = NEWID()
    DECLARE @ClientId UNIQUEIDENTIFIER
    DECLARE @ComplianceType NVARCHAR(100)
    DECLARE @Description NVARCHAR(MAX)
    DECLARE @DueDate DATETIME
    DECLARE @Priority NVARCHAR(20)
    DECLARE @AssignedTo UNIQUEIDENTIFIER
    DECLARE @Notes NVARCHAR(MAX)
    DECLARE @CreatedBy UNIQUEIDENTIFIER
    
    -- Parse JSON payload
    SELECT 
        @ClientId = JSON_VALUE(@Payload, '$.ClientId'),
        @ComplianceType = JSON_VALUE(@Payload, '$.ComplianceType'),
        @Description = JSON_VALUE(@Payload, '$.Description'),
        @DueDate = JSON_VALUE(@Payload, '$.DueDate'),
        @Priority = ISNULL(JSON_VALUE(@Payload, '$.Priority'), 'Medium'),
        @AssignedTo = JSON_VALUE(@Payload, '$.AssignedTo'),
        @Notes = JSON_VALUE(@Payload, '$.Notes'),
        @CreatedBy = JSON_VALUE(@Payload, '$.CreatedBy')
    
    -- Insert new compliance item
    INSERT INTO Compliance (
        ComplianceId, ClientId, ComplianceType, Description,
        DueDate, Priority, AssignedTo, Notes, CreatedBy, CreatedAt, ComplianceYear
    )
    VALUES (
        @ComplianceId, @ClientId, @ComplianceType, @Description,
        @DueDate, @Priority, @AssignedTo, @Notes, @CreatedBy, GETUTCDATE(), YEAR(GETUTCDATE())
    )
    
    -- Return the created item
    SELECT 
        c.ComplianceId,
        c.ClientId,
        cl.CompanyName AS ClientName,
        c.ComplianceType,
        c.Description,
        c.DueDate,
        c.Status,
        c.Priority,
        ISNULL(u.FirstName + ' ' + u.LastName, 'Unassigned') AS AssignedToName,
        c.CreatedAt,
        c.Notes
    FROM Compliance c
    INNER JOIN Clients cl ON c.ClientId = cl.ClientId
    LEFT JOIN Users u ON c.AssignedTo = u.UserId
    WHERE c.ComplianceId = @ComplianceId
    
    FOR JSON PATH, WITHOUT_ARRAY_WRAPPER
END

PRINT 'sp_CreateComplianceItem created';

-- =============================================
-- 5. Update Compliance Status
-- =============================================
CREATE PROCEDURE sp_UpdateComplianceStatus
    @Payload NVARCHAR(MAX)
AS
BEGIN
    SET NOCOUNT ON;
    
    DECLARE @ComplianceId UNIQUEIDENTIFIER
    DECLARE @Status NVARCHAR(50)
    DECLARE @UpdatedBy UNIQUEIDENTIFIER
    
    SELECT 
        @ComplianceId = JSON_VALUE(@Payload, '$.ComplianceId'),
        @Status = JSON_VALUE(@Payload, '$.Status'),
        @UpdatedBy = JSON_VALUE(@Payload, '$.UpdatedBy')
    
    UPDATE Compliance
    SET 
        Status = @Status,
        UpdatedBy = @UpdatedBy,
        UpdatedAt = GETUTCDATE(),
        CompletedAt = CASE WHEN @Status = 'Completed' THEN GETUTCDATE() ELSE CompletedAt END,
        CompletedBy = CASE WHEN @Status = 'Completed' THEN @UpdatedBy ELSE CompletedBy END
    WHERE ComplianceId = @ComplianceId AND IsActive = 1
    
    SELECT @@ROWCOUNT AS RowsAffected
END

PRINT 'sp_UpdateComplianceStatus created';

PRINT '';
PRINT 'All stored procedures created successfully!';
PRINT 'Ready for API integration.';
