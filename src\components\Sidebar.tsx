
import { useState } from "react";
import { Calendar, Bell, FileText, Upload, Clock, FolderOpen, Users, MessageSquare, Home, Settings, Shield, LogOut } from "lucide-react";
import { useNavigate, useLocation } from "react-router-dom";
import { useAuth } from "@/contexts/AuthContext";
import { Button } from "@/components/ui/button";

const navigationItems = [
  { name: "Dashboard", icon: Home, href: "/", current: false },
  { name: "Compliance Calendar", icon: Calendar, href: "/compliance-calendar", current: false },
  { name: "Client Reminders", icon: Bell, href: "/client-reminders", current: false },
  { name: "Report Generator", icon: FileText, href: "/report-generator", current: false },
  { name: "Filing Assistant", icon: Upload, href: "/filing-assistant", current: false },
  { name: "Client History", icon: Clock, href: "/client-history", current: false },
  { name: "Document Vault", icon: FolderO<PERSON>, href: "/document-vault", current: false },
  { name: "Task Management", icon: Users, href: "/task-management", current: false },
  { name: "Communication Hub", icon: MessageSquare, href: "/communication-hub", current: false },
];

export const Sidebar = () => {
  const [navigation, setNavigation] = useState(navigationItems);
  const navigate = useNavigate();
  const location = useLocation();
  const { user, logout } = useAuth();

  const handleNavClick = (clickedIndex: number, href: string) => {
    setNavigation(navigation.map((item, index) => ({
      ...item,
      current: index === clickedIndex
    })));
    navigate(href);
  };

  const handleLogout = () => {
    logout();
    navigate("/login");
  };

  // Update current based on location
  const updatedNavigation = navigation.map(item => ({
    ...item,
    current: location.pathname === item.href
  }));

  return (
    <div className="fixed inset-y-0 left-0 z-50 w-64 bg-white shadow-lg border-r border-gray-200">
      <div className="flex flex-col h-full">
        {/* Logo */}
        <div className="flex items-center px-6 py-4 border-b border-gray-200">
          <div className="flex items-center">
            <div className="w-8 h-8 bg-blue-600 rounded-lg flex items-center justify-center">
              <Shield className="w-5 h-5 text-white" />
            </div>
            <div className="ml-3">
              <h1 className="text-lg font-bold text-gray-900">ComplianceDesk</h1>
              <p className="text-xs text-gray-500">Pro CA Portal</p>
            </div>
          </div>
        </div>

        {/* Navigation */}
        <nav className="flex-1 px-4 py-6 space-y-1">
          {updatedNavigation.map((item, index) => {
            const Icon = item.icon;
            return (
              <button
                key={item.name}
                onClick={() => handleNavClick(index, item.href)}
                className={`
                  group flex items-center w-full px-3 py-2 text-sm font-medium rounded-lg transition-colors duration-150
                  ${item.current
                    ? 'bg-blue-50 text-blue-700 border-r-2 border-blue-700'
                    : 'text-gray-600 hover:bg-gray-50 hover:text-gray-900'
                  }
                `}
              >
                <Icon className={`
                  mr-3 h-5 w-5 flex-shrink-0
                  ${item.current ? 'text-blue-500' : 'text-gray-400 group-hover:text-gray-500'}
                `} />
                {item.name}
              </button>
            );
          })}
        </nav>

        {/* Admin Section - Only visible to admin users */}
        {user?.role === "Admin" && (
          <div className="px-4 py-4 border-t border-gray-200">
            <div className="mb-2">
              <p className="text-xs font-semibold text-gray-500 uppercase tracking-wider">Admin</p>
            </div>
            <button 
              onClick={() => navigate("/admin/staff")}
              className="group flex items-center w-full px-3 py-2 text-sm font-medium text-gray-600 rounded-lg hover:bg-gray-50 hover:text-gray-900 transition-colors duration-150"
            >
              <Users className="mr-3 h-5 w-5 text-gray-400 group-hover:text-gray-500" />
              Staff Management
            </button>
          </div>
        )}

        {/* Settings */}
        <div className="px-4 py-4 border-t border-gray-200">
          <button className="group flex items-center w-full px-3 py-2 text-sm font-medium text-gray-600 rounded-lg hover:bg-gray-50 hover:text-gray-900 transition-colors duration-150">
            <Settings className="mr-3 h-5 w-5 text-gray-400 group-hover:text-gray-500" />
            Settings
          </button>
        </div>

        {/* User Profile */}
        <div className="px-4 py-4 border-t border-gray-200">
          <div className="flex items-center justify-between">
            <div className="flex items-center">
              <div className="w-8 h-8 bg-blue-600 rounded-full flex items-center justify-center">
                <span className="text-sm font-medium text-white">
                  {user?.name?.split(' ').map(n => n[0]).join('') || 'U'}
                </span>
              </div>
              <div className="ml-3">
                <p className="text-sm font-medium text-gray-900">{user?.name}</p>
                <p className="text-xs text-gray-500">{user?.role}</p>
              </div>
            </div>
            <Button
              variant="ghost"
              size="sm"
              onClick={handleLogout}
              className="text-gray-400 hover:text-gray-600"
            >
              <LogOut className="h-4 w-4" />
            </Button>
          </div>
        </div>
      </div>
    </div>
  );
};
