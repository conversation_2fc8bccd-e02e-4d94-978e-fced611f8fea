@echo off
title CA Portal API - Setup and Run
color 0A

echo.
echo ========================================
echo 🚀 CA Portal API - Setup and Run Script
echo ========================================
echo.

REM Check if .NET is installed
echo 📋 Checking Prerequisites...
echo.

dotnet --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ .NET SDK not found
    echo Please install .NET 6.0 SDK from: https://dotnet.microsoft.com/download/dotnet/6.0
    pause
    exit /b 1
) else (
    echo ✅ .NET SDK found
    dotnet --version
)

echo.

REM Check if project file exists
if not exist "CAPortalAPI.csproj" (
    echo ❌ CAPortalAPI.csproj not found in current directory
    echo Please navigate to the backend-api directory first
    pause
    exit /b 1
)

echo ✅ Project file found
echo.

REM Restore packages
echo ⚙️ Setting up the project...
echo.
echo 📦 Restoring NuGet packages...
dotnet restore
if %errorlevel% neq 0 (
    echo ❌ Failed to restore packages
    pause
    exit /b 1
)
echo ✅ Packages restored successfully
echo.

REM Build project
echo 🔨 Building the project...
dotnet build --no-restore
if %errorlevel% neq 0 (
    echo ❌ Build failed
    pause
    exit /b 1
)
echo ✅ Project built successfully
echo.

REM Check configuration
echo ⚙️ Checking configuration...
if exist "appsettings.json" (
    echo ✅ appsettings.json found
) else (
    echo ❌ appsettings.json not found
    pause
    exit /b 1
)
echo.

REM Database setup reminder
echo 🗄️ Database Setup Reminder...
echo Before running the API, ensure you have:
echo 1. ✅ Created TenantRegistry database (master-database-schema.sql)
echo 2. ✅ Inserted sample tenant data (dummy-data-insert.sql)
echo 3. ✅ Created tenant database (tenant-database-template.sql)
echo 4. ✅ Inserted sample tenant data (tenant-sample-data.sql)
echo.

set /p dbsetup="Have you completed the database setup? (y/n): "
if /i not "%dbsetup%"=="y" (
    echo Please complete the database setup first using SQL Server Management Studio
    echo Scripts are located in the 'database' folder
    pause
    exit /b 0
)

echo.

REM Trust development certificates
echo 🔒 Trusting development certificates...
dotnet dev-certs https --trust
echo.

REM Display access information
echo 🚀 Starting the application...
echo.
echo The application will be available at:
echo   🌐 Swagger UI: https://localhost:7000
echo   🌐 API Base:   https://localhost:7000/api
echo.
echo Press Ctrl+C to stop the application
echo.
echo 🎯 Quick Test Instructions:
echo 1. Open browser to https://localhost:7000
echo 2. Try the login endpoint with:
echo    Email: <EMAIL>
echo    Password: password123
echo    TenantKey: kumar-associates
echo 3. Use the returned JWT token for other endpoints
echo.

REM Run the application
dotnet run

if %errorlevel% neq 0 (
    echo.
    echo ❌ Application failed to start
    echo.
    echo 🔧 Troubleshooting tips:
    echo 1. Check if port 7000 is already in use
    echo 2. Verify database connection strings
    echo 3. Ensure SQL Server is running
    echo 4. Check firewall/antivirus settings
    echo.
    pause
    exit /b 1
)

echo.
echo 👋 Application stopped. Thank you for using CA Portal API!
pause
