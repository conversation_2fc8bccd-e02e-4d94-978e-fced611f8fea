import { apiClient } from './api';

// Compliance Types
export interface ComplianceItem {
  complianceId: string;
  clientId: string;
  clientName: string;
  complianceType: string;
  title: string;
  description: string;
  dueDate: string;
  status: string;
  priority: string;
  assignedTo: string;
  assignedToName: string;
  createdAt: string;
  createdBy: string;
  completedAt?: string;
  completedBy?: string;
  completionNotes?: string;
  isRecurring: boolean;
  recurrencePattern?: string;
  tags: string[];
  documents: ComplianceDocument[];
}

export interface ComplianceCalendarItem {
  complianceId: string;
  title: string;
  clientName: string;
  complianceType: string;
  dueDate: string;
  status: string;
  priority: string;
  assignedToName: string;
  isOverdue: boolean;
  daysUntilDue: number;
}

export interface ComplianceDeadline {
  complianceId: string;
  title: string;
  clientName: string;
  complianceType: string;
  dueDate: string;
  priority: string;
  assignedToName: string;
  daysUntilDue: number;
  isOverdue: boolean;
  urgencyLevel: string;
}

export interface ComplianceStats {
  totalItems: number;
  completedItems: number;
  pendingItems: number;
  overdueItems: number;
  upcomingItems: number;
  completionRate: number;
  statsByType: ComplianceStatsByType[];
  statsByStatus: ComplianceStatsByStatus[];
  statsByPriority: ComplianceStatsByPriority[];
}

export interface ComplianceStatsByType {
  complianceType: string;
  count: number;
  completedCount: number;
  completionRate: number;
}

export interface ComplianceStatsByStatus {
  status: string;
  count: number;
  percentage: number;
}

export interface ComplianceStatsByPriority {
  priority: string;
  count: number;
  percentage: number;
}

export interface ComplianceType {
  typeId: string;
  name: string;
  description: string;
  category: string;
  isActive: boolean;
  defaultDurationDays: number;
  defaultPriority: string;
  requiredDocuments: string[];
}

export interface ComplianceReminder {
  reminderId: string;
  complianceId: string;
  userId: string;
  reminderType: string;
  reminderDate: string;
  message: string;
  isSent: boolean;
  sentAt?: string;
  isRead: boolean;
  readAt?: string;
}

export interface ComplianceHistory {
  historyId: string;
  complianceId: string;
  action: string;
  oldValue: string;
  newValue: string;
  changedBy: string;
  changedByName: string;
  changedAt: string;
  notes: string;
}

export interface ComplianceDocument {
  documentId: string;
  fileName: string;
  fileType: string;
  fileSize: number;
  uploadedBy: string;
  uploadedAt: string;
  documentType: string;
  isRequired: boolean;
}

// Request Types
export interface CreateComplianceRequest {
  clientId: string;
  complianceType: string;
  title: string;
  description: string;
  dueDate: string;
  priority: string;
  assignedTo: string;
  isRecurring: boolean;
  recurrencePattern?: string;
  tags: string[];
}

export interface UpdateComplianceRequest {
  complianceId: string;
  title: string;
  description: string;
  dueDate: string;
  status: string;
  priority: string;
  assignedTo: string;
  isRecurring: boolean;
  recurrencePattern?: string;
  tags: string[];
}

export interface ComplianceListResponse {
  complianceItems: ComplianceItem[];
  totalCount: number;
  pageNumber: number;
  pageSize: number;
  totalPages: number;
  hasPreviousPage: boolean;
  hasNextPage: boolean;
}

export interface ComplianceFilters {
  clientId?: string;
  complianceType?: string;
  status?: string;
  priority?: string;
  assignedTo?: string;
  dueDateFrom?: string;
  dueDateTo?: string;
  tags?: string[];
  isOverdue?: boolean;
}

export interface CreateReminderRequest {
  complianceId: string;
  userId: string;
  reminderType: string;
  reminderDate: string;
  message: string;
}

export interface BulkUpdateRequest {
  complianceIds: string[];
  status?: string;
  assignedTo?: string;
  priority?: string;
  dueDate?: string;
  tags?: string[];
  updatedBy: string;
  notes: string;
}

export interface BulkUpdateResult {
  totalItems: number;
  successfulUpdates: number;
  failedUpdates: number;
  errors: string[];
  updatedIds: string[];
}

export interface ComplianceReportRequest {
  startDate: string;
  endDate: string;
  clientId?: string;
  complianceType?: string;
  status?: string;
  assignedTo?: string;
  reportType: string;
  includeDetails: boolean;
  includeStatistics: boolean;
}

export interface ComplianceReport {
  reportId: string;
  reportType: string;
  generatedAt: string;
  generatedBy: string;
  data: ComplianceReportData;
}

export interface ComplianceReportData {
  statistics: ComplianceStats;
  items: ComplianceItem[];
  upcomingDeadlines: ComplianceDeadline[];
  overdueItems: ComplianceItem[];
}

class ComplianceService {
  /**
   * Get paginated list of compliance items
   */
  async getComplianceItems(clientId?: string, pageNumber = 1, pageSize = 20): Promise<ComplianceListResponse> {
    try {
      const params = new URLSearchParams();
      if (clientId) params.append('clientId', clientId);
      params.append('pageNumber', pageNumber.toString());
      params.append('pageSize', pageSize.toString());

      const response = await apiClient.get<ComplianceListResponse>(`/compliance?${params.toString()}`);
      
      if (response.success && response.data) {
        return response.data;
      } else {
        throw new Error(response.message || 'Failed to fetch compliance items');
      }
    } catch (error: any) {
      console.error('Get compliance items error:', error);
      throw new Error(error.response?.data?.message || 'Failed to fetch compliance items');
    }
  }

  /**
   * Get compliance calendar for date range
   */
  async getComplianceCalendar(startDate: Date, endDate: Date, clientId?: string): Promise<ComplianceCalendarItem[]> {
    try {
      const params = new URLSearchParams();
      params.append('startDate', startDate.toISOString());
      params.append('endDate', endDate.toISOString());
      if (clientId) params.append('clientId', clientId);

      const response = await apiClient.get<ComplianceCalendarItem[]>(`/compliance/calendar?${params.toString()}`);
      
      if (response.success && response.data) {
        return response.data;
      } else {
        throw new Error(response.message || 'Failed to fetch compliance calendar');
      }
    } catch (error: any) {
      console.error('Get compliance calendar error:', error);
      throw new Error(error.response?.data?.message || 'Failed to fetch compliance calendar');
    }
  }

  /**
   * Get upcoming deadlines
   */
  async getUpcomingDeadlines(days = 30, clientId?: string): Promise<ComplianceDeadline[]> {
    try {
      const params = new URLSearchParams();
      params.append('days', days.toString());
      if (clientId) params.append('clientId', clientId);

      const response = await apiClient.get<ComplianceDeadline[]>(`/compliance/deadlines?${params.toString()}`);
      
      if (response.success && response.data) {
        return response.data;
      } else {
        throw new Error(response.message || 'Failed to fetch upcoming deadlines');
      }
    } catch (error: any) {
      console.error('Get upcoming deadlines error:', error);
      throw new Error(error.response?.data?.message || 'Failed to fetch upcoming deadlines');
    }
  }

  /**
   * Create new compliance item
   */
  async createComplianceItem(request: CreateComplianceRequest): Promise<ComplianceItem> {
    try {
      const response = await apiClient.post<ComplianceItem>('/compliance', request);
      
      if (response.success && response.data) {
        return response.data;
      } else {
        throw new Error(response.message || 'Failed to create compliance item');
      }
    } catch (error: any) {
      console.error('Create compliance item error:', error);
      throw new Error(error.response?.data?.message || 'Failed to create compliance item');
    }
  }

  /**
   * Get compliance item by ID
   */
  async getComplianceItemById(id: string): Promise<ComplianceItem> {
    try {
      const response = await apiClient.get<ComplianceItem>(`/compliance/${id}`);
      
      if (response.success && response.data) {
        return response.data;
      } else {
        throw new Error(response.message || 'Failed to fetch compliance item');
      }
    } catch (error: any) {
      console.error('Get compliance item by ID error:', error);
      throw new Error(error.response?.data?.message || 'Failed to fetch compliance item');
    }
  }

  /**
   * Update compliance item
   */
  async updateComplianceItem(id: string, request: UpdateComplianceRequest): Promise<ComplianceItem> {
    try {
      const response = await apiClient.put<ComplianceItem>(`/compliance/${id}`, request);
      
      if (response.success && response.data) {
        return response.data;
      } else {
        throw new Error(response.message || 'Failed to update compliance item');
      }
    } catch (error: any) {
      console.error('Update compliance item error:', error);
      throw new Error(error.response?.data?.message || 'Failed to update compliance item');
    }
  }

  /**
   * Delete compliance item
   */
  async deleteComplianceItem(id: string): Promise<void> {
    try {
      const response = await apiClient.delete(`/compliance/${id}`);
      
      if (!response.success) {
        throw new Error(response.message || 'Failed to delete compliance item');
      }
    } catch (error: any) {
      console.error('Delete compliance item error:', error);
      throw new Error(error.response?.data?.message || 'Failed to delete compliance item');
    }
  }

  /**
   * Mark compliance item as completed
   */
  async markCompleted(id: string, notes?: string): Promise<void> {
    try {
      const response = await apiClient.post(`/compliance/${id}/complete`, { notes: notes || '' });
      
      if (!response.success) {
        throw new Error(response.message || 'Failed to mark compliance as completed');
      }
    } catch (error: any) {
      console.error('Mark compliance completed error:', error);
      throw new Error(error.response?.data?.message || 'Failed to mark compliance as completed');
    }
  }

  /**
   * Get compliance statistics
   */
  async getComplianceStats(clientId?: string): Promise<ComplianceStats> {
    try {
      const params = new URLSearchParams();
      if (clientId) params.append('clientId', clientId);

      const response = await apiClient.get<ComplianceStats>(`/compliance/stats?${params.toString()}`);
      
      if (response.success && response.data) {
        return response.data;
      } else {
        throw new Error(response.message || 'Failed to fetch compliance statistics');
      }
    } catch (error: any) {
      console.error('Get compliance stats error:', error);
      throw new Error(error.response?.data?.message || 'Failed to fetch compliance statistics');
    }
  }

  /**
   * Get compliance types
   */
  async getComplianceTypes(): Promise<ComplianceType[]> {
    try {
      const response = await apiClient.get<ComplianceType[]>('/compliance/types');
      
      if (response.success && response.data) {
        return response.data;
      } else {
        throw new Error(response.message || 'Failed to fetch compliance types');
      }
    } catch (error: any) {
      console.error('Get compliance types error:', error);
      throw new Error(error.response?.data?.message || 'Failed to fetch compliance types');
    }
  }

  /**
   * Create compliance reminder
   */
  async createReminder(request: CreateReminderRequest): Promise<ComplianceReminder> {
    try {
      const response = await apiClient.post<ComplianceReminder>('/compliance/reminders', request);
      
      if (response.success && response.data) {
        return response.data;
      } else {
        throw new Error(response.message || 'Failed to create reminder');
      }
    } catch (error: any) {
      console.error('Create reminder error:', error);
      throw new Error(error.response?.data?.message || 'Failed to create reminder');
    }
  }

  /**
   * Get compliance reminders for current user
   */
  async getReminders(): Promise<ComplianceReminder[]> {
    try {
      const response = await apiClient.get<ComplianceReminder[]>('/compliance/reminders');
      
      if (response.success && response.data) {
        return response.data;
      } else {
        throw new Error(response.message || 'Failed to fetch reminders');
      }
    } catch (error: any) {
      console.error('Get reminders error:', error);
      throw new Error(error.response?.data?.message || 'Failed to fetch reminders');
    }
  }

  /**
   * Update compliance status
   */
  async updateStatus(id: string, status: string, notes?: string): Promise<void> {
    try {
      const response = await apiClient.post(`/compliance/${id}/status`, { 
        status, 
        notes: notes || '' 
      });
      
      if (!response.success) {
        throw new Error(response.message || 'Failed to update compliance status');
      }
    } catch (error: any) {
      console.error('Update compliance status error:', error);
      throw new Error(error.response?.data?.message || 'Failed to update compliance status');
    }
  }

  /**
   * Get compliance history
   */
  async getHistory(id: string): Promise<ComplianceHistory[]> {
    try {
      const response = await apiClient.get<ComplianceHistory[]>(`/compliance/${id}/history`);
      
      if (response.success && response.data) {
        return response.data;
      } else {
        throw new Error(response.message || 'Failed to fetch compliance history');
      }
    } catch (error: any) {
      console.error('Get compliance history error:', error);
      throw new Error(error.response?.data?.message || 'Failed to fetch compliance history');
    }
  }

  /**
   * Bulk update compliance items
   */
  async bulkUpdate(request: BulkUpdateRequest): Promise<BulkUpdateResult> {
    try {
      const response = await apiClient.post<BulkUpdateResult>('/compliance/bulk-update', request);
      
      if (response.success && response.data) {
        return response.data;
      } else {
        throw new Error(response.message || 'Failed to bulk update compliance items');
      }
    } catch (error: any) {
      console.error('Bulk update compliance error:', error);
      throw new Error(error.response?.data?.message || 'Failed to bulk update compliance items');
    }
  }

  /**
   * Generate compliance report
   */
  async generateReport(request: ComplianceReportRequest): Promise<ComplianceReport> {
    try {
      const response = await apiClient.post<ComplianceReport>('/compliance/reports', request);
      
      if (response.success && response.data) {
        return response.data;
      } else {
        throw new Error(response.message || 'Failed to generate compliance report');
      }
    } catch (error: any) {
      console.error('Generate compliance report error:', error);
      throw new Error(error.response?.data?.message || 'Failed to generate compliance report');
    }
  }

  /**
   * Search compliance items
   */
  async searchComplianceItems(searchTerm: string, filters?: ComplianceFilters): Promise<ComplianceItem[]> {
    try {
      const params = new URLSearchParams();
      params.append('searchTerm', searchTerm);
      
      if (filters?.clientId) params.append('clientId', filters.clientId);
      if (filters?.complianceType) params.append('complianceType', filters.complianceType);
      if (filters?.status) params.append('status', filters.status);

      const response = await apiClient.get<ComplianceItem[]>(`/compliance/search?${params.toString()}`);
      
      if (response.success && response.data) {
        return response.data;
      } else {
        throw new Error(response.message || 'Failed to search compliance items');
      }
    } catch (error: any) {
      console.error('Search compliance items error:', error);
      throw new Error(error.response?.data?.message || 'Failed to search compliance items');
    }
  }

  /**
   * Get compliance priorities
   */
  getPriorities(): string[] {
    return ['Low', 'Medium', 'High', 'Critical'];
  }

  /**
   * Get compliance statuses
   */
  getStatuses(): string[] {
    return ['Pending', 'In Progress', 'Completed', 'Overdue', 'Cancelled'];
  }

  /**
   * Get reminder types
   */
  getReminderTypes(): string[] {
    return ['Email', 'SMS', 'Push Notification', 'In-App'];
  }
}

// Export singleton instance
export const complianceService = new ComplianceService();
export default complianceService;
