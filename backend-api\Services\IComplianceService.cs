using CAPortalAPI.Models.DTOs;

namespace CAPortalAPI.Services
{
    /// <summary>
    /// Interface for compliance management service
    /// </summary>
    public interface IComplianceService
    {
        /// <summary>
        /// Get compliance items for a client
        /// </summary>
        /// <param name="clientId">Client ID</param>
        /// <param name="pageNumber">Page number</param>
        /// <param name="pageSize">Page size</param>
        /// <param name="status">Optional status filter</param>
        /// <returns>Paginated compliance items</returns>
        Task<ComplianceListResponseDto> GetComplianceItemsAsync(string clientId, int pageNumber = 1, int pageSize = 20, string? status = null);

        /// <summary>
        /// Get compliance item by ID
        /// </summary>
        /// <param name="complianceId">Compliance item ID</param>
        /// <returns>Compliance item details</returns>
        Task<ComplianceItemDto?> GetComplianceItemByIdAsync(string complianceId);

        /// <summary>
        /// Create new compliance item
        /// </summary>
        /// <param name="request">Create compliance item request</param>
        /// <returns>Created compliance item</returns>
        Task<ComplianceItemDto> CreateComplianceItemAsync(CreateComplianceItemRequestDto request);

        /// <summary>
        /// Update compliance item
        /// </summary>
        /// <param name="request">Update compliance item request</param>
        /// <returns>Updated compliance item</returns>
        Task<ComplianceItemDto> UpdateComplianceItemAsync(UpdateComplianceItemRequestDto request);

        /// <summary>
        /// Delete compliance item
        /// </summary>
        /// <param name="complianceId">Compliance item ID</param>
        /// <returns>Success status</returns>
        Task<bool> DeleteComplianceItemAsync(string complianceId);

        /// <summary>
        /// Get compliance calendar for date range
        /// </summary>
        /// <param name="startDate">Start date</param>
        /// <param name="endDate">End date</param>
        /// <param name="clientId">Optional client ID filter</param>
        /// <returns>Compliance calendar items</returns>
        Task<List<ComplianceCalendarItemDto>> GetComplianceCalendarAsync(DateTime startDate, DateTime endDate, string? clientId = null);

        // Removed GetUpcomingDeadlinesAsync - ComplianceDeadlineDto doesn't exist

        /// <summary>
        /// Mark compliance item as completed
        /// </summary>
        /// <param name="complianceId">Compliance item ID</param>
        /// <param name="completedBy">User who completed the item</param>
        /// <param name="completionNotes">Optional completion notes</param>
        /// <returns>Success status</returns>
        Task<bool> MarkComplianceCompletedAsync(string complianceId, string completedBy, string? completionNotes = null);

        /// <summary>
        /// Get compliance statistics
        /// </summary>
        /// <param name="clientId">Optional client ID filter</param>
        /// <returns>Compliance statistics</returns>
        Task<ComplianceDashboardDto> GetComplianceStatsAsync(string? clientId = null);

        // Removed methods that reference non-existent DTOs

        /// <summary>
        /// Update compliance status
        /// </summary>
        /// <param name="complianceId">Compliance item ID</param>
        /// <param name="status">New status</param>
        /// <param name="updatedBy">User updating the status</param>
        /// <param name="notes">Optional notes</param>
        /// <returns>Success status</returns>
        Task<bool> UpdateComplianceStatusAsync(string complianceId, string status, string updatedBy, string? notes = null);

        // Removed remaining methods that reference non-existent DTOs
    }
}
