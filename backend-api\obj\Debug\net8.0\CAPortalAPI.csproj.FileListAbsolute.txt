D:\DataOps Sync\Projects\CA-Portal-main\backend-api\bin\Debug\net8.0\appsettings.Development.json
D:\DataOps Sync\Projects\CA-Portal-main\backend-api\bin\Debug\net8.0\appsettings.json
D:\DataOps Sync\Projects\CA-Portal-main\backend-api\bin\Debug\net8.0\CAPortalAPI.exe
D:\DataOps Sync\Projects\CA-Portal-main\backend-api\bin\Debug\net8.0\CAPortalAPI.deps.json
D:\DataOps Sync\Projects\CA-Portal-main\backend-api\bin\Debug\net8.0\CAPortalAPI.runtimeconfig.json
D:\DataOps Sync\Projects\CA-Portal-main\backend-api\bin\Debug\net8.0\CAPortalAPI.dll
D:\DataOps Sync\Projects\CA-Portal-main\backend-api\bin\Debug\net8.0\CAPortalAPI.pdb
D:\DataOps Sync\Projects\CA-Portal-main\backend-api\bin\Debug\net8.0\Azure.Core.dll
D:\DataOps Sync\Projects\CA-Portal-main\backend-api\bin\Debug\net8.0\Azure.Identity.dll
D:\DataOps Sync\Projects\CA-Portal-main\backend-api\bin\Debug\net8.0\Microsoft.AspNetCore.Authentication.JwtBearer.dll
D:\DataOps Sync\Projects\CA-Portal-main\backend-api\bin\Debug\net8.0\Microsoft.Bcl.AsyncInterfaces.dll
D:\DataOps Sync\Projects\CA-Portal-main\backend-api\bin\Debug\net8.0\Microsoft.Data.SqlClient.dll
D:\DataOps Sync\Projects\CA-Portal-main\backend-api\bin\Debug\net8.0\Microsoft.Identity.Client.dll
D:\DataOps Sync\Projects\CA-Portal-main\backend-api\bin\Debug\net8.0\Microsoft.Identity.Client.Extensions.Msal.dll
D:\DataOps Sync\Projects\CA-Portal-main\backend-api\bin\Debug\net8.0\Microsoft.IdentityModel.Abstractions.dll
D:\DataOps Sync\Projects\CA-Portal-main\backend-api\bin\Debug\net8.0\Microsoft.IdentityModel.JsonWebTokens.dll
D:\DataOps Sync\Projects\CA-Portal-main\backend-api\bin\Debug\net8.0\Microsoft.IdentityModel.Logging.dll
D:\DataOps Sync\Projects\CA-Portal-main\backend-api\bin\Debug\net8.0\Microsoft.IdentityModel.Protocols.dll
D:\DataOps Sync\Projects\CA-Portal-main\backend-api\bin\Debug\net8.0\Microsoft.IdentityModel.Protocols.OpenIdConnect.dll
D:\DataOps Sync\Projects\CA-Portal-main\backend-api\bin\Debug\net8.0\Microsoft.IdentityModel.Tokens.dll
D:\DataOps Sync\Projects\CA-Portal-main\backend-api\bin\Debug\net8.0\Microsoft.OpenApi.dll
D:\DataOps Sync\Projects\CA-Portal-main\backend-api\bin\Debug\net8.0\Microsoft.SqlServer.Server.dll
D:\DataOps Sync\Projects\CA-Portal-main\backend-api\bin\Debug\net8.0\Newtonsoft.Json.dll
D:\DataOps Sync\Projects\CA-Portal-main\backend-api\bin\Debug\net8.0\Swashbuckle.AspNetCore.Swagger.dll
D:\DataOps Sync\Projects\CA-Portal-main\backend-api\bin\Debug\net8.0\Swashbuckle.AspNetCore.SwaggerGen.dll
D:\DataOps Sync\Projects\CA-Portal-main\backend-api\bin\Debug\net8.0\Swashbuckle.AspNetCore.SwaggerUI.dll
D:\DataOps Sync\Projects\CA-Portal-main\backend-api\bin\Debug\net8.0\System.ClientModel.dll
D:\DataOps Sync\Projects\CA-Portal-main\backend-api\bin\Debug\net8.0\System.Configuration.ConfigurationManager.dll
D:\DataOps Sync\Projects\CA-Portal-main\backend-api\bin\Debug\net8.0\System.IdentityModel.Tokens.Jwt.dll
D:\DataOps Sync\Projects\CA-Portal-main\backend-api\bin\Debug\net8.0\System.Memory.Data.dll
D:\DataOps Sync\Projects\CA-Portal-main\backend-api\bin\Debug\net8.0\System.Runtime.Caching.dll
D:\DataOps Sync\Projects\CA-Portal-main\backend-api\bin\Debug\net8.0\System.Security.Cryptography.ProtectedData.dll
D:\DataOps Sync\Projects\CA-Portal-main\backend-api\bin\Debug\net8.0\de\Microsoft.Data.SqlClient.resources.dll
D:\DataOps Sync\Projects\CA-Portal-main\backend-api\bin\Debug\net8.0\es\Microsoft.Data.SqlClient.resources.dll
D:\DataOps Sync\Projects\CA-Portal-main\backend-api\bin\Debug\net8.0\fr\Microsoft.Data.SqlClient.resources.dll
D:\DataOps Sync\Projects\CA-Portal-main\backend-api\bin\Debug\net8.0\it\Microsoft.Data.SqlClient.resources.dll
D:\DataOps Sync\Projects\CA-Portal-main\backend-api\bin\Debug\net8.0\ja\Microsoft.Data.SqlClient.resources.dll
D:\DataOps Sync\Projects\CA-Portal-main\backend-api\bin\Debug\net8.0\ko\Microsoft.Data.SqlClient.resources.dll
D:\DataOps Sync\Projects\CA-Portal-main\backend-api\bin\Debug\net8.0\pt-BR\Microsoft.Data.SqlClient.resources.dll
D:\DataOps Sync\Projects\CA-Portal-main\backend-api\bin\Debug\net8.0\ru\Microsoft.Data.SqlClient.resources.dll
D:\DataOps Sync\Projects\CA-Portal-main\backend-api\bin\Debug\net8.0\zh-Hans\Microsoft.Data.SqlClient.resources.dll
D:\DataOps Sync\Projects\CA-Portal-main\backend-api\bin\Debug\net8.0\zh-Hant\Microsoft.Data.SqlClient.resources.dll
D:\DataOps Sync\Projects\CA-Portal-main\backend-api\bin\Debug\net8.0\runtimes\unix\lib\net8.0\Microsoft.Data.SqlClient.dll
D:\DataOps Sync\Projects\CA-Portal-main\backend-api\bin\Debug\net8.0\runtimes\win\lib\net8.0\Microsoft.Data.SqlClient.dll
D:\DataOps Sync\Projects\CA-Portal-main\backend-api\bin\Debug\net8.0\runtimes\win-arm\native\Microsoft.Data.SqlClient.SNI.dll
D:\DataOps Sync\Projects\CA-Portal-main\backend-api\bin\Debug\net8.0\runtimes\win-arm64\native\Microsoft.Data.SqlClient.SNI.dll
D:\DataOps Sync\Projects\CA-Portal-main\backend-api\bin\Debug\net8.0\runtimes\win-x64\native\Microsoft.Data.SqlClient.SNI.dll
D:\DataOps Sync\Projects\CA-Portal-main\backend-api\bin\Debug\net8.0\runtimes\win-x86\native\Microsoft.Data.SqlClient.SNI.dll
D:\DataOps Sync\Projects\CA-Portal-main\backend-api\bin\Debug\net8.0\runtimes\win\lib\net8.0\System.Runtime.Caching.dll
D:\DataOps Sync\Projects\CA-Portal-main\backend-api\obj\Debug\net8.0\CAPortalAPI.csproj.AssemblyReference.cache
D:\DataOps Sync\Projects\CA-Portal-main\backend-api\obj\Debug\net8.0\CAPortalAPI.GeneratedMSBuildEditorConfig.editorconfig
D:\DataOps Sync\Projects\CA-Portal-main\backend-api\obj\Debug\net8.0\CAPortalAPI.AssemblyInfoInputs.cache
D:\DataOps Sync\Projects\CA-Portal-main\backend-api\obj\Debug\net8.0\CAPortalAPI.AssemblyInfo.cs
D:\DataOps Sync\Projects\CA-Portal-main\backend-api\obj\Debug\net8.0\CAPortalAPI.csproj.CoreCompileInputs.cache
D:\DataOps Sync\Projects\CA-Portal-main\backend-api\obj\Debug\net8.0\CAPortalAPI.MvcApplicationPartsAssemblyInfo.cs
D:\DataOps Sync\Projects\CA-Portal-main\backend-api\obj\Debug\net8.0\CAPortalAPI.MvcApplicationPartsAssemblyInfo.cache
D:\DataOps Sync\Projects\CA-Portal-main\backend-api\obj\Debug\net8.0\scopedcss\bundle\CAPortalAPI.styles.css
D:\DataOps Sync\Projects\CA-Portal-main\backend-api\obj\Debug\net8.0\staticwebassets.build.json
D:\DataOps Sync\Projects\CA-Portal-main\backend-api\obj\Debug\net8.0\staticwebassets.development.json
D:\DataOps Sync\Projects\CA-Portal-main\backend-api\obj\Debug\net8.0\staticwebassets\msbuild.CAPortalAPI.Microsoft.AspNetCore.StaticWebAssets.props
D:\DataOps Sync\Projects\CA-Portal-main\backend-api\obj\Debug\net8.0\staticwebassets\msbuild.build.CAPortalAPI.props
D:\DataOps Sync\Projects\CA-Portal-main\backend-api\obj\Debug\net8.0\staticwebassets\msbuild.buildMultiTargeting.CAPortalAPI.props
D:\DataOps Sync\Projects\CA-Portal-main\backend-api\obj\Debug\net8.0\staticwebassets\msbuild.buildTransitive.CAPortalAPI.props
D:\DataOps Sync\Projects\CA-Portal-main\backend-api\obj\Debug\net8.0\staticwebassets.pack.json
D:\DataOps Sync\Projects\CA-Portal-main\backend-api\obj\Debug\net8.0\CAPortal.5112801C.Up2Date
D:\DataOps Sync\Projects\CA-Portal-main\backend-api\obj\Debug\net8.0\CAPortalAPI.dll
D:\DataOps Sync\Projects\CA-Portal-main\backend-api\obj\Debug\net8.0\refint\CAPortalAPI.dll
D:\DataOps Sync\Projects\CA-Portal-main\backend-api\obj\Debug\net8.0\CAPortalAPI.pdb
D:\DataOps Sync\Projects\CA-Portal-main\backend-api\obj\Debug\net8.0\CAPortalAPI.genruntimeconfig.cache
D:\DataOps Sync\Projects\CA-Portal-main\backend-api\obj\Debug\net8.0\ref\CAPortalAPI.dll
