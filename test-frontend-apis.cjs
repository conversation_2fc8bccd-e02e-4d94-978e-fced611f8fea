// Test all APIs that the frontend compliance form will use
const https = require('https');

// Disable SSL certificate validation for localhost testing
process.env["NODE_TLS_REJECT_UNAUTHORIZED"] = 0;

// Helper function to make API requests
function makeRequest(options, postData = null) {
  return new Promise((resolve, reject) => {
    const req = https.request(options, (res) => {
      let data = '';
      res.on('data', (chunk) => {
        data += chunk;
      });
      res.on('end', () => {
        try {
          const response = JSON.parse(data);
          resolve({ statusCode: res.statusCode, data: response });
        } catch (error) {
          resolve({ statusCode: res.statusCode, data: data });
        }
      });
    });

    req.on('error', (error) => {
      reject(error);
    });

    if (postData) {
      req.write(postData);
    }
    req.end();
  });
}

async function testAPIs() {
  console.log('🧪 Testing Frontend APIs for Compliance Calendar...\n');

  try {
    // 1. Test Login API
    console.log('1️⃣ Testing Login API...');
    const loginPayload = JSON.stringify({
      email: "<EMAIL>",
      password: "password123",
      tenantKey: "kumar-associates"
    });

    const loginResponse = await makeRequest({
      hostname: 'localhost',
      port: 7000,
      path: '/api/auth/login',
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Content-Length': Buffer.byteLength(loginPayload)
      }
    }, loginPayload);

    if (loginResponse.statusCode === 200 && loginResponse.data.success) {
      console.log('✅ Login successful!');
      console.log(`   Token: ${loginResponse.data.data.token.substring(0, 50)}...`);
      console.log(`   User: ${loginResponse.data.data.user.firstName} ${loginResponse.data.data.user.lastName}`);
      
      const token = loginResponse.data.data.token;
      const authHeaders = {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json'
      };

      // 2. Test Clients API
      console.log('\n2️⃣ Testing Clients API...');
      const clientsResponse = await makeRequest({
        hostname: 'localhost',
        port: 7000,
        path: '/api/clients?pageSize=10&isActive=true',
        method: 'GET',
        headers: authHeaders
      });

      if (clientsResponse.statusCode === 200 && clientsResponse.data.success) {
        console.log('✅ Clients API working!');
        console.log(`   Found ${clientsResponse.data.data.clients.length} clients`);
        if (clientsResponse.data.data.clients.length > 0) {
          const client = clientsResponse.data.data.clients[0];
          console.log(`   Sample client: ${client.companyName} (${client.clientId})`);
        }
      } else {
        console.log('❌ Clients API failed:', clientsResponse.statusCode, clientsResponse.data);
      }

      // 3. Test Users API
      console.log('\n3️⃣ Testing Users API...');
      const usersResponse = await makeRequest({
        hostname: 'localhost',
        port: 7000,
        path: '/api/users?pageSize=10&isActive=true',
        method: 'GET',
        headers: authHeaders
      });

      if (usersResponse.statusCode === 200 && usersResponse.data.success) {
        console.log('✅ Users API working!');
        console.log(`   Found ${usersResponse.data.data.users.length} users`);
        if (usersResponse.data.data.users.length > 0) {
          const user = usersResponse.data.data.users[0];
          console.log(`   Sample user: ${user.firstName} ${user.lastName} (${user.userId})`);
        }
      } else {
        console.log('❌ Users API failed:', usersResponse.statusCode, usersResponse.data);
      }

      // 4. Test Compliance Stats API
      console.log('\n4️⃣ Testing Compliance Stats API...');
      const statsResponse = await makeRequest({
        hostname: 'localhost',
        port: 7000,
        path: '/api/compliance/stats',
        method: 'GET',
        headers: authHeaders
      });

      if (statsResponse.statusCode === 200 && statsResponse.data.success) {
        console.log('✅ Compliance Stats API working!');
        console.log(`   Total Items: ${statsResponse.data.data.totalItems}`);
        console.log(`   Pending: ${statsResponse.data.data.pendingItems}`);
        console.log(`   Completed: ${statsResponse.data.data.completedItems}`);
      } else {
        console.log('❌ Compliance Stats API failed:', statsResponse.statusCode, statsResponse.data);
      }

      // 5. Test Compliance Items API
      console.log('\n5️⃣ Testing Compliance Items API...');
      const itemsResponse = await makeRequest({
        hostname: 'localhost',
        port: 7000,
        path: '/api/compliance?pageSize=10',
        method: 'GET',
        headers: authHeaders
      });

      if (itemsResponse.statusCode === 200 && itemsResponse.data.success) {
        console.log('✅ Compliance Items API working!');
        console.log(`   Found ${itemsResponse.data.data.complianceItems.length} items`);
        if (itemsResponse.data.data.complianceItems.length > 0) {
          const item = itemsResponse.data.data.complianceItems[0];
          console.log(`   Sample item: ${item.complianceType} for ${item.clientName}`);
        }
      } else {
        console.log('❌ Compliance Items API failed:', itemsResponse.statusCode, itemsResponse.data);
      }

      // 6. Test Create Compliance API with correct format
      console.log('\n6️⃣ Testing Create Compliance API...');
      
      // Get first client and user for the test
      let testClientId = "EA7BE4A7-F542-41E3-BAA3-0005B145761E";
      let testUserId = "48E45269-09CB-44FC-83DF-02E3F43723EA";
      
      if (clientsResponse.data.success && clientsResponse.data.data.clients.length > 0) {
        testClientId = clientsResponse.data.data.clients[0].clientId;
      }
      
      if (usersResponse.data.success && usersResponse.data.data.users.length > 0) {
        testUserId = usersResponse.data.data.users[0].userId;
      }

      const createPayload = JSON.stringify({
        clientId: testClientId,
        complianceType: "Frontend Test",
        subType: "API Integration Test",
        description: "Testing the create compliance API from frontend test script",
        dueDate: "2025-06-20T23:59:59Z",
        priority: "Medium",
        assignedTo: testUserId,
        notes: "Created via frontend API test"
      });

      const createResponse = await makeRequest({
        hostname: 'localhost',
        port: 7000,
        path: '/api/compliance',
        method: 'POST',
        headers: {
          ...authHeaders,
          'Content-Length': Buffer.byteLength(createPayload)
        }
      }, createPayload);

      if (createResponse.statusCode === 201 && createResponse.data.success) {
        console.log('✅ Create Compliance API working!');
        console.log(`   Created item: ${createResponse.data.data.complianceType}`);
        console.log(`   ID: ${createResponse.data.data.complianceId}`);
        console.log(`   Client: ${createResponse.data.data.clientName}`);
        console.log(`   Assigned to: ${createResponse.data.data.assignedToName}`);
      } else {
        console.log('❌ Create Compliance API failed:', createResponse.statusCode, createResponse.data);
      }

      console.log('\n🎉 All API tests completed!');
      console.log('\n📋 Summary:');
      console.log('✅ Login API - Working');
      console.log(`${clientsResponse.statusCode === 200 ? '✅' : '❌'} Clients API - ${clientsResponse.statusCode === 200 ? 'Working' : 'Failed'}`);
      console.log(`${usersResponse.statusCode === 200 ? '✅' : '❌'} Users API - ${usersResponse.statusCode === 200 ? 'Working' : 'Failed'}`);
      console.log(`${statsResponse.statusCode === 200 ? '✅' : '❌'} Compliance Stats API - ${statsResponse.statusCode === 200 ? 'Working' : 'Failed'}`);
      console.log(`${itemsResponse.statusCode === 200 ? '✅' : '❌'} Compliance Items API - ${itemsResponse.statusCode === 200 ? 'Working' : 'Failed'}`);
      console.log(`${createResponse.statusCode === 201 ? '✅' : '❌'} Create Compliance API - ${createResponse.statusCode === 201 ? 'Working' : 'Failed'}`);

    } else {
      console.log('❌ Login failed:', loginResponse.statusCode, loginResponse.data);
    }

  } catch (error) {
    console.error('❌ Test failed with error:', error.message);
  }
}

testAPIs();
