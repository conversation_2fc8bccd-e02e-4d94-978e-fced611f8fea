-- Fix Authentication Stored Procedures for CA_Portal_kumar_associates
USE [CA_Portal_kumar_associates];
GO

-- Drop existing procedures
IF EXISTS (SELECT * FROM sys.procedures WHERE name = 'sp_AuthenticateUser')
    DROP PROCEDURE sp_AuthenticateUser;
GO

IF EXISTS (SELECT * FROM sys.procedures WHERE name = 'sp_UpdateUserLastLogin')
    DROP PROCEDURE sp_UpdateUserLastLogin;
GO

-- Create sp_AuthenticateUser with @Payload parameter
CREATE PROCEDURE sp_AuthenticateUser
    @Payload NVARCHAR(MAX)
AS
BEGIN
    SET NOCOUNT ON;
    
    DECLARE @Email NVARCHAR(255)
    DECLARE @Password NVARCHAR(255)
    DECLARE @TenantId UNIQUEIDENTIFIER
    
    -- Parse JSON payload
    SELECT 
        @Email = JSON_VALUE(@Payload, '$.Email'),
        @Password = JSON_VALUE(@Payload, '$.Password'),
        @TenantId = TRY_CAST(JSON_VALUE(@Payload, '$.TenantId') AS UNIQUEIDENTIFIER)
    
    -- Validate required fields
    IF @Email IS NULL OR @Email = ''
    BEGIN
        RAISERROR('Email is required', 16, 1)
        RETURN
    END
    
    IF @Password IS NULL OR @Password = ''
    BEGIN
        RAISERROR('Password is required', 16, 1)
        RETURN
    END
    
    -- Authenticate user (simplified - in production, use proper password hashing)
    SELECT
        u.UserId,
        u.FirstName,
        u.LastName,
        u.Email,
        u.Role,
        u.IsActive,
        u.CreatedAt,
        u.LastLoginAt
    FROM Users u
    WHERE u.Email = @Email
        AND u.PasswordHash = @Password  -- In production, use proper password verification
        AND u.IsActive = 1
    
    FOR JSON PATH, WITHOUT_ARRAY_WRAPPER
END
GO

-- Create sp_UpdateUserLastLogin with @Payload parameter
CREATE PROCEDURE sp_UpdateUserLastLogin
    @Payload NVARCHAR(MAX)
AS
BEGIN
    SET NOCOUNT ON;
    
    DECLARE @UserId UNIQUEIDENTIFIER
    DECLARE @LastLoginAt DATETIME
    
    -- Parse JSON payload
    SELECT 
        @UserId = TRY_CAST(JSON_VALUE(@Payload, '$.UserId') AS UNIQUEIDENTIFIER),
        @LastLoginAt = TRY_CAST(JSON_VALUE(@Payload, '$.LastLoginAt') AS DATETIME)
    
    -- Update last login time
    UPDATE Users
    SET LastLoginAt = @LastLoginAt
    WHERE UserId = @UserId AND IsActive = 1
    
    SELECT @@ROWCOUNT AS RowsAffected
END
GO

-- Create sp_GetUserById with @Payload parameter (if it doesn't exist)
IF NOT EXISTS (SELECT * FROM sys.procedures WHERE name = 'sp_GetUserById')
BEGIN
    EXEC('
    CREATE PROCEDURE sp_GetUserById
        @Payload NVARCHAR(MAX)
    AS
    BEGIN
        SET NOCOUNT ON;

        DECLARE @UserId UNIQUEIDENTIFIER

        -- Parse JSON payload
        SELECT @UserId = TRY_CAST(JSON_VALUE(@Payload, ''$.UserId'') AS UNIQUEIDENTIFIER)

        -- Get user by ID
        SELECT
            u.UserId,
            u.FirstName,
            u.LastName,
            u.Email,
            u.Role,
            u.IsActive,
            u.CreatedAt,
            u.LastLoginAt
        FROM Users u
        WHERE u.UserId = @UserId AND u.IsActive = 1

        FOR JSON PATH, WITHOUT_ARRAY_WRAPPER
    END
    ')
END

-- Create sp_ChangeUserPassword with @Payload parameter
CREATE PROCEDURE sp_ChangeUserPassword
    @Payload NVARCHAR(MAX)
AS
BEGIN
    SET NOCOUNT ON;
    
    DECLARE @UserId UNIQUEIDENTIFIER
    DECLARE @CurrentPassword NVARCHAR(255)
    DECLARE @NewPassword NVARCHAR(255)
    
    -- Parse JSON payload
    SELECT 
        @UserId = TRY_CAST(JSON_VALUE(@Payload, '$.UserId') AS UNIQUEIDENTIFIER),
        @CurrentPassword = JSON_VALUE(@Payload, '$.CurrentPassword'),
        @NewPassword = JSON_VALUE(@Payload, '$.NewPassword')
    
    -- Verify current password
    IF NOT EXISTS (
        SELECT 1 FROM Users
        WHERE UserId = @UserId
            AND PasswordHash = @CurrentPassword
            AND IsActive = 1
    )
    BEGIN
        SELECT 0 AS Result -- Current password is incorrect
        RETURN
    END

    -- Update password
    UPDATE Users
    SET PasswordHash = @NewPassword
    WHERE UserId = @UserId AND IsActive = 1
    
    SELECT 1 AS Result -- Password changed successfully
END
GO

PRINT '✅ Authentication stored procedures created successfully!';
PRINT 'All procedures now use @Payload parameter instead of @JsonPayload';
