-- =====================================================
-- Automated Tenant Database Creation Script
-- =====================================================
-- This script creates a new tenant database with schema and sample data
-- Usage: Replace parameters and execute

-- =====================================================
-- PARAMETERS - MODIFY THESE VALUES
-- =====================================================
DECLARE @TenantKey NVARCHAR(50) = 'demo-tenant'; -- Replace with actual tenant key
DECLARE @TenantId UNIQUEIDENTIFIER = NEWID(); -- Replace with actual organization ID from master DB
DECLARE @DatabaseName NVARCHAR(128) = 'CA_Portal_demo_tenant'; -- Replace with actual database name

-- =====================================================
-- Create the tenant database
-- =====================================================
DECLARE @CreateDBSQL NVARCHAR(MAX) = '
CREATE DATABASE [' + @DatabaseName + ']
ON (
    NAME = ''' + @DatabaseName + '_Data'',
    FILENAME = ''C:\Program Files\Microsoft SQL Server\MSSQL15.MSSQLSERVER\MSSQL\DATA\' + @DatabaseName + '.mdf'',
    SIZE = 100MB,
    MAXSIZE = 10GB,
    FILEGROWTH = 10MB
)
LOG ON (
    NAME = ''' + @DatabaseName + '_Log'',
    FILENAME = ''C:\Program Files\Microsoft SQL Server\MSSQL15.MSSQLSERVER\MSSQL\DATA\' + @DatabaseName + '_Log.ldf'',
    SIZE = 10MB,
    MAXSIZE = 1GB,
    FILEGROWTH = 10%
);';

PRINT 'Creating database: ' + @DatabaseName;
EXEC sp_executesql @CreateDBSQL;

-- Switch to the new database
DECLARE @UseDatabaseSQL NVARCHAR(MAX) = 'USE [' + @DatabaseName + '];';
EXEC sp_executesql @UseDatabaseSQL;

PRINT 'Database created successfully. Now creating tables...';

-- =====================================================
-- Create Tables (Execute in the context of new database)
-- =====================================================
DECLARE @CreateTablesSQL NVARCHAR(MAX) = '
USE [' + @DatabaseName + '];

-- Users table
CREATE TABLE Users (
    UserId UNIQUEIDENTIFIER PRIMARY KEY DEFAULT NEWID(),
    Email NVARCHAR(255) UNIQUE NOT NULL,
    PasswordHash NVARCHAR(255) NOT NULL,
    FirstName NVARCHAR(100) NOT NULL,
    LastName NVARCHAR(100) NOT NULL,
    Role NVARCHAR(50) NOT NULL,
    IsActive BIT DEFAULT 1,
    CreatedAt DATETIME2 DEFAULT GETUTCDATE(),
    LastLoginAt DATETIME2,
    TenantId UNIQUEIDENTIFIER NOT NULL,
    Phone NVARCHAR(20),
    Department NVARCHAR(100),
    EmployeeCode NVARCHAR(20)
);

-- Clients table
CREATE TABLE Clients (
    ClientId UNIQUEIDENTIFIER PRIMARY KEY DEFAULT NEWID(),
    ClientCode NVARCHAR(50) UNIQUE NOT NULL,
    CompanyName NVARCHAR(255) NOT NULL,
    ContactPerson NVARCHAR(255),
    Email NVARCHAR(255),
    Phone NVARCHAR(20),
    Address NVARCHAR(500),
    City NVARCHAR(100),
    State NVARCHAR(100),
    PinCode NVARCHAR(10),
    GSTNumber NVARCHAR(15),
    PANNumber NVARCHAR(10),
    ClientType NVARCHAR(50),
    BusinessType NVARCHAR(100),
    AnnualTurnover DECIMAL(18,2),
    IsActive BIT DEFAULT 1,
    CreatedAt DATETIME2 DEFAULT GETUTCDATE(),
    CreatedBy UNIQUEIDENTIFIER REFERENCES Users(UserId),
    UpdatedAt DATETIME2 DEFAULT GETUTCDATE(),
    UpdatedBy UNIQUEIDENTIFIER REFERENCES Users(UserId)
);

-- Compliance Items table
CREATE TABLE ComplianceItems (
    ComplianceId UNIQUEIDENTIFIER PRIMARY KEY DEFAULT NEWID(),
    ClientId UNIQUEIDENTIFIER REFERENCES Clients(ClientId),
    ComplianceType NVARCHAR(100) NOT NULL,
    SubType NVARCHAR(100),
    DueDate DATE NOT NULL,
    Description NVARCHAR(500),
    Status NVARCHAR(50) DEFAULT ''Pending'',
    Priority NVARCHAR(20) DEFAULT ''Medium'',
    AssignedTo UNIQUEIDENTIFIER REFERENCES Users(UserId),
    CreatedAt DATETIME2 DEFAULT GETUTCDATE(),
    CompletedAt DATETIME2,
    Notes NVARCHAR(1000),
    ReminderSent BIT DEFAULT 0,
    LastReminderDate DATETIME2
);

-- Documents table
CREATE TABLE Documents (
    DocumentId UNIQUEIDENTIFIER PRIMARY KEY DEFAULT NEWID(),
    ClientId UNIQUEIDENTIFIER REFERENCES Clients(ClientId),
    FileName NVARCHAR(255) NOT NULL,
    OriginalFileName NVARCHAR(255) NOT NULL,
    FileSize BIGINT NOT NULL,
    FileType NVARCHAR(50),
    StoragePath NVARCHAR(500) NOT NULL,
    Category NVARCHAR(100),
    SubCategory NVARCHAR(100),
    UploadedBy UNIQUEIDENTIFIER REFERENCES Users(UserId),
    UploadedAt DATETIME2 DEFAULT GETUTCDATE(),
    IsDeleted BIT DEFAULT 0,
    DeletedAt DATETIME2,
    DeletedBy UNIQUEIDENTIFIER,
    Tags NVARCHAR(500),
    Description NVARCHAR(500)
);

-- Communication Logs table
CREATE TABLE CommunicationLogs (
    LogId UNIQUEIDENTIFIER PRIMARY KEY DEFAULT NEWID(),
    ClientId UNIQUEIDENTIFIER REFERENCES Clients(ClientId),
    CommunicationType NVARCHAR(50),
    Subject NVARCHAR(255),
    Content NVARCHAR(MAX),
    SentBy UNIQUEIDENTIFIER REFERENCES Users(UserId),
    SentAt DATETIME2 DEFAULT GETUTCDATE(),
    Status NVARCHAR(50) DEFAULT ''Sent'',
    RecipientEmail NVARCHAR(255),
    RecipientPhone NVARCHAR(20),
    TemplateUsed NVARCHAR(100),
    ResponseReceived BIT DEFAULT 0,
    ResponseDate DATETIME2,
    ResponseContent NVARCHAR(MAX)
);

-- Tasks table
CREATE TABLE Tasks (
    TaskId UNIQUEIDENTIFIER PRIMARY KEY DEFAULT NEWID(),
    ClientId UNIQUEIDENTIFIER REFERENCES Clients(ClientId),
    Title NVARCHAR(255) NOT NULL,
    Description NVARCHAR(1000),
    Priority NVARCHAR(20) DEFAULT ''Medium'',
    Status NVARCHAR(50) DEFAULT ''Open'',
    AssignedTo UNIQUEIDENTIFIER REFERENCES Users(UserId),
    CreatedBy UNIQUEIDENTIFIER REFERENCES Users(UserId),
    DueDate DATETIME2,
    CreatedAt DATETIME2 DEFAULT GETUTCDATE(),
    UpdatedAt DATETIME2 DEFAULT GETUTCDATE(),
    CompletedAt DATETIME2,
    EstimatedHours DECIMAL(5,2),
    ActualHours DECIMAL(5,2),
    Category NVARCHAR(100),
    Tags NVARCHAR(500)
);

-- Reports table
CREATE TABLE Reports (
    ReportId UNIQUEIDENTIFIER PRIMARY KEY DEFAULT NEWID(),
    ClientId UNIQUEIDENTIFIER REFERENCES Clients(ClientId),
    ReportType NVARCHAR(100) NOT NULL,
    ReportName NVARCHAR(255) NOT NULL,
    GeneratedBy UNIQUEIDENTIFIER REFERENCES Users(UserId),
    GeneratedAt DATETIME2 DEFAULT GETUTCDATE(),
    ReportPeriod NVARCHAR(100),
    PeriodStart DATE,
    PeriodEnd DATE,
    FilePath NVARCHAR(500),
    Status NVARCHAR(50) DEFAULT ''Generated'',
    Notes NVARCHAR(1000)
);

-- Communication Templates table
CREATE TABLE CommunicationTemplates (
    TemplateId UNIQUEIDENTIFIER PRIMARY KEY DEFAULT NEWID(),
    TemplateName NVARCHAR(255) NOT NULL,
    TemplateType NVARCHAR(50) NOT NULL,
    Subject NVARCHAR(255),
    Content NVARCHAR(MAX) NOT NULL,
    Category NVARCHAR(100),
    IsActive BIT DEFAULT 1,
    CreatedBy UNIQUEIDENTIFIER REFERENCES Users(UserId),
    CreatedAt DATETIME2 DEFAULT GETUTCDATE(),
    UpdatedAt DATETIME2 DEFAULT GETUTCDATE(),
    UsageCount INT DEFAULT 0
);
';

EXEC sp_executesql @CreateTablesSQL;
PRINT 'Tables created successfully. Creating indexes...';

-- =====================================================
-- Create Indexes
-- =====================================================
DECLARE @CreateIndexesSQL NVARCHAR(MAX) = '
USE [' + @DatabaseName + '];

-- Users indexes
CREATE INDEX IX_Users_Email ON Users(Email);
CREATE INDEX IX_Users_TenantId ON Users(TenantId);
CREATE INDEX IX_Users_Role ON Users(Role);

-- Clients indexes
CREATE INDEX IX_Clients_ClientCode ON Clients(ClientCode);
CREATE INDEX IX_Clients_Email ON Clients(Email);
CREATE INDEX IX_Clients_GSTNumber ON Clients(GSTNumber);
CREATE INDEX IX_Clients_PANNumber ON Clients(PANNumber);
CREATE INDEX IX_Clients_CreatedBy ON Clients(CreatedBy);

-- Compliance indexes
CREATE INDEX IX_ComplianceItems_ClientId ON ComplianceItems(ClientId);
CREATE INDEX IX_ComplianceItems_DueDate ON ComplianceItems(DueDate);
CREATE INDEX IX_ComplianceItems_Status ON ComplianceItems(Status);
CREATE INDEX IX_ComplianceItems_AssignedTo ON ComplianceItems(AssignedTo);
CREATE INDEX IX_ComplianceItems_ComplianceType ON ComplianceItems(ComplianceType);

-- Documents indexes
CREATE INDEX IX_Documents_ClientId ON Documents(ClientId);
CREATE INDEX IX_Documents_Category ON Documents(Category);
CREATE INDEX IX_Documents_UploadedBy ON Documents(UploadedBy);
CREATE INDEX IX_Documents_UploadedAt ON Documents(UploadedAt);

-- Tasks indexes
CREATE INDEX IX_Tasks_ClientId ON Tasks(ClientId);
CREATE INDEX IX_Tasks_AssignedTo ON Tasks(AssignedTo);
CREATE INDEX IX_Tasks_Status ON Tasks(Status);
CREATE INDEX IX_Tasks_DueDate ON Tasks(DueDate);
CREATE INDEX IX_Tasks_CreatedBy ON Tasks(CreatedBy);

-- Communication logs indexes
CREATE INDEX IX_CommunicationLogs_ClientId ON CommunicationLogs(ClientId);
CREATE INDEX IX_CommunicationLogs_SentBy ON CommunicationLogs(SentBy);
CREATE INDEX IX_CommunicationLogs_SentAt ON CommunicationLogs(SentAt);
CREATE INDEX IX_CommunicationLogs_CommunicationType ON CommunicationLogs(CommunicationType);
';

EXEC sp_executesql @CreateIndexesSQL;
PRINT 'Indexes created successfully.';

PRINT '=====================================================';
PRINT 'TENANT DATABASE CREATION COMPLETED';
PRINT '=====================================================';
PRINT 'Database Name: ' + @DatabaseName;
PRINT 'Tenant Key: ' + @TenantKey;
PRINT 'Tenant ID: ' + CAST(@TenantId AS NVARCHAR(50));
PRINT '';
PRINT 'Next Steps:';
PRINT '1. Run tenant-sample-data.sql to insert sample data';
PRINT '2. Update TenantRegistry database with this tenant info';
PRINT '3. Configure application connection string';
PRINT '=====================================================';

GO
