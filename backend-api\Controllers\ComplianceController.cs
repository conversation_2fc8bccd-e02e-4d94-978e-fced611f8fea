using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using CAPortalAPI.Services;
using CAPortalAPI.Models.DTOs;
using CAPortalAPI.Models.Common;
using CAPortalAPI.Authorization;
using Newtonsoft.Json;

namespace CAPortalAPI.Controllers
{
    [ApiController]
    [Route("api/[controller]")]
    [Authorize]
    public class ComplianceController : ControllerBase
    {
        private readonly IComplianceService _complianceService;
        private readonly ILogger<ComplianceController> _logger;

        public ComplianceController(IComplianceService complianceService, ILogger<ComplianceController> logger)
        {
            _complianceService = complianceService;
            _logger = logger;
        }

        [HttpGet]
        [Authorize(Policy = AuthorizationPolicies.AllStaff)]
        public async Task<IActionResult> GetComplianceItems([FromQuery] string? clientId = null, [FromQuery] int pageNumber = 1, [FromQuery] int pageSize = 20)
        {
            try
            {
                var result = await _complianceService.GetComplianceItemsAsync(clientId ?? "", pageNumber, pageSize);
                return Ok(ApiResponse.SuccessResponse(result));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting compliance items");
                return StatusCode(500, ApiResponse.ErrorResponse("Failed to get compliance items"));
            }
        }

        [HttpGet("calendar")]
        [Authorize(Policy = AuthorizationPolicies.AllStaff)]
        public async Task<IActionResult> GetComplianceCalendar([FromQuery] DateTime startDate, [FromQuery] DateTime endDate, [FromQuery] string? clientId = null)
        {
            try
            {
                var result = await _complianceService.GetComplianceCalendarAsync(startDate, endDate, clientId);
                return Ok(ApiResponse.SuccessResponse(result));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting compliance calendar");
                return StatusCode(500, ApiResponse.ErrorResponse("Failed to get compliance calendar"));
            }
        }

        // Removed GetUpcomingDeadlines - ComplianceDeadlineDto doesn't exist

        [HttpPost]
        [Authorize(Policy = AuthorizationPolicies.ComplianceManagement)]
        public async Task<IActionResult> CreateComplianceItem([FromBody] CreateComplianceItemRequestDto request)
        {
            try
            {
                if (!ModelState.IsValid)
                {
                    return BadRequest(ApiResponse.ErrorResponse("Invalid request data",
                        ModelState.Values.SelectMany(v => v.Errors.Select(e => e.ErrorMessage)).ToList()));
                }

                var result = await _complianceService.CreateComplianceItemAsync(request);
                return CreatedAtAction(nameof(GetComplianceItemById), new { id = result.ComplianceId }, ApiResponse.SuccessResponse(result));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error creating compliance item");
                return StatusCode(500, ApiResponse.ErrorResponse("Failed to create compliance item"));
            }
        }

        [HttpGet("{id}")]
        [Authorize(Policy = AuthorizationPolicies.AllStaff)]
        public async Task<IActionResult> GetComplianceItemById(string id)
        {
            try
            {
                var result = await _complianceService.GetComplianceItemByIdAsync(id);
                if (result == null)
                    return NotFound(ApiResponse.ErrorResponse("Compliance item not found"));

                return Ok(ApiResponse.SuccessResponse(result));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting compliance item {Id}", id);
                return StatusCode(500, ApiResponse.ErrorResponse("Failed to get compliance item"));
            }
        }

        [HttpPut("{id}")]
        [Authorize(Policy = AuthorizationPolicies.ComplianceManagement)]
        public async Task<IActionResult> UpdateComplianceItem(Guid id, [FromBody] UpdateComplianceItemRequestDto request)
        {
            try
            {
                if (!ModelState.IsValid)
                {
                    return BadRequest(ApiResponse.ErrorResponse("Invalid request data",
                        ModelState.Values.SelectMany(v => v.Errors.Select(e => e.ErrorMessage)).ToList()));
                }

                request.ComplianceId = id;
                var result = await _complianceService.UpdateComplianceItemAsync(request);
                return Ok(ApiResponse.SuccessResponse(result));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating compliance item {Id}", id);
                return StatusCode(500, ApiResponse.ErrorResponse("Failed to update compliance item"));
            }
        }

        [HttpDelete("{id}")]
        [Authorize(Policy = AuthorizationPolicies.ComplianceManagement)]
        public async Task<IActionResult> DeleteComplianceItem(string id)
        {
            try
            {
                var result = await _complianceService.DeleteComplianceItemAsync(id);
                if (!result)
                    return NotFound(ApiResponse.ErrorResponse("Compliance item not found"));

                return Ok(ApiResponse.SuccessResponse("Compliance item deleted successfully"));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error deleting compliance item {Id}", id);
                return StatusCode(500, ApiResponse.ErrorResponse("Failed to delete compliance item"));
            }
        }

        [HttpPost("{id}/complete")]
        [Authorize(Policy = AuthorizationPolicies.AllStaff)]
        public async Task<IActionResult> MarkComplianceCompleted(string id, [FromBody] CompleteComplianceRequestDto request)
        {
            try
            {
                var userId = GetCurrentUserId();
                var result = await _complianceService.MarkComplianceCompletedAsync(id, userId, request.Notes);
                if (!result)
                    return NotFound(ApiResponse.ErrorResponse("Compliance item not found"));

                return Ok(ApiResponse.SuccessResponse("Compliance item marked as completed"));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error marking compliance item as completed {Id}", id);
                return StatusCode(500, ApiResponse.ErrorResponse("Failed to mark compliance item as completed"));
            }
        }

        [HttpGet("stats")]
        [Authorize(Policy = AuthorizationPolicies.ManagerOrAdmin)]
        public async Task<IActionResult> GetComplianceStats([FromQuery] string? clientId = null)
        {
            try
            {
                var result = await _complianceService.GetComplianceStatsAsync(clientId);
                return Ok(ApiResponse.SuccessResponse(result));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting compliance stats");
                return StatusCode(500, ApiResponse.ErrorResponse("Failed to get compliance stats"));
            }
        }

        // Removed GetComplianceTypes - ComplianceTypeDto doesn't exist

        // Simplified endpoints for basic functionality
        [HttpPost("{id}/status")]
        [Authorize(Policy = AuthorizationPolicies.ComplianceManagement)]
        public async Task<IActionResult> UpdateComplianceStatus(string id, [FromBody] UpdateComplianceStatusRequestDto request)
        {
            try
            {
                var userId = GetCurrentUserId();
                var result = await _complianceService.UpdateComplianceStatusAsync(id, request.Status, userId, request.Notes);
                if (!result)
                    return NotFound(ApiResponse.ErrorResponse("Compliance item not found"));

                return Ok(ApiResponse.SuccessResponse("Compliance status updated successfully"));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating compliance status {Id}", id);
                return StatusCode(500, ApiResponse.ErrorResponse("Failed to update compliance status"));
            }
        }

        private string GetCurrentUserId()
        {
            return User.FindFirst("UserId")?.Value ?? "";
        }
    }

    public class CompleteComplianceRequestDto
    {
        public string Notes { get; set; } = string.Empty;
    }

    public class UpdateComplianceStatusRequestDto
    {
        public string Status { get; set; } = string.Empty;
        public string Notes { get; set; } = string.Empty;
    }
}
