import React, { useState, useEffect } from 'react';
import { 
  Form, 
  Input, 
  Select, 
  DatePicker, 
  Button, 
  Card, 
  Row, 
  Col, 
  Switch,
  Alert,
  Spin,
  message
} from 'antd';
import { SaveOutlined, CloseOutlined } from '@ant-design/icons';
import { complianceService, CreateComplianceRequest, UpdateComplianceRequest, ComplianceItem } from '../../services/complianceService';
import { clientService } from '../../services/clientService';
import { userService } from '../../services/userService';
import dayjs from 'dayjs';

const { Option } = Select;
const { TextArea } = Input;

interface ComplianceFormProps {
  compliance?: ComplianceItem;
  onSave?: (compliance: ComplianceItem) => void;
  onCancel?: () => void;
  mode?: 'create' | 'edit';
}

const ComplianceForm: React.FC<ComplianceFormProps> = ({
  compliance,
  onSave,
  onCancel,
  mode = 'create'
}) => {
  const [form] = Form.useForm();
  const [loading, setLoading] = useState(false);
  const [clients, setClients] = useState<any[]>([]);
  const [users, setUsers] = useState<any[]>([]);
  const [error, setError] = useState<string>('');

  useEffect(() => {
    loadFormData();
    if (compliance && mode === 'edit') {
      populateForm();
    }
  }, [compliance, mode]);

  const loadFormData = async () => {
    try {
      // Load clients and users for dropdowns
      const [clientsData, usersData] = await Promise.all([
        clientService.getClients(),
        userService.getUsers()
      ]);
      
      setClients(clientsData.clients || []);
      setUsers(usersData.users || []);
    } catch (err: any) {
      console.error('Error loading form data:', err);
      setError('Failed to load form data');
    }
  };

  const populateForm = () => {
    if (compliance) {
      form.setFieldsValue({
        clientId: compliance.clientId,
        complianceType: compliance.complianceType,
        subType: compliance.subType,
        description: compliance.description,
        dueDate: dayjs(compliance.dueDate),
        priority: compliance.priority,
        assignedTo: compliance.assignedTo,
        status: compliance.status,
        notes: compliance.notes,
        reminderSent: compliance.reminderSent
      });
    }
  };

  const handleSubmit = async (values: any) => {
    try {
      setLoading(true);
      setError('');

      const formData = {
        ...values,
        dueDate: values.dueDate.toISOString(),
      };

      let result: ComplianceItem;

      if (mode === 'create') {
        const createRequest: CreateComplianceRequest = {
          clientId: formData.clientId,
          complianceType: formData.complianceType,
          subType: formData.subType,
          description: formData.description,
          dueDate: formData.dueDate,
          priority: formData.priority,
          assignedTo: formData.assignedTo,
          notes: formData.notes
        };

        console.log('Creating compliance item with request:', createRequest);
        result = await complianceService.createComplianceItem(createRequest);
        message.success('Compliance item created successfully');
      } else {
        const updateRequest: UpdateComplianceRequest = {
          complianceId: compliance!.complianceId,
          clientId: formData.clientId,
          complianceType: formData.complianceType,
          subType: formData.subType,
          description: formData.description,
          dueDate: formData.dueDate,
          status: formData.status,
          priority: formData.priority,
          assignedTo: formData.assignedTo,
          notes: formData.notes
        };
        result = await complianceService.updateComplianceItem(compliance!.complianceId, updateRequest);
        message.success('Compliance item updated successfully');
      }

      onSave?.(result);
    } catch (err: any) {
      setError(err.message || 'Failed to save compliance item');
      message.error('Failed to save compliance item');
    } finally {
      setLoading(false);
    }
  };

  const complianceTypes = [
    'Tax Filing',
    'Audit',
    'Regulatory Compliance',
    'Financial Reporting',
    'Legal Compliance',
    'Environmental Compliance',
    'Safety Compliance',
    'Data Protection',
    'Other'
  ];

  const priorities = [
    { value: 'Low', color: '#52c41a' },
    { value: 'Medium', color: '#1890ff' },
    { value: 'High', color: '#fa8c16' },
    { value: 'Critical', color: '#ff4d4f' }
  ];

  const statuses = [
    'Pending',
    'In Progress',
    'Completed',
    'Overdue',
    'Cancelled'
  ];

  return (
    <Card 
      title={mode === 'create' ? 'Create Compliance Item' : 'Edit Compliance Item'}
      extra={
        <Button 
          icon={<CloseOutlined />} 
          onClick={onCancel}
        >
          Cancel
        </Button>
      }
    >
      {error && (
        <Alert
          message="Error"
          description={error}
          type="error"
          showIcon
          closable
          style={{ marginBottom: '24px' }}
        />
      )}

      <Spin spinning={loading}>
        <Form
          form={form}
          layout="vertical"
          onFinish={handleSubmit}
          initialValues={{
            priority: 'Medium',
            status: 'Pending',
            reminderSent: false
          }}
        >
          <Row gutter={[16, 0]}>
            <Col xs={24} md={12}>
              <Form.Item
                name="clientId"
                label="Client"
                rules={[{ required: true, message: 'Please select a client' }]}
              >
                <Select
                  placeholder="Select client"
                  showSearch
                  filterOption={(input, option) =>
                    option?.children?.toLowerCase().includes(input.toLowerCase())
                  }
                >
                  {clients.map(client => (
                    <Option key={client.clientId} value={client.clientId}>
                      {client.companyName}
                    </Option>
                  ))}
                </Select>
              </Form.Item>
            </Col>

            <Col xs={24} md={12}>
              <Form.Item
                name="complianceType"
                label="Compliance Type"
                rules={[{ required: true, message: 'Please select compliance type' }]}
              >
                <Select placeholder="Select compliance type">
                  {complianceTypes.map(type => (
                    <Option key={type} value={type}>
                      {type}
                    </Option>
                  ))}
                </Select>
              </Form.Item>
            </Col>
          </Row>

          <Row gutter={[16, 0]}>
            <Col xs={24} md={12}>
              <Form.Item
                name="subType"
                label="Sub Type"
              >
                <Input placeholder="Enter sub type (optional)" />
              </Form.Item>
            </Col>

            <Col xs={24} md={12}>
              <Form.Item
                name="dueDate"
                label="Due Date"
                rules={[{ required: true, message: 'Please select due date' }]}
              >
                <DatePicker 
                  style={{ width: '100%' }}
                  disabledDate={(current) => current && current < dayjs().startOf('day')}
                />
              </Form.Item>
            </Col>
          </Row>

          <Row gutter={[16, 0]}>
            <Col xs={24} md={12}>
              <Form.Item
                name="priority"
                label="Priority"
                rules={[{ required: true, message: 'Please select priority' }]}
              >
                <Select placeholder="Select priority">
                  {priorities.map(priority => (
                    <Option key={priority.value} value={priority.value}>
                      <span style={{ color: priority.color }}>
                        {priority.value}
                      </span>
                    </Option>
                  ))}
                </Select>
              </Form.Item>
            </Col>

            <Col xs={24} md={12}>
              <Form.Item
                name="assignedTo"
                label="Assigned To"
              >
                <Select
                  placeholder="Select assignee"
                  allowClear
                  showSearch
                  filterOption={(input, option) =>
                    option?.children?.toLowerCase().includes(input.toLowerCase())
                  }
                >
                  {users.map(user => (
                    <Option key={user.userId} value={user.userId}>
                      {user.firstName} {user.lastName}
                    </Option>
                  ))}
                </Select>
              </Form.Item>
            </Col>
          </Row>

          {mode === 'edit' && (
            <Row gutter={[16, 0]}>
              <Col xs={24} md={12}>
                <Form.Item
                  name="status"
                  label="Status"
                  rules={[{ required: true, message: 'Please select status' }]}
                >
                  <Select placeholder="Select status">
                    {statuses.map(status => (
                      <Option key={status} value={status}>
                        {status}
                      </Option>
                    ))}
                  </Select>
                </Form.Item>
              </Col>

              <Col xs={24} md={12}>
                <Form.Item
                  name="reminderSent"
                  label="Reminder Sent"
                  valuePropName="checked"
                >
                  <Switch />
                </Form.Item>
              </Col>
            </Row>
          )}

          <Form.Item
            name="description"
            label="Description"
          >
            <TextArea 
              rows={3}
              placeholder="Enter compliance description"
            />
          </Form.Item>

          <Form.Item
            name="notes"
            label="Notes"
          >
            <TextArea 
              rows={3}
              placeholder="Enter additional notes"
            />
          </Form.Item>

          <Form.Item style={{ marginBottom: 0, textAlign: 'right' }}>
            <Button 
              type="default" 
              onClick={onCancel}
              style={{ marginRight: '8px' }}
            >
              Cancel
            </Button>
            <Button 
              type="primary" 
              htmlType="submit"
              icon={<SaveOutlined />}
              loading={loading}
            >
              {mode === 'create' ? 'Create' : 'Update'} Compliance Item
            </Button>
          </Form.Item>
        </Form>
      </Spin>
    </Card>
  );
};

export default ComplianceForm;
