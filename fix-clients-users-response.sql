-- Fix Clients and Users Stored Procedures Response Format
USE [CA_Portal_kumar_associates];
GO

-- Drop existing procedures
IF EXISTS (SELECT * FROM sys.procedures WHERE name = 'sp_GetClients')
    DROP PROCEDURE sp_GetClients;
GO

IF EXISTS (SELECT * FROM sys.procedures WHERE name = 'sp_GetUsers')
    DROP PROCEDURE sp_GetUsers;
GO

-- Create sp_GetClients with proper response format
CREATE PROCEDURE sp_GetClients
    @Payload NVARCHAR(MAX)
AS
BEGIN
    SET NOCOUNT ON;
    
    DECLARE @PageNumber INT = 1
    DECLARE @PageSize INT = 20
    DECLARE @SearchTerm NVARCHAR(255) = NULL
    DECLARE @ClientType NVARCHAR(50) = NULL
    DECLARE @BusinessType NVARCHAR(50) = NULL
    DECLARE @IsActive BIT = NULL
    DECLARE @City NVARCHAR(100) = NULL
    DECLARE @State NVARCHAR(100) = NULL
    
    -- Parse JSON payload
    SELECT 
        @PageNumber = ISNULL(TRY_CAST(JSON_VALUE(@Payload, '$.PageNumber') AS INT), 1),
        @PageSize = ISNULL(TRY_CAST(JSON_VALUE(@Payload, '$.PageSize') AS INT), 20),
        @SearchTerm = JSON_VALUE(@Payload, '$.SearchTerm'),
        @ClientType = JSON_VALUE(@Payload, '$.ClientType'),
        @BusinessType = JSON_VALUE(@Payload, '$.BusinessType'),
        @IsActive = TRY_CAST(JSON_VALUE(@Payload, '$.IsActive') AS BIT),
        @City = JSON_VALUE(@Payload, '$.City'),
        @State = JSON_VALUE(@Payload, '$.State')
    
    -- Calculate offset
    DECLARE @Offset INT = (@PageNumber - 1) * @PageSize
    
    -- Get total count
    DECLARE @TotalCount INT
    SELECT @TotalCount = COUNT(*)
    FROM Clients
    WHERE 1=1
        AND (@SearchTerm IS NULL OR @SearchTerm = '' OR CompanyName LIKE '%' + @SearchTerm + '%' OR Email LIKE '%' + @SearchTerm + '%' OR ContactPerson LIKE '%' + @SearchTerm + '%')
        AND (@ClientType IS NULL OR @ClientType = '' OR ClientType = @ClientType)
        AND (@BusinessType IS NULL OR @BusinessType = '' OR BusinessType = @BusinessType)
        AND (@IsActive IS NULL OR IsActive = @IsActive)
        AND (@City IS NULL OR @City = '' OR City = @City)
        AND (@State IS NULL OR @State = '' OR State = @State)
    
    -- Calculate total pages
    DECLARE @TotalPages INT = CEILING(CAST(@TotalCount AS FLOAT) / @PageSize)
    
    -- Get clients data
    DECLARE @ClientsJson NVARCHAR(MAX)
    SELECT @ClientsJson = (
        SELECT 
            ClientId,
            ClientCode,
            CompanyName,
            ContactPerson,
            Email,
            Phone,
            Address,
            City,
            State,
            PinCode,
            GSTNumber,
            PANNumber,
            ClientType,
            BusinessType,
            AnnualTurnover,
            IsActive,
            CreatedAt,
            CreatedBy,
            UpdatedAt,
            UpdatedBy
        FROM Clients
        WHERE 1=1
            AND (@SearchTerm IS NULL OR @SearchTerm = '' OR CompanyName LIKE '%' + @SearchTerm + '%' OR Email LIKE '%' + @SearchTerm + '%' OR ContactPerson LIKE '%' + @SearchTerm + '%')
            AND (@ClientType IS NULL OR @ClientType = '' OR ClientType = @ClientType)
            AND (@BusinessType IS NULL OR @BusinessType = '' OR BusinessType = @BusinessType)
            AND (@IsActive IS NULL OR IsActive = @IsActive)
            AND (@City IS NULL OR @City = '' OR City = @City)
            AND (@State IS NULL OR @State = '' OR State = @State)
        ORDER BY CompanyName
        OFFSET @Offset ROWS
        FETCH NEXT @PageSize ROWS ONLY
        FOR JSON PATH
    )
    
    -- Return response in expected format
    SELECT 
        ISNULL(@ClientsJson, '[]') AS Clients,
        @TotalCount AS TotalCount,
        @PageNumber AS PageNumber,
        @PageSize AS PageSize,
        @TotalPages AS TotalPages
    FOR JSON PATH, WITHOUT_ARRAY_WRAPPER
END
GO

-- Create sp_GetUsers with proper response format
CREATE PROCEDURE sp_GetUsers
    @Payload NVARCHAR(MAX)
AS
BEGIN
    SET NOCOUNT ON;
    
    DECLARE @PageNumber INT = 1
    DECLARE @PageSize INT = 20
    DECLARE @SearchTerm NVARCHAR(255) = NULL
    DECLARE @Role NVARCHAR(50) = NULL
    DECLARE @IsActive BIT = NULL
    DECLARE @Department NVARCHAR(100) = NULL
    
    -- Parse JSON payload
    SELECT 
        @PageNumber = ISNULL(TRY_CAST(JSON_VALUE(@Payload, '$.PageNumber') AS INT), 1),
        @PageSize = ISNULL(TRY_CAST(JSON_VALUE(@Payload, '$.PageSize') AS INT), 20),
        @SearchTerm = JSON_VALUE(@Payload, '$.SearchTerm'),
        @Role = JSON_VALUE(@Payload, '$.Role'),
        @IsActive = TRY_CAST(JSON_VALUE(@Payload, '$.IsActive') AS BIT),
        @Department = JSON_VALUE(@Payload, '$.Department')
    
    -- Calculate offset
    DECLARE @Offset INT = (@PageNumber - 1) * @PageSize
    
    -- Get total count
    DECLARE @TotalCount INT
    SELECT @TotalCount = COUNT(*)
    FROM Users
    WHERE 1=1
        AND (@SearchTerm IS NULL OR @SearchTerm = '' OR FirstName LIKE '%' + @SearchTerm + '%' OR LastName LIKE '%' + @SearchTerm + '%' OR Email LIKE '%' + @SearchTerm + '%')
        AND (@Role IS NULL OR @Role = '' OR Role = @Role)
        AND (@IsActive IS NULL OR IsActive = @IsActive)
        AND (@Department IS NULL OR @Department = '' OR Department = @Department)
    
    -- Calculate total pages
    DECLARE @TotalPages INT = CEILING(CAST(@TotalCount AS FLOAT) / @PageSize)
    
    -- Get users data
    DECLARE @UsersJson NVARCHAR(MAX)
    SELECT @UsersJson = (
        SELECT 
            UserId,
            Email,
            FirstName,
            LastName,
            Role,
            IsActive,
            CreatedAt,
            LastLoginAt,
            TenantId,
            Phone,
            Department,
            EmployeeCode
        FROM Users
        WHERE 1=1
            AND (@SearchTerm IS NULL OR @SearchTerm = '' OR FirstName LIKE '%' + @SearchTerm + '%' OR LastName LIKE '%' + @SearchTerm + '%' OR Email LIKE '%' + @SearchTerm + '%')
            AND (@Role IS NULL OR @Role = '' OR Role = @Role)
            AND (@IsActive IS NULL OR IsActive = @IsActive)
            AND (@Department IS NULL OR @Department = '' OR Department = @Department)
        ORDER BY FirstName, LastName
        OFFSET @Offset ROWS
        FETCH NEXT @PageSize ROWS ONLY
        FOR JSON PATH
    )
    
    -- Return response in expected format
    SELECT 
        ISNULL(@UsersJson, '[]') AS Users,
        @TotalCount AS TotalCount,
        @PageNumber AS PageNumber,
        @PageSize AS PageSize,
        @TotalPages AS TotalPages
    FOR JSON PATH, WITHOUT_ARRAY_WRAPPER
END
GO

PRINT '✅ Clients and Users stored procedures response format fixed!';
PRINT 'Procedures now return proper response objects with pagination metadata';
