# Test Create Compliance Item API
Write-Host "🧪 Testing Create Compliance Item API..." -ForegroundColor Cyan

# Get a real client ID from database
$clientQuery = "SELECT TOP 1 ClientId FROM Clients WHERE IsActive = 1"
$clientResult = sqlcmd -S localhost -E -d CA_Portal_kumar_associates -Q $clientQuery -h -1
$clientId = $clientResult.Trim()

Write-Host "📋 Using Client ID: $clientId" -ForegroundColor Yellow

# Create test payload
$testPayload = @{
    clientId = $clientId
    complianceType = "API Test Compliance"
    subType = "End-to-End Test"
    description = "Testing the create compliance API endpoint from PowerShell"
    dueDate = "2024-12-31T23:59:59Z"
    priority = "High"
    notes = "Created via automated API test"
} | ConvertTo-Json

Write-Host "📤 Sending POST request..." -ForegroundColor Yellow
Write-Host "Payload: $testPayload" -ForegroundColor Gray

try {
    # Make the API call
    $response = Invoke-RestMethod -Uri "https://localhost:7000/api/compliance" `
                                  -Method POST `
                                  -Body $testPayload `
                                  -ContentType "application/json"

    Write-Host "✅ SUCCESS! Compliance item created:" -ForegroundColor Green
    Write-Host ($response | ConvertTo-Json -Depth 3) -ForegroundColor Green

    # Verify it was created by checking the database
    Write-Host "`n🔍 Verifying in database..." -ForegroundColor Cyan
    $verifyQuery = "SELECT TOP 1 ComplianceId, ComplianceType, Description, Status, Priority FROM Compliance WHERE ComplianceType = 'API Test Compliance' ORDER BY CreatedAt DESC"
    sqlcmd -S localhost -E -d CA_Portal_kumar_associates -Q $verifyQuery

} catch {
    Write-Host "❌ ERROR: $($_.Exception.Message)" -ForegroundColor Red
    if ($_.Exception.Response) {
        Write-Host "Status Code: $($_.Exception.Response.StatusCode)" -ForegroundColor Red
    }
}

Write-Host "`n📊 Current compliance stats:" -ForegroundColor Cyan
$statsQuery = "SELECT 'Total Items' AS Metric, COUNT(*) AS Count FROM Compliance UNION ALL SELECT 'Pending Items', COUNT(*) FROM Compliance WHERE Status = 'Pending'"
sqlcmd -S localhost -E -d CA_Portal_kumar_associates -Q $statsQuery
