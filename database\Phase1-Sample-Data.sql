-- =============================================
-- Phase 1.2: Sample Data Population for Compliance Calendar Module
-- Realistic test data with 50+ compliance items
-- =============================================

USE [KumarAssociatesDB];

-- =============================================
-- 1. Populate ComplianceTypes Reference Data
-- =============================================
IF NOT EXISTS (SELECT 1 FROM ComplianceTypes)
BEGIN
    INSERT INTO [ComplianceTypes] (
        [Name], [Description], [Category], [RegulatoryBody], [DefaultDurationDays], 
        [DefaultPriority], [DefaultEstimatedHours], [SortOrder], [Color], [Icon]
    )
    VALUES 
        ('Income Tax Return', 'Annual income tax return filing', 'Tax', 'Income Tax Department', 30, 'High', 8.0, 1, '#FF6B6B', 'receipt-tax'),
        ('GST Return - GSTR1', 'Monthly GST return for outward supplies', 'GST', 'GST Council', 11, 'High', 4.0, 2, '#4ECDC4', 'file-text'),
        ('GST Return - GSTR3B', 'Monthly GST return summary', 'GST', 'GST Council', 20, 'High', 6.0, 3, '#45B7D1', 'file-check'),
        ('TDS Return', 'Tax Deducted at Source quarterly return', 'Tax', 'Income Tax Department', 30, 'Medium', 5.0, 4, '#96CEB4', 'minus-circle'),
        ('ESI Return', 'Employee State Insurance monthly return', 'Labor', 'ESIC', 21, 'Medium', 2.0, 5, '#FFEAA7', 'users'),
        ('PF Return', 'Provident Fund monthly return', 'Labor', 'EPFO', 15, 'Medium', 3.0, 6, '#DDA0DD', 'shield'),
        ('ROC Annual Filing', 'Registrar of Companies annual return', 'Corporate', 'MCA', 60, 'High', 12.0, 7, '#98D8C8', 'building'),
        ('Audit Report', 'Annual financial audit report', 'Audit', 'Various', 90, 'Critical', 40.0, 8, '#F7DC6F', 'search'),
        ('VAT Return', 'Value Added Tax return filing', 'Tax', 'State Tax Department', 20, 'Medium', 4.0, 9, '#BB8FCE', 'percent'),
        ('Professional Tax', 'Professional tax payment and return', 'Tax', 'State Government', 10, 'Low', 1.0, 10, '#85C1E9', 'credit-card'),
        ('Labor License Renewal', 'Annual labor license renewal', 'Labor', 'Labor Department', 30, 'Medium', 6.0, 11, '#F8C471', 'award'),
        ('Environmental Clearance', 'Environmental compliance certificate', 'Environmental', 'Pollution Board', 45, 'High', 15.0, 12, '#82E0AA', 'leaf'),
        ('Fire Safety Certificate', 'Annual fire safety compliance', 'Safety', 'Fire Department', 30, 'High', 8.0, 13, '#EC7063', 'shield-alt'),
        ('Trade License Renewal', 'Municipal trade license renewal', 'Legal', 'Municipal Corporation', 30, 'Medium', 4.0, 14, '#AED6F1', 'store'),
        ('FSSAI License Renewal', 'Food safety license renewal', 'Food Safety', 'FSSAI', 30, 'Medium', 5.0, 15, '#A9DFBF', 'utensils');
    
    PRINT 'ComplianceTypes populated with 15 standard compliance types.';
END

-- =============================================
-- 2. Create Sample Clients (if not exists)
-- =============================================
IF NOT EXISTS (SELECT 1 FROM Clients)
BEGIN
    INSERT INTO [Clients] ([CompanyName], [ContactPerson], [Email], [Phone], [Address])
    VALUES 
        ('ABC Manufacturing Ltd', 'Rajesh Kumar', '<EMAIL>', '+91-**********', 'Plot 123, Industrial Area, Gurgaon'),
        ('XYZ Services Pvt Ltd', 'Priya Sharma', '<EMAIL>', '+91-**********', 'Office 456, Cyber City, Bangalore'),
        ('Tech Solutions Inc', 'Amit Patel', '<EMAIL>', '+91-**********', 'Tower A, IT Park, Pune'),
        ('Global Exports Ltd', 'Sunita Gupta', '<EMAIL>', '+91-**********', 'Warehouse 789, MIDC, Mumbai'),
        ('Green Energy Corp', 'Vikram Singh', '<EMAIL>', '+91-**********', 'Solar Park, Sector 15, Noida'),
        ('Food Processing Co', 'Meera Joshi', '<EMAIL>', '+91-9876543215', 'Factory Road, Indore'),
        ('Textile Mills Ltd', 'Ravi Agarwal', '<EMAIL>', '+91-9876543216', 'Mill Area, Coimbatore'),
        ('Construction Corp', 'Deepak Verma', '<EMAIL>', '+91-9876543217', 'Project Site, Ghaziabad'),
        ('Pharma Industries', 'Kavita Reddy', '<EMAIL>', '+91-9876543218', 'Pharma City, Hyderabad'),
        ('Auto Components Ltd', 'Suresh Yadav', '<EMAIL>', '+91-9876543219', 'Auto Hub, Chennai');
    
    PRINT 'Sample clients created.';
END

-- =============================================
-- 3. Create Sample Users (if not exists)
-- =============================================
IF NOT EXISTS (SELECT 1 FROM Users)
BEGIN
    INSERT INTO [Users] ([Email], [FirstName], [LastName], [PasswordHash], [Role])
    VALUES 
        ('<EMAIL>', 'Admin', 'User', 'hashed_password_admin', 'Admin'),
        ('<EMAIL>', 'Compliance', 'Manager', 'hashed_password_manager', 'Manager'),
        ('<EMAIL>', 'Senior', 'Associate', 'hashed_password_senior', 'Senior'),
        ('<EMAIL>', 'Rahul', 'Sharma', 'hashed_password_junior1', 'Staff'),
        ('<EMAIL>', 'Pooja', 'Gupta', 'hashed_password_junior2', 'Staff'),
        ('<EMAIL>', 'Arjun', 'Patel', 'hashed_password_junior3', 'Staff'),
        ('<EMAIL>', 'Intern', 'Trainee', 'hashed_password_intern', 'Intern');
    
    PRINT 'Sample users created.';
END

-- =============================================
-- 4. Populate Compliance Items (50+ items)
-- =============================================
DECLARE @ClientIds TABLE (ClientId UNIQUEIDENTIFIER, CompanyName NVARCHAR(200));
DECLARE @UserIds TABLE (UserId UNIQUEIDENTIFIER, FirstName NVARCHAR(100));
DECLARE @TypeIds TABLE (TypeId UNIQUEIDENTIFIER, Name NVARCHAR(100));

-- Get client IDs
INSERT INTO @ClientIds SELECT ClientId, CompanyName FROM Clients;

-- Get user IDs  
INSERT INTO @UserIds SELECT UserId, FirstName FROM Users WHERE Role IN ('Manager', 'Senior', 'Staff');

-- Get compliance type IDs
INSERT INTO @TypeIds SELECT TypeId, Name FROM ComplianceTypes;

-- Clear existing compliance data for fresh start
DELETE FROM ComplianceComments;
DELETE FROM ComplianceAttachments;
DELETE FROM ComplianceReminders;
DELETE FROM ComplianceHistory;
DELETE FROM Compliance;

-- Insert comprehensive compliance data
INSERT INTO [Compliance] (
    [ClientId], [ComplianceType], [SubType], [Description], [DueDate], [Status], [Priority], 
    [AssignedTo], [CreatedBy], [Notes], [EstimatedHours], [ComplianceYear], [RegulatoryBody], [IsRecurring], [RecurrencePattern]
)
SELECT 
    c.ClientId,
    ct.Name,
    CASE 
        WHEN ct.Name LIKE '%GST%' THEN 'Monthly Filing'
        WHEN ct.Name LIKE '%Tax%' THEN 'Annual Filing'
        WHEN ct.Name LIKE '%Return%' THEN 'Quarterly Filing'
        ELSE 'Standard Compliance'
    END,
    ct.Description + ' for ' + c.CompanyName,
    -- Due dates spread across the year
    CASE 
        WHEN ROW_NUMBER() OVER (ORDER BY c.ClientId, ct.TypeId) % 12 = 1 THEN DATEADD(DAY, 15, GETUTCDATE())
        WHEN ROW_NUMBER() OVER (ORDER BY c.ClientId, ct.TypeId) % 12 = 2 THEN DATEADD(DAY, 30, GETUTCDATE())
        WHEN ROW_NUMBER() OVER (ORDER BY c.ClientId, ct.TypeId) % 12 = 3 THEN DATEADD(DAY, -5, GETUTCDATE()) -- Overdue
        WHEN ROW_NUMBER() OVER (ORDER BY c.ClientId, ct.TypeId) % 12 = 4 THEN DATEADD(DAY, 45, GETUTCDATE())
        WHEN ROW_NUMBER() OVER (ORDER BY c.ClientId, ct.TypeId) % 12 = 5 THEN DATEADD(DAY, 7, GETUTCDATE())
        WHEN ROW_NUMBER() OVER (ORDER BY c.ClientId, ct.TypeId) % 12 = 6 THEN DATEADD(DAY, -10, GETUTCDATE()) -- Overdue
        WHEN ROW_NUMBER() OVER (ORDER BY c.ClientId, ct.TypeId) % 12 = 7 THEN DATEADD(DAY, 60, GETUTCDATE())
        WHEN ROW_NUMBER() OVER (ORDER BY c.ClientId, ct.TypeId) % 12 = 8 THEN DATEADD(DAY, 90, GETUTCDATE())
        WHEN ROW_NUMBER() OVER (ORDER BY c.ClientId, ct.TypeId) % 12 = 9 THEN DATEADD(DAY, 21, GETUTCDATE())
        WHEN ROW_NUMBER() OVER (ORDER BY c.ClientId, ct.TypeId) % 12 = 10 THEN DATEADD(DAY, -2, GETUTCDATE()) -- Overdue
        WHEN ROW_NUMBER() OVER (ORDER BY c.ClientId, ct.TypeId) % 12 = 11 THEN DATEADD(DAY, 120, GETUTCDATE())
        ELSE DATEADD(DAY, 35, GETUTCDATE())
    END,
    -- Status distribution
    CASE 
        WHEN ROW_NUMBER() OVER (ORDER BY c.ClientId, ct.TypeId) % 10 = 1 THEN 'Completed'
        WHEN ROW_NUMBER() OVER (ORDER BY c.ClientId, ct.TypeId) % 10 = 2 THEN 'In Progress'
        WHEN ROW_NUMBER() OVER (ORDER BY c.ClientId, ct.TypeId) % 10 = 3 THEN 'Overdue'
        WHEN ROW_NUMBER() OVER (ORDER BY c.ClientId, ct.TypeId) % 10 = 4 THEN 'Pending'
        WHEN ROW_NUMBER() OVER (ORDER BY c.ClientId, ct.TypeId) % 10 = 5 THEN 'In Progress'
        WHEN ROW_NUMBER() OVER (ORDER BY c.ClientId, ct.TypeId) % 10 = 6 THEN 'Overdue'
        WHEN ROW_NUMBER() OVER (ORDER BY c.ClientId, ct.TypeId) % 10 = 7 THEN 'Completed'
        WHEN ROW_NUMBER() OVER (ORDER BY c.ClientId, ct.TypeId) % 10 = 8 THEN 'Pending'
        WHEN ROW_NUMBER() OVER (ORDER BY c.ClientId, ct.TypeId) % 10 = 9 THEN 'In Progress'
        ELSE 'Pending'
    END,
    -- Priority distribution
    CASE 
        WHEN ct.Name LIKE '%Income Tax%' OR ct.Name LIKE '%Audit%' THEN 'Critical'
        WHEN ct.Name LIKE '%GST%' OR ct.Name LIKE '%ROC%' THEN 'High'
        WHEN ct.Name LIKE '%TDS%' OR ct.Name LIKE '%ESI%' THEN 'Medium'
        ELSE 'Low'
    END,
    -- Assign to different users
    (SELECT TOP 1 UserId FROM @UserIds ORDER BY NEWID()),
    (SELECT TOP 1 UserId FROM @UserIds WHERE FirstName = 'Compliance'),
    -- Notes
    CASE 
        WHEN ROW_NUMBER() OVER (ORDER BY c.ClientId, ct.TypeId) % 5 = 1 THEN 'Client has provided all required documents. Processing in progress.'
        WHEN ROW_NUMBER() OVER (ORDER BY c.ClientId, ct.TypeId) % 5 = 2 THEN 'Awaiting additional documentation from client. Follow-up required.'
        WHEN ROW_NUMBER() OVER (ORDER BY c.ClientId, ct.TypeId) % 5 = 3 THEN 'Urgent filing required. Penalty may apply if delayed further.'
        WHEN ROW_NUMBER() OVER (ORDER BY c.ClientId, ct.TypeId) % 5 = 4 THEN 'Regular compliance item. Standard processing timeline applies.'
        ELSE 'New compliance requirement. Client briefing scheduled.'
    END,
    -- Estimated hours
    CASE 
        WHEN ct.Name LIKE '%Audit%' THEN 40.0
        WHEN ct.Name LIKE '%Income Tax%' THEN 8.0
        WHEN ct.Name LIKE '%GST%' THEN 4.0
        WHEN ct.Name LIKE '%ROC%' THEN 12.0
        ELSE 3.0
    END,
    YEAR(GETUTCDATE()),
    ct.RegulatoryBody,
    CASE WHEN ct.Name LIKE '%Monthly%' OR ct.Name LIKE '%GST%' OR ct.Name LIKE '%ESI%' OR ct.Name LIKE '%PF%' THEN 1 ELSE 0 END,
    CASE 
        WHEN ct.Name LIKE '%Monthly%' OR ct.Name LIKE '%GST%' OR ct.Name LIKE '%ESI%' OR ct.Name LIKE '%PF%' THEN 'Monthly'
        WHEN ct.Name LIKE '%TDS%' THEN 'Quarterly'
        WHEN ct.Name LIKE '%Annual%' OR ct.Name LIKE '%Income Tax%' THEN 'Annually'
        ELSE NULL
    END
FROM @ClientIds c
CROSS JOIN @TypeIds ct
WHERE ROW_NUMBER() OVER (ORDER BY c.ClientId, ct.TypeId) <= 75; -- Create 75 compliance items

PRINT 'Compliance items populated with 75 realistic entries.';

-- =============================================
-- 5. Update Completed Items
-- =============================================
UPDATE Compliance 
SET CompletedAt = DATEADD(DAY, -RAND() * 30, GETUTCDATE()),
    CompletedBy = CreatedBy,
    ActualHours = EstimatedHours * (0.8 + RAND() * 0.4) -- 80% to 120% of estimated
WHERE Status = 'Completed';

-- =============================================
-- 6. Update Overdue Status for Past Due Items
-- =============================================
UPDATE Compliance 
SET Status = 'Overdue'
WHERE DueDate < GETUTCDATE() AND Status NOT IN ('Completed', 'Cancelled');

PRINT 'Compliance statuses updated based on due dates.';

-- =============================================
-- Summary Report
-- =============================================
PRINT '';
PRINT '============================================================';
PRINT 'Phase 1.2: Sample Data Population Complete!';
PRINT '============================================================';

SELECT 
    'Total Compliance Items' AS [Metric],
    COUNT(*) AS [Count]
FROM Compliance
UNION ALL
SELECT 'Pending Items', COUNT(*) FROM Compliance WHERE Status = 'Pending'
UNION ALL
SELECT 'In Progress Items', COUNT(*) FROM Compliance WHERE Status = 'In Progress'
UNION ALL
SELECT 'Completed Items', COUNT(*) FROM Compliance WHERE Status = 'Completed'
UNION ALL
SELECT 'Overdue Items', COUNT(*) FROM Compliance WHERE Status = 'Overdue'
UNION ALL
SELECT 'High Priority Items', COUNT(*) FROM Compliance WHERE Priority = 'High'
UNION ALL
SELECT 'Critical Priority Items', COUNT(*) FROM Compliance WHERE Priority = 'Critical';

PRINT '';
PRINT 'Ready for Phase 1.3: Database Verification';
PRINT '============================================================';
