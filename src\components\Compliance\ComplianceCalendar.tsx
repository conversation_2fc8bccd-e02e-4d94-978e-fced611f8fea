import React, { useState, useEffect } from 'react';
import { Calendar, Badge, Card, Modal, Descriptions, Tag, Button, Select, Alert, Spin } from 'antd';
import { CalendarOutlined, EyeOutlined } from '@ant-design/icons';
import { complianceService, ComplianceCalendarItem } from '../../services/complianceService';
import dayjs, { Dayjs } from 'dayjs';

const { Option } = Select;

interface ComplianceCalendarProps {
  selectedClient?: string;
  onViewCompliance?: (compliance: ComplianceCalendarItem) => void;
}

const ComplianceCalendar: React.FC<ComplianceCalendarProps> = ({
  selectedClient,
  onViewCompliance
}) => {
  const [loading, setLoading] = useState(false);
  const [calendarData, setCalendarData] = useState<ComplianceCalendarItem[]>([]);
  const [selectedDate, setSelectedDate] = useState<Dayjs>(dayjs());
  const [selectedItems, setSelectedItems] = useState<ComplianceCalendarItem[]>([]);
  const [modalVisible, setModalVisible] = useState(false);
  const [error, setError] = useState<string>('');

  useEffect(() => {
    loadCalendarData();
  }, [selectedClient, selectedDate]);

  const loadCalendarData = async () => {
    try {
      setLoading(true);
      setError('');

      // Load calendar data for the current month
      const startDate = selectedDate.startOf('month').toDate();
      const endDate = selectedDate.endOf('month').toDate();

      const data = await complianceService.getComplianceCalendar(startDate, endDate, selectedClient);
      setCalendarData(data);

    } catch (err: any) {
      setError(err.message || 'Failed to load calendar data');
      console.error('Calendar load error:', err);
    } finally {
      setLoading(false);
    }
  };

  const getStatusBadgeStatus = (status: string) => {
    switch (status.toLowerCase()) {
      case 'completed': return 'success';
      case 'in progress': return 'processing';
      case 'overdue': return 'error';
      case 'pending': return 'warning';
      default: return 'default';
    }
  };

  const getPriorityColor = (priority: string) => {
    switch (priority.toLowerCase()) {
      case 'critical': return '#ff4d4f';
      case 'high': return '#fa8c16';
      case 'medium': return '#1890ff';
      case 'low': return '#52c41a';
      default: return '#d9d9d9';
    }
  };

  const getItemsForDate = (date: Dayjs) => {
    return calendarData.filter(item => 
      dayjs(item.dueDate).format('YYYY-MM-DD') === date.format('YYYY-MM-DD')
    );
  };

  const dateCellRender = (date: Dayjs) => {
    const items = getItemsForDate(date);
    
    if (items.length === 0) return null;

    return (
      <div style={{ fontSize: '12px' }}>
        {items.slice(0, 3).map((item, index) => (
          <div key={index} style={{ marginBottom: '2px' }}>
            <Badge 
              status={getStatusBadgeStatus(item.status)}
              text={
                <span 
                  style={{ 
                    fontSize: '11px',
                    color: getPriorityColor(item.priority),
                    fontWeight: item.priority === 'Critical' ? 'bold' : 'normal'
                  }}
                >
                  {item.title || item.complianceType}
                </span>
              }
            />
          </div>
        ))}
        {items.length > 3 && (
          <div style={{ fontSize: '10px', color: '#666' }}>
            +{items.length - 3} more
          </div>
        )}
      </div>
    );
  };

  const onDateSelect = (date: Dayjs) => {
    const items = getItemsForDate(date);
    if (items.length > 0) {
      setSelectedItems(items);
      setModalVisible(true);
    }
  };

  const onPanelChange = (date: Dayjs) => {
    setSelectedDate(date);
  };

  return (
    <div style={{ padding: '24px' }}>
      {/* Header */}
      <div style={{ marginBottom: '24px', display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
        <div>
          <h1 style={{ margin: 0, display: 'flex', alignItems: 'center' }}>
            <CalendarOutlined style={{ marginRight: '8px' }} />
            Compliance Calendar
          </h1>
          <p style={{ margin: '4px 0 0 0', color: '#666' }}>
            View compliance deadlines and schedules
          </p>
        </div>
      </div>

      {error && (
        <Alert
          message="Error"
          description={error}
          type="error"
          showIcon
          closable
          style={{ marginBottom: '24px' }}
        />
      )}

      {/* Calendar */}
      <Card>
        <Spin spinning={loading}>
          <Calendar
            dateCellRender={dateCellRender}
            onSelect={onDateSelect}
            onPanelChange={onPanelChange}
            value={selectedDate}
          />
        </Spin>
      </Card>

      {/* Details Modal */}
      <Modal
        title={`Compliance Items - ${selectedItems.length > 0 ? dayjs(selectedItems[0].dueDate).format('MMMM DD, YYYY') : ''}`}
        open={modalVisible}
        onCancel={() => setModalVisible(false)}
        footer={null}
        width={800}
      >
        <div style={{ maxHeight: '400px', overflowY: 'auto' }}>
          {selectedItems.map((item, index) => (
            <Card 
              key={index}
              size="small"
              style={{ marginBottom: '16px' }}
              title={
                <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                  <span>{item.title || item.complianceType}</span>
                  <div>
                    <Tag color={getPriorityColor(item.priority)}>
                      {item.priority}
                    </Tag>
                    <Tag color={getStatusBadgeStatus(item.status) === 'success' ? 'green' : 
                               getStatusBadgeStatus(item.status) === 'error' ? 'red' :
                               getStatusBadgeStatus(item.status) === 'warning' ? 'orange' : 'blue'}>
                      {item.status}
                    </Tag>
                  </div>
                </div>
              }
              extra={
                <Button 
                  type="link" 
                  icon={<EyeOutlined />}
                  onClick={() => onViewCompliance?.(item)}
                >
                  View
                </Button>
              }
            >
              <Descriptions column={2} size="small">
                <Descriptions.Item label="Client">
                  {item.clientName}
                </Descriptions.Item>
                <Descriptions.Item label="Type">
                  {item.complianceType}
                </Descriptions.Item>
                <Descriptions.Item label="Due Date">
                  {dayjs(item.dueDate).format('MMM DD, YYYY')}
                </Descriptions.Item>
                <Descriptions.Item label="Assigned To">
                  {item.assignedToName || 'Unassigned'}
                </Descriptions.Item>
                {item.isOverdue && (
                  <Descriptions.Item label="Status" span={2}>
                    <Tag color="red">Overdue by {Math.abs(item.daysUntilDue)} days</Tag>
                  </Descriptions.Item>
                )}
                {!item.isOverdue && item.daysUntilDue <= 7 && (
                  <Descriptions.Item label="Status" span={2}>
                    <Tag color="orange">Due in {item.daysUntilDue} days</Tag>
                  </Descriptions.Item>
                )}
              </Descriptions>
            </Card>
          ))}
        </div>
      </Modal>

      {/* Legend */}
      <Card 
        title="Legend" 
        size="small" 
        style={{ marginTop: '16px' }}
      >
        <div style={{ display: 'flex', flexWrap: 'wrap', gap: '16px' }}>
          <div style={{ display: 'flex', alignItems: 'center' }}>
            <Badge status="success" />
            <span style={{ marginLeft: '4px' }}>Completed</span>
          </div>
          <div style={{ display: 'flex', alignItems: 'center' }}>
            <Badge status="processing" />
            <span style={{ marginLeft: '4px' }}>In Progress</span>
          </div>
          <div style={{ display: 'flex', alignItems: 'center' }}>
            <Badge status="warning" />
            <span style={{ marginLeft: '4px' }}>Pending</span>
          </div>
          <div style={{ display: 'flex', alignItems: 'center' }}>
            <Badge status="error" />
            <span style={{ marginLeft: '4px' }}>Overdue</span>
          </div>
          <div style={{ marginLeft: '24px', display: 'flex', alignItems: 'center', gap: '8px' }}>
            <span>Priority:</span>
            <Tag color="#ff4d4f">Critical</Tag>
            <Tag color="#fa8c16">High</Tag>
            <Tag color="#1890ff">Medium</Tag>
            <Tag color="#52c41a">Low</Tag>
          </div>
        </div>
      </Card>
    </div>
  );
};

export default ComplianceCalendar;
