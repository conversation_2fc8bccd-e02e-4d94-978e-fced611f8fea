-- Fixed Create Compliance Item Stored Procedure
USE [CA_Portal_kumar_associates];
GO

CREATE PROCEDURE sp_CreateComplianceItem
    @Payload NVARCHAR(MAX)
AS
BEGIN
    SET NOCOUNT ON;
    
    DECLARE @ComplianceId UNIQUEIDENTIFIER = NEWID()
    DECLARE @ClientId UNIQUEIDENTIFIER
    DECLARE @ComplianceType NVARCHAR(100)
    DECLARE @SubType NVARCHAR(100)
    DECLARE @Description NVARCHAR(MAX)
    DECLARE @DueDate DATETIME
    DECLARE @Priority NVARCHAR(20)
    DECLARE @AssignedTo UNIQUEIDENTIFIER
    DECLARE @Notes NVARCHAR(MAX)
    DECLARE @CreatedBy UNIQUEIDENTIFIER
    DECLARE @EstimatedHours DECIMAL(5,2)
    
    -- Parse JSON payload with proper GUID conversion
    SELECT 
        @ClientId = TRY_CAST(JSON_VALUE(@Payload, '$.clientId') AS UNIQUEIDENTIFIER),
        @ComplianceType = JSON_VALUE(@Payload, '$.complianceType'),
        @SubType = JSON_VALUE(@Payload, '$.subType'),
        @Description = JSON_VALUE(@Payload, '$.description'),
        @DueDate = TRY_CAST(JSON_VALUE(@Payload, '$.dueDate') AS DATETIME),
        @Priority = ISNULL(JSON_VALUE(@Payload, '$.priority'), 'Medium'),
        @AssignedTo = TRY_CAST(JSON_VALUE(@Payload, '$.assignedTo') AS UNIQUEIDENTIFIER),
        @Notes = JSON_VALUE(@Payload, '$.notes'),
        @CreatedBy = TRY_CAST(JSON_VALUE(@Payload, '$.createdBy') AS UNIQUEIDENTIFIER),
        @EstimatedHours = TRY_CAST(JSON_VALUE(@Payload, '$.estimatedHours') AS DECIMAL(5,2))
    
    -- Use AssignedTo as CreatedBy if CreatedBy is not provided
    IF @CreatedBy IS NULL
        SET @CreatedBy = ISNULL(@AssignedTo, @ClientId)
    
    -- Validate required fields
    IF @ClientId IS NULL
    BEGIN
        RAISERROR('ClientId is required', 16, 1)
        RETURN
    END
    
    IF @ComplianceType IS NULL OR @ComplianceType = ''
    BEGIN
        RAISERROR('ComplianceType is required', 16, 1)
        RETURN
    END
    
    IF @DueDate IS NULL
    BEGIN
        RAISERROR('DueDate is required', 16, 1)
        RETURN
    END
    
    -- Insert new compliance item
    INSERT INTO Compliance (
        ComplianceId, ClientId, ComplianceType, SubType, Description,
        DueDate, Priority, AssignedTo, Notes, CreatedBy, CreatedAt, 
        EstimatedHours, ComplianceYear, Status, IsActive
    )
    VALUES (
        @ComplianceId, @ClientId, @ComplianceType, @SubType, @Description,
        @DueDate, @Priority, @AssignedTo, @Notes, @CreatedBy, GETUTCDATE(), 
        @EstimatedHours, YEAR(@DueDate), 'Pending', 1
    )
    
    -- Return the created item
    SELECT 
        c.ComplianceId,
        c.ClientId,
        cl.CompanyName AS ClientName,
        c.ComplianceType,
        c.SubType,
        c.Description,
        c.DueDate,
        c.Status,
        c.Priority,
        c.AssignedTo,
        ISNULL(u.FirstName + ' ' + u.LastName, 'Unassigned') AS AssignedToName,
        c.CreatedAt,
        c.Notes,
        c.EstimatedHours
    FROM Compliance c
    INNER JOIN Clients cl ON c.ClientId = cl.ClientId
    LEFT JOIN Users u ON c.AssignedTo = u.UserId
    WHERE c.ComplianceId = @ComplianceId
    
    FOR JSON PATH, WITHOUT_ARRAY_WRAPPER
END
GO

PRINT '✅ Fixed sp_CreateComplianceItem created successfully!';
