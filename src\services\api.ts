// API Configuration and Base Service
import axios, { AxiosInstance, AxiosRequestConfig, AxiosResponse } from 'axios';
import { env } from '@/config/env';

// API Configuration
export const API_CONFIG = {
  BASE_URL: env.API_URL,
  TIMEOUT: 30000,
  TENANT_HEADER: 'X-Tenant-ID',
};

// API Response Types
export interface ApiResponse<T = any> {
  success: boolean;
  message: string;
  data?: T;
  errors?: string[];
  timestamp: string;
}

export interface PaginatedResponse<T> {
  data: T[];
  totalCount: number;
  pageNumber: number;
  pageSize: number;
  totalPages: number;
  hasPreviousPage: boolean;
  hasNextPage: boolean;
}

// Auth Types
export interface LoginRequest {
  email: string;
  password: string;
  tenantKey?: string;
}

export interface LoginResponse {
  token: string;
  refreshToken: string;
  expiresAt: string;
  user: User;
  tenant: Tenant;
}

export interface User {
  userId: string;
  email: string;
  firstName: string;
  lastName: string;
  role: string;
  isActive: boolean;
  createdAt: string;
  lastLoginAt?: string;
  tenantId: string;
  phone?: string;
  department?: string;
  employeeCode?: string;
}

export interface Tenant {
  organizationId: string;
  tenantKey: string;
  organizationName: string;
  domain: string;
  databaseName: string;
  connectionString: string;
  isActive: boolean;
  createdAt: string;
  updatedAt: string;
  subscriptionTier: string;
  maxUsers: number;
  storageLimit: number;
}

// Token Management
class TokenManager {
  private static readonly TOKEN_KEY = 'ca_portal_token';
  private static readonly REFRESH_TOKEN_KEY = 'ca_portal_refresh_token';
  private static readonly USER_KEY = 'ca_portal_user';
  private static readonly TENANT_KEY = 'ca_portal_tenant';

  static getToken(): string | null {
    return localStorage.getItem(this.TOKEN_KEY);
  }

  static setToken(token: string): void {
    localStorage.setItem(this.TOKEN_KEY, token);
  }

  static getRefreshToken(): string | null {
    return localStorage.getItem(this.REFRESH_TOKEN_KEY);
  }

  static setRefreshToken(token: string): void {
    localStorage.setItem(this.REFRESH_TOKEN_KEY, token);
  }

  static getUser(): User | null {
    const user = localStorage.getItem(this.USER_KEY);
    return user ? JSON.parse(user) : null;
  }

  static setUser(user: User): void {
    localStorage.setItem(this.USER_KEY, JSON.stringify(user));
  }

  static getTenant(): Tenant | null {
    const tenant = localStorage.getItem(this.TENANT_KEY);
    return tenant ? JSON.parse(tenant) : null;
  }

  static setTenant(tenant: Tenant): void {
    localStorage.setItem(this.TENANT_KEY, JSON.stringify(tenant));
  }

  static clearAll(): void {
    localStorage.removeItem(this.TOKEN_KEY);
    localStorage.removeItem(this.REFRESH_TOKEN_KEY);
    localStorage.removeItem(this.USER_KEY);
    localStorage.removeItem(this.TENANT_KEY);
  }

  static isTokenExpired(token: string): boolean {
    try {
      const payload = JSON.parse(atob(token.split('.')[1]));
      const currentTime = Date.now() / 1000;
      return payload.exp < currentTime;
    } catch {
      return true;
    }
  }
}

// API Client Class
class ApiClient {
  private instance: AxiosInstance;

  constructor() {
    this.instance = axios.create({
      baseURL: API_CONFIG.BASE_URL,
      timeout: API_CONFIG.TIMEOUT,
      headers: {
        'Content-Type': 'application/json',
      },
    });

    this.setupInterceptors();
  }

  private setupInterceptors(): void {
    // Request interceptor
    this.instance.interceptors.request.use(
      (config) => {
        const token = TokenManager.getToken();
        const tenant = TokenManager.getTenant();

        if (token) {
          config.headers.Authorization = `Bearer ${token}`;
        }

        if (tenant) {
          config.headers[API_CONFIG.TENANT_HEADER] = tenant.tenantKey;
        }

        return config;
      },
      (error) => {
        return Promise.reject(error);
      }
    );

    // Response interceptor
    this.instance.interceptors.response.use(
      (response: AxiosResponse) => {
        return response;
      },
      async (error) => {
        const originalRequest = error.config;

        if (error.response?.status === 401 && !originalRequest._retry) {
          originalRequest._retry = true;

          try {
            const refreshToken = TokenManager.getRefreshToken();
            if (refreshToken) {
              const response = await this.refreshToken(refreshToken);
              TokenManager.setToken(response.data.token);
              
              // Retry original request
              originalRequest.headers.Authorization = `Bearer ${response.data.token}`;
              return this.instance(originalRequest);
            }
          } catch (refreshError) {
            // Refresh failed, redirect to login
            TokenManager.clearAll();
            window.location.href = '/login';
            return Promise.reject(refreshError);
          }
        }

        return Promise.reject(error);
      }
    );
  }

  private async refreshToken(refreshToken: string): Promise<AxiosResponse> {
    return this.instance.post('/auth/refresh-token', { refreshToken });
  }

  // Generic API methods
  async get<T>(url: string, config?: AxiosRequestConfig): Promise<ApiResponse<T>> {
    const response = await this.instance.get(url, config);
    return response.data;
  }

  async post<T>(url: string, data?: any, config?: AxiosRequestConfig): Promise<ApiResponse<T>> {
    const response = await this.instance.post(url, data, config);
    return response.data;
  }

  async put<T>(url: string, data?: any, config?: AxiosRequestConfig): Promise<ApiResponse<T>> {
    const response = await this.instance.put(url, data, config);
    return response.data;
  }

  async delete<T>(url: string, config?: AxiosRequestConfig): Promise<ApiResponse<T>> {
    const response = await this.instance.delete(url, config);
    return response.data;
  }

  async patch<T>(url: string, data?: any, config?: AxiosRequestConfig): Promise<ApiResponse<T>> {
    const response = await this.instance.patch(url, data, config);
    return response.data;
  }
}

// Export singleton instance
export const apiClient = new ApiClient();
export { TokenManager };

// Utility function to handle API errors
export const handleApiError = (error: any): string => {
  if (error.response?.data?.message) {
    return error.response.data.message;
  }
  
  if (error.response?.data?.errors?.length > 0) {
    return error.response.data.errors.join(', ');
  }
  
  if (error.message) {
    return error.message;
  }
  
  return 'An unexpected error occurred';
};

// Utility function to check if user is authenticated
export const isAuthenticated = (): boolean => {
  const token = TokenManager.getToken();
  return token !== null && !TokenManager.isTokenExpired(token);
};

// Utility function to get current user
export const getCurrentUser = (): User | null => {
  return TokenManager.getUser();
};

// Utility function to get current tenant
export const getCurrentTenant = (): Tenant | null => {
  return TokenManager.getTenant();
};
