
import { Toaster } from "@/components/ui/toaster";
import { Toaster as Sonner } from "@/components/ui/sonner";
import { TooltipProvider } from "@/components/ui/tooltip";
import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import { BrowserRouter, Routes, Route } from "react-router-dom";
import { AuthProvider } from "@/contexts/AuthContext";
import ProtectedRoute from "@/components/ProtectedRoute";
import Index from "./pages/Index";
import Login from "./pages/Login";
import AdminStaffManagement from "./pages/AdminStaffManagement";
import ComplianceCalendar from "./pages/ComplianceCalendar";
import ClientReminders from "./pages/ClientReminders";
import ReportGenerator from "./pages/ReportGenerator";
import FilingAssistant from "./pages/FilingAssistant";
import ClientHistory from "./pages/ClientHistory";
import DocumentVault from "./pages/DocumentVault";
import TaskManagement from "./pages/TaskManagement";
import CommunicationHub from "./pages/CommunicationHub";
import NotFound from "./pages/NotFound";
import ApiTest from "./components/ApiTest";
import "./utils/envTest"; // Import environment test for debugging

const queryClient = new QueryClient();

const App = () => (
  <QueryClientProvider client={queryClient}>
    <TooltipProvider>
      <Toaster />
      <Sonner />
      <AuthProvider>
        <BrowserRouter>
          <Routes>
            <Route path="/login" element={<Login />} />
            <Route path="/" element={
              <ProtectedRoute>
                <Index />
              </ProtectedRoute>
            } />
            <Route path="/compliance-calendar" element={
              <ProtectedRoute>
                <ComplianceCalendar />
              </ProtectedRoute>
            } />
            <Route path="/admin/staff" element={
              <ProtectedRoute>
                <AdminStaffManagement />
              </ProtectedRoute>
            } />
            <Route path="/client-reminders" element={
              <ProtectedRoute>
                <ClientReminders />
              </ProtectedRoute>
            } />
            <Route path="/report-generator" element={
              <ProtectedRoute>
                <ReportGenerator />
              </ProtectedRoute>
            } />
            <Route path="/filing-assistant" element={
              <ProtectedRoute>
                <FilingAssistant />
              </ProtectedRoute>
            } />
            <Route path="/client-history" element={
              <ProtectedRoute>
                <ClientHistory />
              </ProtectedRoute>
            } />
            <Route path="/document-vault" element={
              <ProtectedRoute>
                <DocumentVault />
              </ProtectedRoute>
            } />
            <Route path="/task-management" element={
              <ProtectedRoute>
                <TaskManagement />
              </ProtectedRoute>
            } />
            <Route path="/communication-hub" element={
              <ProtectedRoute>
                <CommunicationHub />
              </ProtectedRoute>
            } />
            <Route path="/api-test" element={
              <ProtectedRoute requiredRoles={['Admin']}>
                <ApiTest />
              </ProtectedRoute>
            } />
            {/* ADD ALL CUSTOM ROUTES ABOVE THE CATCH-ALL "*" ROUTE */}
            <Route path="*" element={<NotFound />} />
          </Routes>
        </BrowserRouter>
      </AuthProvider>
    </TooltipProvider>
  </QueryClientProvider>
);

export default App;
