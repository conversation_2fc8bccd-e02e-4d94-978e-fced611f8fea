using System.ComponentModel.DataAnnotations;

namespace CAPortalAPI.Models.DTOs
{
    public class ClientDto
    {
        public Guid ClientId { get; set; }
        public string ClientCode { get; set; } = string.Empty;
        public string CompanyName { get; set; } = string.Empty;
        public string? ContactPerson { get; set; }
        public string? Email { get; set; }
        public string? Phone { get; set; }
        public string? Address { get; set; }
        public string? City { get; set; }
        public string? State { get; set; }
        public string? PinCode { get; set; }
        public string? GSTNumber { get; set; }
        public string? PANNumber { get; set; }
        public string ClientType { get; set; } = string.Empty;
        public string? BusinessType { get; set; }
        public decimal? AnnualTurnover { get; set; }
        public bool IsActive { get; set; }
        public DateTime CreatedAt { get; set; }
        public Guid CreatedBy { get; set; }
        public DateTime UpdatedAt { get; set; }
        public Guid? UpdatedBy { get; set; }
    }

    public class CreateClientRequestDto
    {
        [Required]
        [MinLength(3)]
        [MaxLength(50)]
        public string ClientCode { get; set; } = string.Empty;

        [Required]
        [MinLength(2)]
        [MaxLength(255)]
        public string CompanyName { get; set; } = string.Empty;

        public string? ContactPerson { get; set; }

        [EmailAddress]
        public string? Email { get; set; }

        public string? Phone { get; set; }
        public string? Address { get; set; }
        public string? City { get; set; }
        public string? State { get; set; }
        public string? PinCode { get; set; }

        [RegularExpression(@"^[0-9]{2}[A-Z]{5}[0-9]{4}[A-Z]{1}[1-9A-Z]{1}Z[0-9A-Z]{1}$", 
            ErrorMessage = "Invalid GST Number format")]
        public string? GSTNumber { get; set; }

        [RegularExpression(@"^[A-Z]{5}[0-9]{4}[A-Z]{1}$", 
            ErrorMessage = "Invalid PAN Number format")]
        public string? PANNumber { get; set; }

        [Required]
        public string ClientType { get; set; } = string.Empty;

        public string? BusinessType { get; set; }
        public decimal? AnnualTurnover { get; set; }
    }

    public class UpdateClientRequestDto
    {
        [Required]
        public Guid ClientId { get; set; }

        [Required]
        [MinLength(3)]
        [MaxLength(50)]
        public string ClientCode { get; set; } = string.Empty;

        [Required]
        [MinLength(2)]
        [MaxLength(255)]
        public string CompanyName { get; set; } = string.Empty;

        public string? ContactPerson { get; set; }

        [EmailAddress]
        public string? Email { get; set; }

        public string? Phone { get; set; }
        public string? Address { get; set; }
        public string? City { get; set; }
        public string? State { get; set; }
        public string? PinCode { get; set; }

        [RegularExpression(@"^[0-9]{2}[A-Z]{5}[0-9]{4}[A-Z]{1}[1-9A-Z]{1}Z[0-9A-Z]{1}$", 
            ErrorMessage = "Invalid GST Number format")]
        public string? GSTNumber { get; set; }

        [RegularExpression(@"^[A-Z]{5}[0-9]{4}[A-Z]{1}$", 
            ErrorMessage = "Invalid PAN Number format")]
        public string? PANNumber { get; set; }

        [Required]
        public string ClientType { get; set; } = string.Empty;

        public string? BusinessType { get; set; }
        public decimal? AnnualTurnover { get; set; }
        public bool IsActive { get; set; } = true;
    }

    public class ClientListRequestDto
    {
        public int PageNumber { get; set; } = 1;
        public int PageSize { get; set; } = 20;
        public string? SearchTerm { get; set; }
        public string? ClientType { get; set; }
        public string? BusinessType { get; set; }
        public bool? IsActive { get; set; }
        public string? City { get; set; }
        public string? State { get; set; }
    }

    public class ClientListResponseDto
    {
        public List<ClientDto> Clients { get; set; } = new();
        public int TotalCount { get; set; }
        public int PageNumber { get; set; }
        public int PageSize { get; set; }
        public int TotalPages { get; set; }
    }
}
