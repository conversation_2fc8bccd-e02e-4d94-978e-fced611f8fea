-- =============================================
-- Step 2: Create Tables in KumarAssociatesDB
-- =============================================

USE [KumarAssociatesDB];

-- =============================================
-- 1. Create Users Table
-- =============================================
IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='Users' AND xtype='U')
BEGIN
    CREATE TABLE [dbo].[Users] (
        [UserId] UNIQUEIDENTIFIER PRIMARY KEY DEFAULT NEWID(),
        [Email] NVARCHAR(255) NOT NULL UNIQUE,
        [FirstName] NVARCHAR(100) NOT NULL,
        [LastName] NVARCHAR(100) NOT NULL,
        [PasswordHash] NVARCHAR(255) NOT NULL,
        [Role] NVARCHAR(50) NOT NULL DEFAULT 'Staff',
        [IsActive] BIT NOT NULL DEFAULT 1,
        [CreatedAt] DATETIME NOT NULL DEFAULT GETUTCDATE(),
        [LastLoginAt] DATETIME NULL
    );
    PRINT 'Users table created.';
END

-- =============================================
-- 2. Create Clients Table
-- =============================================
IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='Clients' AND xtype='U')
BEGIN
    CREATE TABLE [dbo].[Clients] (
        [ClientId] UNIQUEIDENTIFIER PRIMARY KEY DEFAULT NEWID(),
        [CompanyName] NVARCHAR(200) NOT NULL,
        [ContactPerson] NVARCHAR(100) NULL,
        [Email] NVARCHAR(255) NULL,
        [Phone] NVARCHAR(20) NULL,
        [Address] NVARCHAR(500) NULL,
        [IsActive] BIT NOT NULL DEFAULT 1,
        [CreatedAt] DATETIME NOT NULL DEFAULT GETUTCDATE()
    );
    PRINT 'Clients table created.';
END

-- =============================================
-- 3. Create ComplianceTypes Table
-- =============================================
IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='ComplianceTypes' AND xtype='U')
BEGIN
    CREATE TABLE [dbo].[ComplianceTypes] (
        [TypeId] UNIQUEIDENTIFIER PRIMARY KEY DEFAULT NEWID(),
        [Name] NVARCHAR(100) NOT NULL UNIQUE,
        [Description] NVARCHAR(MAX) NULL,
        [Category] NVARCHAR(50) NULL,
        [RegulatoryBody] NVARCHAR(100) NULL,
        [DefaultDurationDays] INT NULL,
        [DefaultPriority] NVARCHAR(20) NULL,
        [DefaultEstimatedHours] DECIMAL(5,2) NULL,
        [IsActive] BIT NOT NULL DEFAULT 1,
        [CreatedAt] DATETIME NOT NULL DEFAULT GETUTCDATE(),
        [SortOrder] INT NULL,
        [Color] NVARCHAR(7) NULL,
        [Icon] NVARCHAR(50) NULL
    );
    PRINT 'ComplianceTypes table created.';
END

-- =============================================
-- 4. Create Main Compliance Table
-- =============================================
IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='Compliance' AND xtype='U')
BEGIN
    CREATE TABLE [dbo].[Compliance] (
        [ComplianceId] UNIQUEIDENTIFIER PRIMARY KEY DEFAULT NEWID(),
        [ClientId] UNIQUEIDENTIFIER NOT NULL,
        [ComplianceType] NVARCHAR(100) NOT NULL,
        [SubType] NVARCHAR(100) NULL,
        [Description] NVARCHAR(MAX) NULL,
        [DueDate] DATETIME NOT NULL,
        [Status] NVARCHAR(50) NOT NULL DEFAULT 'Pending',
        [Priority] NVARCHAR(20) NOT NULL DEFAULT 'Medium',
        [AssignedTo] UNIQUEIDENTIFIER NULL,
        [CreatedAt] DATETIME NOT NULL DEFAULT GETUTCDATE(),
        [CreatedBy] UNIQUEIDENTIFIER NULL,
        [UpdatedAt] DATETIME NULL,
        [UpdatedBy] UNIQUEIDENTIFIER NULL,
        [CompletedAt] DATETIME NULL,
        [CompletedBy] UNIQUEIDENTIFIER NULL,
        [Notes] NVARCHAR(MAX) NULL,
        [ReminderSent] BIT NOT NULL DEFAULT 0,
        [LastReminderDate] DATETIME NULL,
        [IsActive] BIT NOT NULL DEFAULT 1,
        [EstimatedHours] DECIMAL(5,2) NULL,
        [ActualHours] DECIMAL(5,2) NULL,
        [ComplianceYear] INT NULL,
        [RegulatoryBody] NVARCHAR(100) NULL,
        [IsRecurring] BIT NOT NULL DEFAULT 0,
        [RecurrencePattern] NVARCHAR(50) NULL
    );
    PRINT 'Compliance table created.';
END

-- =============================================
-- 5. Create ComplianceHistory Table
-- =============================================
IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='ComplianceHistory' AND xtype='U')
BEGIN
    CREATE TABLE [dbo].[ComplianceHistory] (
        [HistoryId] UNIQUEIDENTIFIER PRIMARY KEY DEFAULT NEWID(),
        [ComplianceId] UNIQUEIDENTIFIER NOT NULL,
        [Action] NVARCHAR(100) NOT NULL,
        [FieldName] NVARCHAR(100) NULL,
        [OldValue] NVARCHAR(MAX) NULL,
        [NewValue] NVARCHAR(MAX) NULL,
        [ChangedBy] UNIQUEIDENTIFIER NOT NULL,
        [ChangedAt] DATETIME NOT NULL DEFAULT GETUTCDATE(),
        [Notes] NVARCHAR(MAX) NULL
    );
    PRINT 'ComplianceHistory table created.';
END

-- =============================================
-- 6. Create ComplianceReminders Table
-- =============================================
IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='ComplianceReminders' AND xtype='U')
BEGIN
    CREATE TABLE [dbo].[ComplianceReminders] (
        [ReminderId] UNIQUEIDENTIFIER PRIMARY KEY DEFAULT NEWID(),
        [ComplianceId] UNIQUEIDENTIFIER NOT NULL,
        [ReminderType] NVARCHAR(50) NOT NULL,
        [ReminderDate] DATETIME NOT NULL,
        [DaysBefore] INT NOT NULL,
        [IsSent] BIT NOT NULL DEFAULT 0,
        [SentAt] DATETIME NULL,
        [CreatedAt] DATETIME NOT NULL DEFAULT GETUTCDATE()
    );
    PRINT 'ComplianceReminders table created.';
END

-- =============================================
-- 7. Create Indexes
-- =============================================
IF NOT EXISTS (SELECT * FROM sys.indexes WHERE name = 'IX_Compliance_ClientId')
    CREATE INDEX [IX_Compliance_ClientId] ON [Compliance]([ClientId]);

IF NOT EXISTS (SELECT * FROM sys.indexes WHERE name = 'IX_Compliance_DueDate')
    CREATE INDEX [IX_Compliance_DueDate] ON [Compliance]([DueDate]);

IF NOT EXISTS (SELECT * FROM sys.indexes WHERE name = 'IX_Compliance_Status')
    CREATE INDEX [IX_Compliance_Status] ON [Compliance]([Status]);

IF NOT EXISTS (SELECT * FROM sys.indexes WHERE name = 'IX_Compliance_AssignedTo')
    CREATE INDEX [IX_Compliance_AssignedTo] ON [Compliance]([AssignedTo]);

PRINT 'Indexes created.';

-- =============================================
-- 8. Add Constraints
-- =============================================
IF NOT EXISTS (SELECT * FROM sys.check_constraints WHERE name = 'CK_Compliance_Status')
    ALTER TABLE [Compliance] ADD CONSTRAINT [CK_Compliance_Status] 
    CHECK ([Status] IN ('Pending', 'In Progress', 'Completed', 'Overdue', 'Cancelled'));

IF NOT EXISTS (SELECT * FROM sys.check_constraints WHERE name = 'CK_Compliance_Priority')
    ALTER TABLE [Compliance] ADD CONSTRAINT [CK_Compliance_Priority] 
    CHECK ([Priority] IN ('Low', 'Medium', 'High', 'Critical'));

PRINT 'Constraints added.';

PRINT '';
PRINT 'All tables created successfully!';
PRINT 'Ready for data population.';
