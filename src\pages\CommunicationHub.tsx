
import { useState } from "react";
import { Sidebar } from "@/components/Sidebar";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Badge } from "@/components/ui/badge";
import { Send, Mail, MessageSquare, Clock, CheckCircle, Copy, Plus } from "lucide-react";

const CommunicationHub = () => {
  const [selectedTemplate, setSelectedTemplate] = useState("");
  const [customMessage, setCustomMessage] = useState("");
  const [selectedClients, setSelectedClients] = useState<string[]>([]);

  // Pre-built templates
  const templates = [
    {
      id: "gst-reminder",
      name: "GST Filing Reminder",
      subject: "GST Return Filing Due - Action Required",
      content: `Dear [CLIENT_NAME],

This is a reminder that your GST return filing is due on [DUE_DATE].

Please provide the following documents:
- Sales invoices for the month
- Purchase invoices
- Export/Import documents (if applicable)

You can upload these documents directly to our portal or email them to us.

Best regards,
[CA_NAME]
[FIRM_NAME]`
    },
    {
      id: "tds-reminder",
      name: "TDS Payment Reminder",
      subject: "TDS Payment Due - [DUE_DATE]",
      content: `Dear [CLIENT_NAME],

Your TDS payment for the quarter is due on [DUE_DATE].

Please ensure timely payment to avoid late fees and interest charges.

If you need assistance with the calculation or payment process, please contact us.

Best regards,
[CA_NAME]
[FIRM_NAME]`
    },
    {
      id: "document-request",
      name: "Document Request",
      subject: "Required Documents for [SERVICE_TYPE]",
      content: `Dear [CLIENT_NAME],

We require the following documents to proceed with your [SERVICE_TYPE]:

□ [DOCUMENT_1]
□ [DOCUMENT_2]
□ [DOCUMENT_3]

Please upload these documents to our secure portal or email them to us at your earliest convenience.

Best regards,
[CA_NAME]
[FIRM_NAME]`
    },
    {
      id: "compliance-checklist",
      name: "Monthly Compliance Checklist",
      subject: "Monthly Compliance Checklist - [MONTH] [YEAR]",
      content: `Dear [CLIENT_NAME],

Please find below your monthly compliance checklist:

GST Compliance:
□ GSTR-1 filing
□ GSTR-3B filing
□ Payment of GST dues

TDS Compliance:
□ TDS deduction
□ TDS payment
□ TDS return filing

Other Requirements:
□ [OTHER_REQUIREMENT_1]
□ [OTHER_REQUIREMENT_2]

Please let us know if you need any assistance.

Best regards,
[CA_NAME]
[FIRM_NAME]`
    }
  ];

  // Sample clients
  const clients = [
    { id: 1, name: "ABC Enterprises", email: "<EMAIL>", phone: "+91 98765 43210" },
    { id: 2, name: "XYZ Ltd", email: "<EMAIL>", phone: "+91 98765 43211" },
    { id: 3, name: "Tech Solutions Pvt Ltd", email: "<EMAIL>", phone: "+91 98765 43212" },
    { id: 4, name: "Suresh Kumar", email: "<EMAIL>", phone: "+91 98765 43213" },
    { id: 5, name: "Global Traders", email: "<EMAIL>", phone: "+91 98765 43214" }
  ];

  // Communication log
  const communicationLog = [
    {
      id: 1,
      client: "ABC Enterprises",
      type: "Email",
      subject: "GST Filing Reminder",
      sentDate: "2024-04-05",
      status: "Delivered",
      response: "Documents provided"
    },
    {
      id: 2,
      client: "XYZ Ltd",
      type: "WhatsApp",
      subject: "TDS Payment Reminder",
      sentDate: "2024-04-04",
      status: "Read",
      response: "Payment done"
    },
    {
      id: 3,
      client: "Tech Solutions Pvt Ltd",
      type: "Email",
      subject: "Document Request",
      sentDate: "2024-04-03",
      status: "Pending",
      response: "-"
    }
  ];

  const handleTemplateSelect = (templateId: string) => {
    const template = templates.find(t => t.id === templateId);
    if (template) {
      setSelectedTemplate(templateId);
      setCustomMessage(template.content);
    }
  };

  const handleSendMessage = () => {
    console.log("Sending message to:", selectedClients);
    console.log("Message content:", customMessage);
    // Reset form
    setSelectedClients([]);
    setCustomMessage("");
    setSelectedTemplate("");
  };

  const handleClientToggle = (clientId: string) => {
    setSelectedClients(prev => 
      prev.includes(clientId) 
        ? prev.filter(id => id !== clientId)
        : [...prev, clientId]
    );
  };

  const copyTemplate = (content: string) => {
    navigator.clipboard.writeText(content);
  };

  return (
    <div className="flex min-h-screen bg-gray-50">
      <Sidebar />
      
      <main className="flex-1 ml-64 p-8">
        <div className="max-w-7xl mx-auto">
          {/* Header */}
          <div className="mb-8">
            <h1 className="text-3xl font-bold text-gray-900 mb-2">Client Communication Hub</h1>
            <p className="text-gray-600">Pre-built templates and communication tracking for consistent client messaging</p>
          </div>

          <Tabs defaultValue="compose" className="space-y-6">
            <TabsList className="grid w-full grid-cols-3">
              <TabsTrigger value="compose">Compose Message</TabsTrigger>
              <TabsTrigger value="templates">Manage Templates</TabsTrigger>
              <TabsTrigger value="logs">Communication Logs</TabsTrigger>
            </TabsList>

            {/* Compose Message Tab */}
            <TabsContent value="compose" className="space-y-6">
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                {/* Template Selection */}
                <Card>
                  <CardHeader>
                    <CardTitle>Select Template</CardTitle>
                    <CardDescription>Choose from pre-built communication templates</CardDescription>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    {templates.map((template) => (
                      <div
                        key={template.id}
                        className={`p-4 border rounded-lg cursor-pointer transition-colors ${
                          selectedTemplate === template.id 
                            ? 'border-blue-500 bg-blue-50' 
                            : 'border-gray-200 hover:border-gray-300'
                        }`}
                        onClick={() => handleTemplateSelect(template.id)}
                      >
                        <div className="flex justify-between items-start">
                          <div>
                            <h4 className="font-medium text-gray-900">{template.name}</h4>
                            <p className="text-sm text-gray-600 mt-1">{template.subject}</p>
                          </div>
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={(e) => {
                              e.stopPropagation();
                              copyTemplate(template.content);
                            }}
                          >
                            <Copy className="h-4 w-4" />
                          </Button>
                        </div>
                      </div>
                    ))}
                  </CardContent>
                </Card>

                {/* Client Selection */}
                <Card>
                  <CardHeader>
                    <CardTitle>Select Recipients</CardTitle>
                    <CardDescription>Choose clients to send the message to</CardDescription>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-2 max-h-64 overflow-y-auto">
                      {clients.map((client) => (
                        <div
                          key={client.id}
                          className="flex items-center space-x-3 p-2 rounded-lg hover:bg-gray-50"
                        >
                          <input
                            type="checkbox"
                            id={`client-${client.id}`}
                            checked={selectedClients.includes(client.id.toString())}
                            onChange={() => handleClientToggle(client.id.toString())}
                            className="rounded border-gray-300"
                          />
                          <label htmlFor={`client-${client.id}`} className="flex-1 cursor-pointer">
                            <div>
                              <p className="font-medium text-gray-900">{client.name}</p>
                              <p className="text-sm text-gray-500">{client.email}</p>
                            </div>
                          </label>
                        </div>
                      ))}
                    </div>
                  </CardContent>
                </Card>
              </div>

              {/* Message Composition */}
              <Card>
                <CardHeader>
                  <CardTitle>Compose Message</CardTitle>
                  <CardDescription>Customize your message before sending</CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">Message Content</label>
                    <Textarea
                      value={customMessage}
                      onChange={(e) => setCustomMessage(e.target.value)}
                      placeholder="Type your message here or select a template above..."
                      rows={10}
                      className="w-full"
                    />
                  </div>
                  
                  <div className="flex justify-between items-center">
                    <div className="text-sm text-gray-500">
                      Selected: {selectedClients.length} clients
                    </div>
                    <div className="space-x-4">
                      <Button variant="outline" className="mr-2">
                        <Mail className="h-4 w-4 mr-2" />
                        Send Email
                      </Button>
                      <Button onClick={handleSendMessage} className="bg-green-600 hover:bg-green-700">
                        <MessageSquare className="h-4 w-4 mr-2" />
                        Send WhatsApp
                      </Button>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </TabsContent>

            {/* Templates Tab */}
            <TabsContent value="templates" className="space-y-6">
              <Card>
                <CardHeader>
                  <CardTitle>Message Templates</CardTitle>
                  <CardDescription>Manage your communication templates</CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    {templates.map((template) => (
                      <div key={template.id} className="border rounded-lg p-4">
                        <div className="flex justify-between items-start mb-2">
                          <div>
                            <h4 className="font-medium text-gray-900">{template.name}</h4>
                            <p className="text-sm text-gray-600">{template.subject}</p>
                          </div>
                          <div className="space-x-2">
                            <Button variant="outline" size="sm">Edit</Button>
                            <Button variant="ghost" size="sm" onClick={() => copyTemplate(template.content)}>
                              <Copy className="h-4 w-4" />
                            </Button>
                          </div>
                        </div>
                        <div className="bg-gray-50 p-3 rounded text-sm">
                          <pre className="whitespace-pre-wrap font-sans">{template.content}</pre>
                        </div>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            </TabsContent>

            {/* Communication Logs Tab */}
            <TabsContent value="logs" className="space-y-6">
              <Card>
                <CardHeader>
                  <CardTitle>Communication History</CardTitle>
                  <CardDescription>Track all client communications and responses</CardDescription>
                </CardHeader>
                <CardContent>
                  <Table>
                    <TableHeader>
                      <TableRow>
                        <TableHead>Client</TableHead>
                        <TableHead>Type</TableHead>
                        <TableHead>Subject</TableHead>
                        <TableHead>Sent Date</TableHead>
                        <TableHead>Status</TableHead>
                        <TableHead>Response</TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {communicationLog.map((log) => (
                        <TableRow key={log.id}>
                          <TableCell className="font-medium">{log.client}</TableCell>
                          <TableCell>
                            <Badge variant="outline">
                              {log.type === "Email" ? <Mail className="h-3 w-3 mr-1" /> : <MessageSquare className="h-3 w-3 mr-1" />}
                              {log.type}
                            </Badge>
                          </TableCell>
                          <TableCell>{log.subject}</TableCell>
                          <TableCell>{log.sentDate}</TableCell>
                          <TableCell>
                            <Badge className={
                              log.status === "Delivered" ? "bg-green-100 text-green-800" :
                              log.status === "Read" ? "bg-blue-100 text-blue-800" :
                              "bg-yellow-100 text-yellow-800"
                            }>
                              {log.status}
                            </Badge>
                          </TableCell>
                          <TableCell>{log.response}</TableCell>
                        </TableRow>
                      ))}
                    </TableBody>
                  </Table>
                </CardContent>
              </Card>
            </TabsContent>
          </Tabs>
        </div>
      </main>
    </div>
  );
};

export default CommunicationHub;
