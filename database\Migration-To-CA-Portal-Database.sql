-- =============================================
-- Migration Script: Enhance CA_Portal_kumar_associates for Compliance Calendar
-- Adds missing fields, tables, and stored procedures to existing database
-- =============================================

USE [CA_Portal_kumar_associates];

PRINT '============================================================';
PRINT 'Starting Migration to CA_Portal_kumar_associates Database';
PRINT '============================================================';

-- =============================================
-- 1. Enhance Existing Compliance Table
-- =============================================
PRINT '';
PRINT '1. ENHANCING COMPLIANCE TABLE';
PRINT '------------------------------';

-- Add missing fields to existing Compliance table
IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID('Compliance') AND name = 'EstimatedHours')
BEGIN
    ALTER TABLE Compliance ADD EstimatedHours DECIMAL(5,2) NULL;
    PRINT '✅ Added EstimatedHours column';
END

IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID('Compliance') AND name = 'ActualHours')
BEGIN
    ALTER TABLE Compliance ADD ActualHours DECIMAL(5,2) NULL;
    PRINT '✅ Added ActualHours column';
END

IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID('Compliance') AND name = 'ComplianceYear')
BEGIN
    ALTER TABLE Compliance ADD ComplianceYear INT NULL;
    PRINT '✅ Added ComplianceYear column';
END

IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID('Compliance') AND name = 'RegulatoryBody')
BEGIN
    ALTER TABLE Compliance ADD RegulatoryBody NVARCHAR(100) NULL;
    PRINT '✅ Added RegulatoryBody column';
END

IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID('Compliance') AND name = 'IsRecurring')
BEGIN
    ALTER TABLE Compliance ADD IsRecurring BIT NOT NULL DEFAULT 0;
    PRINT '✅ Added IsRecurring column';
END

IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID('Compliance') AND name = 'RecurrencePattern')
BEGIN
    ALTER TABLE Compliance ADD RecurrencePattern NVARCHAR(50) NULL;
    PRINT '✅ Added RecurrencePattern column';
END

-- =============================================
-- 2. Create Missing Support Tables
-- =============================================
PRINT '';
PRINT '2. CREATING SUPPORT TABLES';
PRINT '---------------------------';

-- ComplianceHistory Table
IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='ComplianceHistory' AND xtype='U')
BEGIN
    CREATE TABLE [dbo].[ComplianceHistory] (
        [HistoryId] UNIQUEIDENTIFIER PRIMARY KEY DEFAULT NEWID(),
        [ComplianceId] UNIQUEIDENTIFIER NOT NULL,
        [Action] NVARCHAR(100) NOT NULL,
        [FieldName] NVARCHAR(100) NULL,
        [OldValue] NVARCHAR(MAX) NULL,
        [NewValue] NVARCHAR(MAX) NULL,
        [ChangedBy] UNIQUEIDENTIFIER NOT NULL,
        [ChangedAt] DATETIME NOT NULL DEFAULT GETUTCDATE(),
        [Notes] NVARCHAR(MAX) NULL,
        [IPAddress] NVARCHAR(45) NULL,
        [UserAgent] NVARCHAR(500) NULL,
        
        CONSTRAINT [FK_ComplianceHistory_Compliance] FOREIGN KEY ([ComplianceId]) REFERENCES [Compliance]([ComplianceId]) ON DELETE CASCADE
    );
    
    CREATE INDEX [IX_ComplianceHistory_ComplianceId] ON [ComplianceHistory]([ComplianceId], [ChangedAt]);
    CREATE INDEX [IX_ComplianceHistory_ChangedAt] ON [ComplianceHistory]([ChangedAt]);
    
    PRINT '✅ ComplianceHistory table created';
END

-- ComplianceReminders Table
IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='ComplianceReminders' AND xtype='U')
BEGIN
    CREATE TABLE [dbo].[ComplianceReminders] (
        [ReminderId] UNIQUEIDENTIFIER PRIMARY KEY DEFAULT NEWID(),
        [ComplianceId] UNIQUEIDENTIFIER NOT NULL,
        [ReminderType] NVARCHAR(50) NOT NULL, -- Email, SMS, InApp
        [ReminderDate] DATETIME NOT NULL,
        [DaysBefore] INT NOT NULL,
        [IsSent] BIT NOT NULL DEFAULT 0,
        [SentAt] DATETIME NULL,
        [SentTo] NVARCHAR(255) NULL,
        [Subject] NVARCHAR(255) NULL,
        [Message] NVARCHAR(MAX) NULL,
        [CreatedAt] DATETIME NOT NULL DEFAULT GETUTCDATE(),
        [CreatedBy] UNIQUEIDENTIFIER NULL,
        
        CONSTRAINT [FK_ComplianceReminders_Compliance] FOREIGN KEY ([ComplianceId]) REFERENCES [Compliance]([ComplianceId]) ON DELETE CASCADE,
        CONSTRAINT [CK_ComplianceReminders_Type] CHECK ([ReminderType] IN ('Email', 'SMS', 'InApp', 'Push'))
    );
    
    CREATE INDEX [IX_ComplianceReminders_ReminderDate] ON [ComplianceReminders]([ReminderDate], [IsSent]);
    
    PRINT '✅ ComplianceReminders table created';
END

-- ComplianceAttachments Table
IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='ComplianceAttachments' AND xtype='U')
BEGIN
    CREATE TABLE [dbo].[ComplianceAttachments] (
        [AttachmentId] UNIQUEIDENTIFIER PRIMARY KEY DEFAULT NEWID(),
        [ComplianceId] UNIQUEIDENTIFIER NOT NULL,
        [FileName] NVARCHAR(255) NOT NULL,
        [OriginalFileName] NVARCHAR(255) NOT NULL,
        [FilePath] NVARCHAR(500) NOT NULL,
        [FileSize] BIGINT NOT NULL,
        [ContentType] NVARCHAR(100) NOT NULL,
        [Description] NVARCHAR(500) NULL,
        [UploadedBy] UNIQUEIDENTIFIER NOT NULL,
        [UploadedAt] DATETIME NOT NULL DEFAULT GETUTCDATE(),
        [IsActive] BIT NOT NULL DEFAULT 1,
        
        CONSTRAINT [FK_ComplianceAttachments_Compliance] FOREIGN KEY ([ComplianceId]) REFERENCES [Compliance]([ComplianceId]) ON DELETE CASCADE
    );
    
    CREATE INDEX [IX_ComplianceAttachments_ComplianceId] ON [ComplianceAttachments]([ComplianceId], [IsActive]);
    
    PRINT '✅ ComplianceAttachments table created';
END

-- ComplianceComments Table
IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='ComplianceComments' AND xtype='U')
BEGIN
    CREATE TABLE [dbo].[ComplianceComments] (
        [CommentId] UNIQUEIDENTIFIER PRIMARY KEY DEFAULT NEWID(),
        [ComplianceId] UNIQUEIDENTIFIER NOT NULL,
        [Comment] NVARCHAR(MAX) NOT NULL,
        [CommentType] NVARCHAR(50) NOT NULL DEFAULT 'General', -- General, StatusUpdate, Internal
        [CreatedBy] UNIQUEIDENTIFIER NOT NULL,
        [CreatedAt] DATETIME NOT NULL DEFAULT GETUTCDATE(),
        [IsInternal] BIT NOT NULL DEFAULT 0,
        [ParentCommentId] UNIQUEIDENTIFIER NULL, -- For threaded comments
        
        CONSTRAINT [FK_ComplianceComments_Compliance] FOREIGN KEY ([ComplianceId]) REFERENCES [Compliance]([ComplianceId]) ON DELETE CASCADE,
        CONSTRAINT [FK_ComplianceComments_Parent] FOREIGN KEY ([ParentCommentId]) REFERENCES [ComplianceComments]([CommentId]),
        CONSTRAINT [CK_ComplianceComments_Type] CHECK ([CommentType] IN ('General', 'StatusUpdate', 'Internal', 'ClientNote'))
    );
    
    CREATE INDEX [IX_ComplianceComments_ComplianceId] ON [ComplianceComments]([ComplianceId], [CreatedAt]);
    
    PRINT '✅ ComplianceComments table created';
END

-- =============================================
-- 3. Add Performance Indexes
-- =============================================
PRINT '';
PRINT '3. ADDING PERFORMANCE INDEXES';
PRINT '------------------------------';

-- Compliance table indexes
IF NOT EXISTS (SELECT * FROM sys.indexes WHERE name = 'IX_Compliance_ClientId_Enhanced')
BEGIN
    CREATE INDEX [IX_Compliance_ClientId_Enhanced] ON [Compliance]([ClientId]) INCLUDE ([Status], [Priority], [DueDate]);
    PRINT '✅ Enhanced ClientId index created';
END

IF NOT EXISTS (SELECT * FROM sys.indexes WHERE name = 'IX_Compliance_DueDate_Enhanced')
BEGIN
    CREATE INDEX [IX_Compliance_DueDate_Enhanced] ON [Compliance]([DueDate]) INCLUDE ([Status], [Priority], [ClientId]);
    PRINT '✅ Enhanced DueDate index created';
END

IF NOT EXISTS (SELECT * FROM sys.indexes WHERE name = 'IX_Compliance_Status_Enhanced')
BEGIN
    CREATE INDEX [IX_Compliance_Status_Enhanced] ON [Compliance]([Status]) INCLUDE ([DueDate], [Priority], [ClientId]);
    PRINT '✅ Enhanced Status index created';
END

IF NOT EXISTS (SELECT * FROM sys.indexes WHERE name = 'IX_Compliance_AssignedTo_Enhanced')
BEGIN
    CREATE INDEX [IX_Compliance_AssignedTo_Enhanced] ON [Compliance]([AssignedTo]) INCLUDE ([Status], [DueDate], [Priority]);
    PRINT '✅ Enhanced AssignedTo index created';
END

-- =============================================
-- 4. Add Constraints and Validation
-- =============================================
PRINT '';
PRINT '4. ADDING CONSTRAINTS';
PRINT '---------------------';

-- Add check constraints if they don't exist
IF NOT EXISTS (SELECT * FROM sys.check_constraints WHERE name = 'CK_Compliance_Status_Enhanced')
BEGIN
    ALTER TABLE [Compliance] ADD CONSTRAINT [CK_Compliance_Status_Enhanced] 
    CHECK ([Status] IN ('Pending', 'In Progress', 'Completed', 'Overdue', 'Cancelled'));
    PRINT '✅ Status constraint added';
END

IF NOT EXISTS (SELECT * FROM sys.check_constraints WHERE name = 'CK_Compliance_Priority_Enhanced')
BEGIN
    ALTER TABLE [Compliance] ADD CONSTRAINT [CK_Compliance_Priority_Enhanced] 
    CHECK ([Priority] IN ('Low', 'Medium', 'High', 'Critical'));
    PRINT '✅ Priority constraint added';
END

IF NOT EXISTS (SELECT * FROM sys.check_constraints WHERE name = 'CK_Compliance_RecurrencePattern')
BEGIN
    ALTER TABLE [Compliance] ADD CONSTRAINT [CK_Compliance_RecurrencePattern] 
    CHECK ([RecurrencePattern] IN ('Monthly', 'Quarterly', 'Half-Yearly', 'Annually') OR [RecurrencePattern] IS NULL);
    PRINT '✅ RecurrencePattern constraint added';
END

-- =============================================
-- 5. Create Stored Procedures
-- =============================================
PRINT '';
PRINT '5. CREATING STORED PROCEDURES';
PRINT '------------------------------';

-- Drop existing procedures if they exist
IF EXISTS (SELECT * FROM sys.procedures WHERE name = 'sp_GetComplianceStats')
    DROP PROCEDURE sp_GetComplianceStats;

IF EXISTS (SELECT * FROM sys.procedures WHERE name = 'sp_GetComplianceItems')
    DROP PROCEDURE sp_GetComplianceItems;

IF EXISTS (SELECT * FROM sys.procedures WHERE name = 'sp_GetComplianceCalendar')
    DROP PROCEDURE sp_GetComplianceCalendar;

IF EXISTS (SELECT * FROM sys.procedures WHERE name = 'sp_CreateComplianceItem')
    DROP PROCEDURE sp_CreateComplianceItem;

PRINT '✅ Existing procedures dropped';

PRINT '';
PRINT '============================================================';
PRINT 'Database Enhancement Complete!';
PRINT 'Ready for stored procedures creation...';
PRINT '============================================================';
