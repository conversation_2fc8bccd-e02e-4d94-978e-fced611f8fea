
import { useState } from "react";
import { Sidebar } from "@/components/Sidebar";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Badge } from "@/components/ui/badge";
import { Upload, Search, FolderOpen, FileText, Download, Eye, Trash2, Filter } from "lucide-react";
import { useToast } from "@/hooks/use-toast";

const DocumentVault = () => {
  const [selectedFiles, setSelectedFiles] = useState<FileList | null>(null);
  const [selectedClient, setSelectedClient] = useState("");
  const [documentType, setDocumentType] = useState("");
  const [searchQuery, setSearchQuery] = useState("");
  const [filterType, setFilterType] = useState("all");
  const { toast } = useToast();

  const handleFileUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    const files = event.target.files;
    setSelectedFiles(files);
  };

  const handleUploadSubmit = () => {
    if (!selectedFiles || !selectedClient || !documentType) {
      toast({
        title: "Missing Information",
        description: "Please select files, client, and document type.",
        variant: "destructive",
      });
      return;
    }

    toast({
      title: "Documents Uploaded Successfully",
      description: `${selectedFiles.length} document(s) uploaded and auto-categorized for ${selectedClient}`,
    });

    // Reset form
    setSelectedFiles(null);
    setSelectedClient("");
    setDocumentType("");
    const fileInput = document.getElementById('document-upload') as HTMLInputElement;
    if (fileInput) fileInput.value = '';
  };

  const documents = [
    { id: 1, name: "Form 16 - FY 2023-24.pdf", client: "ABC Enterprises", type: "Tax Document", size: "2.3 MB", uploadDate: "2024-06-01", category: "Income Tax" },
    { id: 2, name: "GSTR-1 May 2024.xlsx", client: "XYZ Corp", type: "GST Return", size: "1.8 MB", uploadDate: "2024-06-15", category: "GST" },
    { id: 3, name: "Bank Statement May 2024.pdf", client: "DEF Limited", type: "Financial Statement", size: "3.1 MB", uploadDate: "2024-06-10", category: "Banking" },
    { id: 4, name: "PAN Card - John Doe.pdf", client: "John Doe", type: "Identity Document", size: "0.5 MB", uploadDate: "2024-01-15", category: "Identity" },
    { id: 5, name: "Audit Report FY 2023-24.pdf", client: "ABC Enterprises", type: "Audit Report", size: "5.2 MB", uploadDate: "2024-05-20", category: "Audit" },
    { id: 6, name: "TDS Certificate Q1 2024.pdf", client: "XYZ Corp", type: "TDS Certificate", size: "1.2 MB", uploadDate: "2024-04-30", category: "TDS" },
  ];

  const clients = [
    "ABC Enterprises", "XYZ Corp", "DEF Limited", "John Doe", "Jane Smith", "GHI Industries"
  ];

  const documentTypes = [
    { value: "form16", label: "Form 16" },
    { value: "gstr", label: "GST Return" },
    { value: "tds-cert", label: "TDS Certificate" },
    { value: "bank-statement", label: "Bank Statement" },
    { value: "pan-card", label: "PAN Card" },
    { value: "audit-report", label: "Audit Report" },
    { value: "itr", label: "ITR" },
    { value: "balance-sheet", label: "Balance Sheet" },
    { value: "p-l-statement", label: "P&L Statement" },
    { value: "other", label: "Other" },
  ];

  const categories = ["All", "Income Tax", "GST", "TDS", "Banking", "Identity", "Audit", "Financial"];

  const filteredDocuments = documents.filter(doc => {
    const matchesSearch = doc.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         doc.client.toLowerCase().includes(searchQuery.toLowerCase());
    const matchesFilter = filterType === "all" || doc.category.toLowerCase() === filterType.toLowerCase();
    return matchesSearch && matchesFilter;
  });

  const getCategoryColor = (category: string) => {
    const colors: { [key: string]: string } = {
      "Income Tax": "bg-blue-100 text-blue-800",
      "GST": "bg-green-100 text-green-800",
      "TDS": "bg-purple-100 text-purple-800",
      "Banking": "bg-orange-100 text-orange-800",
      "Identity": "bg-red-100 text-red-800",
      "Audit": "bg-indigo-100 text-indigo-800",
      "Financial": "bg-yellow-100 text-yellow-800",
    };
    return colors[category] || "bg-gray-100 text-gray-800";
  };

  return (
    <div className="flex min-h-screen bg-gray-50">
      <Sidebar />
      
      <main className="flex-1 ml-64 p-8">
        <div className="max-w-7xl mx-auto">
          {/* Header */}
          <div className="mb-8">
            <h1 className="text-3xl font-bold text-gray-900 mb-2">
              Document Vault
            </h1>
            <p className="text-gray-600 text-lg">
              Auto-sorted document storage with smart categorization and search
            </p>
          </div>

          {/* Stats Cards */}
          <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
            <Card>
              <CardContent className="p-6">
                <div className="flex items-center">
                  <div className="p-2 bg-blue-100 rounded-lg">
                    <FileText className="h-6 w-6 text-blue-600" />
                  </div>
                  <div className="ml-4">
                    <p className="text-sm font-medium text-gray-600">Total Documents</p>
                    <p className="text-2xl font-bold text-gray-900">1,247</p>
                  </div>
                </div>
              </CardContent>
            </Card>
            
            <Card>
              <CardContent className="p-6">
                <div className="flex items-center">
                  <div className="p-2 bg-green-100 rounded-lg">
                    <FolderOpen className="h-6 w-6 text-green-600" />
                  </div>
                  <div className="ml-4">
                    <p className="text-sm font-medium text-gray-600">Categories</p>
                    <p className="text-2xl font-bold text-gray-900">8</p>
                  </div>
                </div>
              </CardContent>
            </Card>
            
            <Card>
              <CardContent className="p-6">
                <div className="flex items-center">
                  <div className="p-2 bg-purple-100 rounded-lg">
                    <Upload className="h-6 w-6 text-purple-600" />
                  </div>
                  <div className="ml-4">
                    <p className="text-sm font-medium text-gray-600">This Month</p>
                    <p className="text-2xl font-bold text-gray-900">156</p>
                  </div>
                </div>
              </CardContent>
            </Card>
            
            <Card>
              <CardContent className="p-6">
                <div className="flex items-center">
                  <div className="p-2 bg-orange-100 rounded-lg">
                    <FileText className="h-6 w-6 text-orange-600" />
                  </div>
                  <div className="ml-4">
                    <p className="text-sm font-medium text-gray-600">Storage Used</p>
                    <p className="text-2xl font-bold text-gray-900">2.3 GB</p>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>

          <Tabs defaultValue="upload" className="w-full">
            <TabsList className="grid w-full grid-cols-2">
              <TabsTrigger value="upload">Upload Documents</TabsTrigger>
              <TabsTrigger value="vault">Document Vault</TabsTrigger>
            </TabsList>
            
            <TabsContent value="upload">
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center">
                    <Upload className="mr-2 h-5 w-5" />
                    Upload New Documents
                  </CardTitle>
                  <CardDescription>
                    Upload documents and they'll be auto-categorized by type and client
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-6">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div className="space-y-2">
                      <Label htmlFor="client-select">Select Client</Label>
                      <Select value={selectedClient} onValueChange={setSelectedClient}>
                        <SelectTrigger>
                          <SelectValue placeholder="Choose client" />
                        </SelectTrigger>
                        <SelectContent>
                          {clients.map((client) => (
                            <SelectItem key={client} value={client}>{client}</SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    </div>

                    <div className="space-y-2">
                      <Label htmlFor="document-type">Document Type</Label>
                      <Select value={documentType} onValueChange={setDocumentType}>
                        <SelectTrigger>
                          <SelectValue placeholder="Select document type" />
                        </SelectTrigger>
                        <SelectContent>
                          {documentTypes.map((type) => (
                            <SelectItem key={type.value} value={type.value}>{type.label}</SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    </div>
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="document-upload">Upload Files</Label>
                    <div className="border-2 border-dashed border-gray-300 rounded-lg p-8 text-center">
                      <Upload className="mx-auto h-12 w-12 text-gray-400" />
                      <div className="mt-4">
                        <label htmlFor="document-upload" className="cursor-pointer">
                          <span className="text-blue-600 hover:text-blue-500 font-medium">Click to upload</span>
                          <span className="text-gray-500"> or drag and drop</span>
                        </label>
                        <input
                          id="document-upload"
                          type="file"
                          multiple
                          accept=".pdf,.xlsx,.xls,.csv,.jpg,.jpeg,.png"
                          className="hidden"
                          onChange={handleFileUpload}
                        />
                      </div>
                      <p className="text-sm text-gray-500 mt-2">
                        PDF, Excel, Images up to 10MB each
                      </p>
                    </div>
                    {selectedFiles && (
                      <div className="mt-4 p-4 bg-gray-50 rounded-lg">
                        <p className="font-medium text-gray-900 mb-2">
                          {selectedFiles.length} file(s) selected:
                        </p>
                        {Array.from(selectedFiles).map((file, index) => (
                          <div key={index} className="flex justify-between items-center py-1">
                            <span className="text-sm text-gray-600">{file.name}</span>
                            <span className="text-xs text-gray-500">
                              {(file.size / 1024 / 1024).toFixed(2)} MB
                            </span>
                          </div>
                        ))}
                      </div>
                    )}
                  </div>

                  <Button onClick={handleUploadSubmit} className="w-full">
                    <Upload className="mr-2 h-4 w-4" />
                    Upload and Auto-Categorize
                  </Button>
                </CardContent>
              </Card>
            </TabsContent>
            
            <TabsContent value="vault">
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center">
                    <FolderOpen className="mr-2 h-5 w-5" />
                    Document Vault
                  </CardTitle>
                  <CardDescription>
                    Search and manage all uploaded documents
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  {/* Search and Filter */}
                  <div className="flex flex-col sm:flex-row gap-4 mb-6">
                    <div className="relative flex-1">
                      <Search className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
                      <Input
                        placeholder="Search documents or clients..."
                        value={searchQuery}
                        onChange={(e) => setSearchQuery(e.target.value)}
                        className="pl-10"
                      />
                    </div>
                    <Select value={filterType} onValueChange={setFilterType}>
                      <SelectTrigger className="w-full sm:w-48">
                        <Filter className="mr-2 h-4 w-4" />
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        {categories.map((category) => (
                          <SelectItem key={category.toLowerCase()} value={category.toLowerCase()}>
                            {category}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>

                  {/* Documents Grid */}
                  <div className="space-y-4">
                    {filteredDocuments.map((document) => (
                      <div key={document.id} className="flex items-center justify-between p-4 bg-white border rounded-lg hover:shadow-md transition-shadow">
                        <div className="flex items-center space-x-4">
                          <FileText className="h-8 w-8 text-gray-400" />
                          <div>
                            <p className="font-medium text-gray-900">{document.name}</p>
                            <p className="text-sm text-gray-500">{document.client} • {document.size}</p>
                            <div className="flex items-center space-x-2 mt-1">
                              <Badge className={getCategoryColor(document.category)}>
                                {document.category}
                              </Badge>
                              <span className="text-xs text-gray-400">{document.uploadDate}</span>
                            </div>
                          </div>
                        </div>
                        <div className="flex items-center space-x-2">
                          <Button variant="ghost" size="sm">
                            <Eye className="h-4 w-4" />
                          </Button>
                          <Button variant="ghost" size="sm">
                            <Download className="h-4 w-4" />
                          </Button>
                          <Button variant="ghost" size="sm" className="text-red-600 hover:text-red-700">
                            <Trash2 className="h-4 w-4" />
                          </Button>
                        </div>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            </TabsContent>
          </Tabs>
        </div>
      </main>
    </div>
  );
};

export default DocumentVault;
