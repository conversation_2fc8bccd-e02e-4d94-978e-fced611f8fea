{"format": 1, "restore": {"D:\\DataOps Sync\\Projects\\CA-Portal-main\\backend-api\\CAPortalAPI.csproj": {}}, "projects": {"D:\\DataOps Sync\\Projects\\CA-Portal-main\\backend-api\\CAPortalAPI.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\DataOps Sync\\Projects\\CA-Portal-main\\backend-api\\CAPortalAPI.csproj", "projectName": "CAPortalAPI", "projectPath": "D:\\DataOps Sync\\Projects\\CA-Portal-main\\backend-api\\CAPortalAPI.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\DataOps Sync\\Projects\\CA-Portal-main\\backend-api\\obj\\", "projectStyle": "PackageReference", "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config"], "originalTargetFrameworks": ["net8.0"], "sources": {"https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "dependencies": {"Microsoft.AspNetCore.Authentication.JwtBearer": {"target": "Package", "version": "[8.0.11, )"}, "Microsoft.Data.SqlClient": {"target": "Package", "version": "[5.2.2, )"}, "Microsoft.IdentityModel.Tokens": {"target": "Package", "version": "[8.2.1, )"}, "Newtonsoft.Json": {"target": "Package", "version": "[13.0.3, )"}, "Swashbuckle.AspNetCore": {"target": "Package", "version": "[6.8.1, )"}, "System.IdentityModel.Tokens.Jwt": {"target": "Package", "version": "[8.2.1, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.AspNetCore.App": {"privateAssets": "none"}, "Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\8.0.411/PortableRuntimeIdentifierGraph.json"}}}}}