
import { useState } from "react";
import { Sidebar } from "@/components/Sidebar";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Upload, FileText, TrendingUp, TrendingDown, DollarSign, AlertCircle, Download, Eye } from "lucide-react";
import { useToast } from "@/hooks/use-toast";

const ReportGenerator = () => {
  const [selectedClient, setSelectedClient] = useState("");
  const [reportType, setReportType] = useState("");
  const [uploadedFile, setUploadedFile] = useState<File | null>(null);
  const { toast } = useToast();

  const handleFileUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      setUploadedFile(file);
      toast({
        title: "File Uploaded",
        description: `${file.name} uploaded successfully.`,
      });
    }
  };

  const handleGenerateReport = () => {
    if (!selectedClient || !reportType || !uploadedFile) {
      toast({
        title: "Missing Information",
        description: "Please select client, report type, and upload a file.",
        variant: "destructive",
      });
      return;
    }

    toast({
      title: "Report Generated Successfully",
      description: `${reportType} report generated for ${selectedClient}.`,
    });
  };

  const recentReports = [
    { id: 1, client: "ABC Enterprises", type: "Monthly KPI", date: "2024-06-01", status: "Ready" },
    { id: 2, client: "XYZ Ltd", type: "Tax Summary", date: "2024-05-30", status: "Processing" },
    { id: 3, client: "PQR Corp", type: "Financial Overview", date: "2024-05-28", status: "Ready" },
    { id: 4, client: "MNO Industries", type: "Compliance Report", date: "2024-05-25", status: "Ready" },
  ];

  const sampleKPIs = {
    totalSales: "₹15,42,000",
    totalExpenses: "₹8,76,000",
    netProfit: "₹6,66,000",
    taxesPaid: "₹1,89,000",
    pendingDues: "₹45,000",
    profitMargin: "43.2%"
  };

  return (
    <div className="flex min-h-screen bg-gray-50">
      <Sidebar />
      
      <main className="flex-1 ml-64 p-8">
        <div className="max-w-7xl mx-auto">
          {/* Header */}
          <div className="mb-8">
            <h1 className="text-3xl font-bold text-gray-900 mb-2">
              Auto Report Generator
            </h1>
            <p className="text-gray-600 text-lg">
              Upload financials → instant KPI reports (sales, expenses, taxes, dues)
            </p>
          </div>

          {/* Stats Cards */}
          <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
            <Card>
              <CardContent className="p-6">
                <div className="flex items-center">
                  <div className="p-2 bg-blue-100 rounded-lg">
                    <FileText className="h-6 w-6 text-blue-600" />
                  </div>
                  <div className="ml-4">
                    <p className="text-sm font-medium text-gray-600">Reports Generated</p>
                    <p className="text-2xl font-bold text-gray-900">127</p>
                  </div>
                </div>
              </CardContent>
            </Card>
            
            <Card>
              <CardContent className="p-6">
                <div className="flex items-center">
                  <div className="p-2 bg-green-100 rounded-lg">
                    <Upload className="h-6 w-6 text-green-600" />
                  </div>
                  <div className="ml-4">
                    <p className="text-sm font-medium text-gray-600">Files Processed</p>
                    <p className="text-2xl font-bold text-gray-900">234</p>
                  </div>
                </div>
              </CardContent>
            </Card>
            
            <Card>
              <CardContent className="p-6">
                <div className="flex items-center">
                  <div className="p-2 bg-orange-100 rounded-lg">
                    <TrendingUp className="h-6 w-6 text-orange-600" />
                  </div>
                  <div className="ml-4">
                    <p className="text-sm font-medium text-gray-600">Time Saved</p>
                    <p className="text-2xl font-bold text-gray-900">89hrs</p>
                  </div>
                </div>
              </CardContent>
            </Card>
            
            <Card>
              <CardContent className="p-6">
                <div className="flex items-center">
                  <div className="p-2 bg-purple-100 rounded-lg">
                    <AlertCircle className="h-6 w-6 text-purple-600" />
                  </div>
                  <div className="ml-4">
                    <p className="text-sm font-medium text-gray-600">Pending Reviews</p>
                    <p className="text-2xl font-bold text-gray-900">12</p>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>

          <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8">
            {/* Upload and Generate */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center">
                  <Upload className="mr-2 h-5 w-5" />
                  Generate New Report
                </CardTitle>
                <CardDescription>
                  Upload client financials to generate instant KPI reports
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-6">
                <div className="space-y-2">
                  <Label htmlFor="client">Select Client</Label>
                  <Select value={selectedClient} onValueChange={setSelectedClient}>
                    <SelectTrigger>
                      <SelectValue placeholder="Choose client" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="abc-enterprises">ABC Enterprises</SelectItem>
                      <SelectItem value="xyz-ltd">XYZ Ltd</SelectItem>
                      <SelectItem value="pqr-corp">PQR Corp</SelectItem>
                      <SelectItem value="mno-industries">MNO Industries</SelectItem>
                      <SelectItem value="def-company">DEF Company</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="reportType">Report Type</Label>
                  <Select value={reportType} onValueChange={setReportType}>
                    <SelectTrigger>
                      <SelectValue placeholder="Select report type" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="monthly-kpi">Monthly KPI Report</SelectItem>
                      <SelectItem value="quarterly-summary">Quarterly Summary</SelectItem>
                      <SelectItem value="tax-summary">Tax Summary Report</SelectItem>
                      <SelectItem value="financial-overview">Financial Overview</SelectItem>
                      <SelectItem value="compliance-report">Compliance Report</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="fileUpload">Upload Financial Data</Label>
                  <div className="border-2 border-dashed border-gray-300 rounded-lg p-6 text-center">
                    <Upload className="mx-auto h-12 w-12 text-gray-400" />
                    <div className="mt-4">
                      <Label htmlFor="fileUpload" className="cursor-pointer">
                        <span className="mt-2 block text-sm font-medium text-gray-900">
                          Drop files here or click to upload
                        </span>
                        <span className="mt-1 block text-sm text-gray-500">
                          Supports Excel, CSV, PDF (Max 10MB)
                        </span>
                      </Label>
                      <Input
                        id="fileUpload"
                        type="file"
                        className="hidden"
                        accept=".xlsx,.xls,.csv,.pdf"
                        onChange={handleFileUpload}
                      />
                    </div>
                  </div>
                  {uploadedFile && (
                    <p className="text-sm text-green-600 mt-2">
                      ✓ {uploadedFile.name} uploaded successfully
                    </p>
                  )}
                </div>

                <Button onClick={handleGenerateReport} className="w-full" size="lg">
                  <FileText className="mr-2 h-4 w-4" />
                  Generate Report
                </Button>
              </CardContent>
            </Card>

            {/* Sample KPI Preview */}
            <Card>
              <CardHeader>
                <CardTitle>Sample KPI Dashboard</CardTitle>
                <CardDescription>
                  Preview of auto-generated KPIs
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="grid grid-cols-2 gap-4">
                    <div className="bg-green-50 p-4 rounded-lg">
                      <div className="flex items-center justify-between">
                        <span className="text-sm font-medium text-green-800">Total Sales</span>
                        <TrendingUp className="h-4 w-4 text-green-600" />
                      </div>
                      <p className="text-2xl font-bold text-green-900">{sampleKPIs.totalSales}</p>
                    </div>
                    
                    <div className="bg-red-50 p-4 rounded-lg">
                      <div className="flex items-center justify-between">
                        <span className="text-sm font-medium text-red-800">Total Expenses</span>
                        <TrendingDown className="h-4 w-4 text-red-600" />
                      </div>
                      <p className="text-2xl font-bold text-red-900">{sampleKPIs.totalExpenses}</p>
                    </div>
                    
                    <div className="bg-blue-50 p-4 rounded-lg">
                      <div className="flex items-center justify-between">
                        <span className="text-sm font-medium text-blue-800">Net Profit</span>
                        <DollarSign className="h-4 w-4 text-blue-600" />
                      </div>
                      <p className="text-2xl font-bold text-blue-900">{sampleKPIs.netProfit}</p>
                    </div>
                    
                    <div className="bg-purple-50 p-4 rounded-lg">
                      <div className="flex items-center justify-between">
                        <span className="text-sm font-medium text-purple-800">Profit Margin</span>
                        <TrendingUp className="h-4 w-4 text-purple-600" />
                      </div>
                      <p className="text-2xl font-bold text-purple-900">{sampleKPIs.profitMargin}</p>
                    </div>
                  </div>
                  
                  <div className="pt-4 border-t">
                    <div className="flex justify-between items-center mb-2">
                      <span className="text-sm font-medium text-gray-600">Taxes Paid</span>
                      <span className="text-lg font-semibold">{sampleKPIs.taxesPaid}</span>
                    </div>
                    <div className="flex justify-between items-center">
                      <span className="text-sm font-medium text-gray-600">Pending Dues</span>
                      <span className="text-lg font-semibold text-orange-600">{sampleKPIs.pendingDues}</span>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Recent Reports */}
          <Card>
            <CardHeader>
              <CardTitle>Recent Reports</CardTitle>
              <CardDescription>
                Latest generated reports and their status
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {recentReports.map((report) => (
                  <div key={report.id} className="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
                    <div className="flex items-center space-x-4">
                      <div className="p-2 bg-blue-100 rounded-lg">
                        <FileText className="h-5 w-5 text-blue-600" />
                      </div>
                      <div>
                        <p className="font-medium text-gray-900">{report.client}</p>
                        <p className="text-sm text-gray-500">{report.type} • {report.date}</p>
                      </div>
                    </div>
                    <div className="flex items-center space-x-3">
                      <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                        report.status === "Ready" 
                          ? "bg-green-100 text-green-800" 
                          : "bg-orange-100 text-orange-800"
                      }`}>
                        {report.status}
                      </span>
                      {report.status === "Ready" && (
                        <div className="flex space-x-2">
                          <Button size="sm" variant="outline">
                            <Eye className="h-4 w-4 mr-1" />
                            View
                          </Button>
                          <Button size="sm">
                            <Download className="h-4 w-4 mr-1" />
                            Download
                          </Button>
                        </div>
                      )}
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </div>
      </main>
    </div>
  );
};

export default ReportGenerator;
