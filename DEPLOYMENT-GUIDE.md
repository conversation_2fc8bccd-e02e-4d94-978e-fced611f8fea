# 🚀 Compliance Management System - Deployment Guide

## 📋 Overview

This guide provides step-by-step instructions for deploying the enhanced Compliance Management System to production.

## 🏗️ Architecture Overview

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Frontend      │    │   Backend API   │    │   Database      │
│   (React)       │◄──►│   (.NET Core)   │◄──►│   (SQL Server)  │
│   Port: 8081    │    │   Port: 7000    │    │   Multi-tenant  │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

## 🎯 Pre-Deployment Checklist

### ✅ **Backend Requirements**
- [ ] .NET Core 6.0+ installed
- [ ] SQL Server 2019+ or Azure SQL Database
- [ ] IIS or hosting environment configured
- [ ] SSL certificates for HTTPS
- [ ] Environment variables configured

### ✅ **Frontend Requirements**
- [ ] Node.js 18+ installed
- [ ] Build tools configured
- [ ] Web server (IIS, Nginx, or Apache)
- [ ] CDN configuration (optional)

### ✅ **Database Requirements**
- [ ] SQL Server instance accessible
- [ ] Database backups configured
- [ ] Connection strings secured
- [ ] Stored procedures deployed

## 🗄️ Database Deployment

### Step 1: Create Database Structure

```sql
-- 1. Run the main setup script
sqlcmd -S [ServerName] -d [DatabaseName] -i "database/setup-compliance-database.sql"

-- 2. Run the stored procedures script
sqlcmd -S [ServerName] -d [DatabaseName] -i "database/stored-procedures/compliance-procedures.sql"

-- 3. Verify installation
SELECT COUNT(*) FROM Compliance;
SELECT COUNT(*) FROM ComplianceTypes;
```

### Step 2: Configure Connection Strings

Update `appsettings.Production.json`:

```json
{
  "ConnectionStrings": {
    "TenantRegistry": "Server=[MasterServer];Database=[TenantRegistry];Trusted_Connection=true;",
    "TenantConnection": "Server=[TenantServer];Database=[TenantDB];Trusted_Connection=true;"
  },
  "JwtSettings": {
    "SecretKey": "[YourSecretKey]",
    "Issuer": "[YourIssuer]",
    "Audience": "[YourAudience]",
    "ExpirationMinutes": 60
  },
  "Logging": {
    "LogLevel": {
      "Default": "Information",
      "Microsoft.AspNetCore": "Warning"
    }
  }
}
```

## 🔧 Backend API Deployment

### Step 1: Build and Publish

```bash
# Navigate to backend directory
cd backend-api

# Restore packages
dotnet restore

# Build in release mode
dotnet build --configuration Release

# Publish for deployment
dotnet publish --configuration Release --output ./publish
```

### Step 2: Deploy to IIS

1. **Create Application Pool**
   - Name: `CAPortalAPI`
   - .NET CLR Version: `No Managed Code`
   - Managed Pipeline Mode: `Integrated`

2. **Create Website**
   - Site Name: `CA Portal API`
   - Physical Path: `C:\inetpub\wwwroot\ca-portal-api`
   - Port: `443` (HTTPS)
   - SSL Certificate: Install and bind

3. **Copy Files**
   ```bash
   xcopy /s /e ".\publish\*" "C:\inetpub\wwwroot\ca-portal-api\"
   ```

### Step 3: Configure Environment

Create `web.config`:

```xml
<?xml version="1.0" encoding="utf-8"?>
<configuration>
  <location path="." inheritInChildApplications="false">
    <system.webServer>
      <handlers>
        <add name="aspNetCore" path="*" verb="*" modules="AspNetCoreModuleV2" resourceType="Unspecified" />
      </handlers>
      <aspNetCore processPath="dotnet" arguments=".\CAPortalAPI.dll" stdoutLogEnabled="false" stdoutLogFile=".\logs\stdout" hostingModel="inprocess" />
    </system.webServer>
  </location>
</configuration>
```

## 🎨 Frontend Deployment

### Step 1: Build for Production

```bash
# Navigate to frontend directory
cd frontend

# Install dependencies
npm install

# Build for production
npm run build

# The build files will be in the 'dist' directory
```

### Step 2: Deploy to Web Server

#### Option A: IIS Deployment

1. **Create Website**
   - Site Name: `CA Portal Frontend`
   - Physical Path: `C:\inetpub\wwwroot\ca-portal`
   - Port: `443` (HTTPS)

2. **Copy Build Files**
   ```bash
   xcopy /s /e ".\dist\*" "C:\inetpub\wwwroot\ca-portal\"
   ```

3. **Configure URL Rewrite** (for SPA routing)
   ```xml
   <configuration>
     <system.webServer>
       <rewrite>
         <rules>
           <rule name="React Routes" stopProcessing="true">
             <match url=".*" />
             <conditions logicalGrouping="MatchAll">
               <add input="{REQUEST_FILENAME}" matchType="IsFile" negate="true" />
               <add input="{REQUEST_FILENAME}" matchType="IsDirectory" negate="true" />
               <add input="{REQUEST_URI}" pattern="^/(api)" negate="true" />
             </conditions>
             <action type="Rewrite" url="/" />
           </rule>
         </rules>
       </rewrite>
     </system.webServer>
   </configuration>
   ```

#### Option B: Nginx Deployment

```nginx
server {
    listen 443 ssl;
    server_name your-domain.com;
    
    ssl_certificate /path/to/certificate.crt;
    ssl_certificate_key /path/to/private.key;
    
    root /var/www/ca-portal;
    index index.html;
    
    location / {
        try_files $uri $uri/ /index.html;
    }
    
    location /api {
        proxy_pass https://localhost:7000;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
}
```

## 🔐 Security Configuration

### Step 1: SSL/TLS Setup

1. **Obtain SSL Certificate**
   - Use Let's Encrypt for free certificates
   - Or purchase from a trusted CA

2. **Configure HTTPS Redirect**
   ```csharp
   // In Program.cs
   app.UseHttpsRedirection();
   app.UseHsts();
   ```

### Step 2: CORS Configuration

```csharp
// In Program.cs
builder.Services.AddCors(options =>
{
    options.AddPolicy("ProductionPolicy", policy =>
    {
        policy.WithOrigins("https://your-domain.com")
              .AllowAnyMethod()
              .AllowAnyHeader()
              .AllowCredentials();
    });
});

app.UseCors("ProductionPolicy");
```

### Step 3: API Security Headers

```csharp
app.Use(async (context, next) =>
{
    context.Response.Headers.Add("X-Content-Type-Options", "nosniff");
    context.Response.Headers.Add("X-Frame-Options", "DENY");
    context.Response.Headers.Add("X-XSS-Protection", "1; mode=block");
    context.Response.Headers.Add("Referrer-Policy", "strict-origin-when-cross-origin");
    await next();
});
```

## 📊 Monitoring and Logging

### Step 1: Application Insights (Azure)

```json
{
  "ApplicationInsights": {
    "InstrumentationKey": "[YourKey]"
  }
}
```

### Step 2: Structured Logging

```csharp
// In Program.cs
builder.Logging.AddConsole();
builder.Logging.AddEventLog();
builder.Services.AddApplicationInsightsTelemetry();
```

### Step 3: Health Checks

```csharp
builder.Services.AddHealthChecks()
    .AddSqlServer(connectionString)
    .AddCheck("compliance-api", () => HealthCheckResult.Healthy());

app.MapHealthChecks("/health");
```

## 🧪 Post-Deployment Testing

### Step 1: API Health Check

```bash
curl -k https://your-api-domain.com/health
```

### Step 2: Authentication Test

```bash
curl -k -X POST https://your-api-domain.com/api/auth/login \
  -H "Content-Type: application/json" \
  -d '{"email":"<EMAIL>","password":"Test123!","tenantKey":"test"}'
```

### Step 3: Compliance API Test

```bash
# Test with valid JWT token
curl -k -X GET https://your-api-domain.com/api/compliance \
  -H "Authorization: Bearer [YourJWTToken]"
```

### Step 4: Frontend Functionality

1. Navigate to `https://your-domain.com`
2. Test login functionality
3. Navigate to Compliance Calendar
4. Test CRUD operations
5. Verify calendar view works
6. Test responsive design

## 🔄 Maintenance and Updates

### Database Maintenance

```sql
-- Weekly maintenance script
EXEC sp_UpdateOverdueCompliance;

-- Monthly cleanup (if needed)
DELETE FROM ComplianceHistory 
WHERE ChangedAt < DATEADD(MONTH, -6, GETUTCDATE());
```

### Application Updates

1. **Zero-Downtime Deployment**
   - Use blue-green deployment
   - Or rolling updates with load balancer

2. **Database Migrations**
   - Always backup before migrations
   - Test migrations in staging first
   - Use transaction scripts for rollback

### Monitoring Checklist

- [ ] API response times < 500ms
- [ ] Database connection pool healthy
- [ ] SSL certificate expiration monitoring
- [ ] Disk space monitoring
- [ ] Error rate monitoring
- [ ] User activity monitoring

## 🆘 Troubleshooting

### Common Issues

1. **API Returns 500 Error**
   - Check connection strings
   - Verify database accessibility
   - Check application logs

2. **Frontend Not Loading**
   - Verify build files copied correctly
   - Check web server configuration
   - Verify API endpoints accessible

3. **Authentication Failures**
   - Check JWT configuration
   - Verify tenant database connectivity
   - Check user credentials

### Log Locations

- **IIS Logs**: `C:\inetpub\logs\LogFiles\`
- **Application Logs**: `C:\inetpub\wwwroot\ca-portal-api\logs\`
- **Event Viewer**: Windows Logs > Application

## 📞 Support

For deployment support:
1. Check this deployment guide
2. Review application logs
3. Test in staging environment first
4. Contact development team if needed

---

## 🎉 Deployment Complete!

Your Compliance Management System is now ready for production use with:

✅ **Secure API** with JWT authentication  
✅ **Responsive Frontend** with modern UI  
✅ **Robust Database** with proper indexing  
✅ **Comprehensive Monitoring** and logging  
✅ **Production-Ready** security measures  

**Happy Compliance Management!** 🎊
