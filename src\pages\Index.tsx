
import { useState } from "react";
import { Sidebar } from "@/components/Sidebar";
import { ModuleCard } from "@/components/ModuleCard";
import { Calendar, Bell, FileText, Upload, Clock, FolderOpen, Users, MessageSquare } from "lucide-react";
import { useNavigate } from "react-router-dom";

const modules = [
  {
    id: 1,
    title: "Centralized Compliance Calendar",
    description: "Auto-generated calendar of all upcoming deadlines (GST, TDS, ROC, ITR) by client",
    icon: Calendar,
    status: "active",
    timeSaved: "No more Excel trackers, avoid penalties, easy task assignment",
    color: "bg-blue-500",
    route: "/compliance-calendar"
  },
  {
    id: 2,
    title: "Client Reminder Automation",
    description: "Automated WhatsApp/email reminders for tax deadlines and compliance",
    icon: Bell,
    status: "active",
    timeSaved: "Eliminates repetitive follow-ups and manual messaging",
    color: "bg-green-500",
    route: "/client-reminders"
  },
  {
    id: 3,
    title: "Auto Report Generator",
    description: "Upload financials → instant KPI reports (sales, expenses, taxes, dues)",
    icon: FileText,
    status: "active",
    timeSaved: "No manual Excel reports, instant client meeting materials",
    color: "bg-purple-500",
    route: "/report-generator"
  },
  {
    id: 4,
    title: "Filing Assistant",
    description: "Upload invoices → generate JSON/XML files for GST or TDS returns",
    icon: Upload,
    status: "active",
    timeSaved: "Saves 1-2 hours per return, eliminates format errors",
    color: "bg-orange-500",
    route: "/filing-assistant"
  },
  {
    id: 5,
    title: "Client History View",
    description: "Complete view of past filings, pending documents, and communication logs",
    icon: Clock,
    status: "active",
    timeSaved: "No more email digging, quick team onboarding",
    color: "bg-indigo-500",
    route: "/client-history"
  },
  {
    id: 6,
    title: "Document Vault",
    description: "Auto-categorize uploads by type (Form 16, GSTR, Audit Reports, etc.)",
    icon: FolderOpen,
    status: "active",
    timeSaved: "Organized storage, everything searchable per client",
    color: "bg-teal-500",
    route: "/document-vault"
  },
  {
    id: 7,
    title: "Task Assignment & Tracking",
    description: "Assign work to staff, track completion, add internal notes",
    icon: Users,
    status: "active",
    timeSaved: "Better coordination, no missed or duplicate tasks",
    color: "bg-red-500",
    route: "/task-management"
  },
  {
    id: 8,
    title: "Client Communication Hub",
    description: "Pre-built templates for emailing reminders, checklists, data requests",
    icon: MessageSquare,
    status: "active",
    timeSaved: "Consistent communication, no copy-paste cycle",
    color: "bg-pink-500",
    route: "/communication-hub"
  }
];

const Index = () => {
  const [selectedModule, setSelectedModule] = useState(null);
  const navigate = useNavigate();

  const handleModuleClick = (module: any) => {
    navigate(module.route);
    setSelectedModule(module);
  };

  return (
    <div className="flex min-h-screen bg-gray-50">
      <Sidebar />
      
      <main className="flex-1 ml-64 p-8">
        <div className="max-w-7xl mx-auto">
          {/* Header */}
          <div className="mb-8">
            <h1 className="text-3xl font-bold text-gray-900 mb-2">
              ComplianceDesk Pro Dashboard
            </h1>
            <p className="text-gray-600 text-lg">
              Complete CA Firm Management Portal - Streamline compliance, automate client communication, and boost productivity
            </p>
          </div>

          {/* Client Stats Overview */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
            <div className="bg-white rounded-lg p-6 shadow-sm border border-gray-200">
              <div className="flex items-center">
                <div className="p-2 bg-blue-100 rounded-lg">
                  <Users className="h-6 w-6 text-blue-600" />
                </div>
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-600">Total Clients</p>
                  <p className="text-2xl font-bold text-gray-900">147</p>
                </div>
              </div>
            </div>
            
            <div className="bg-white rounded-lg p-6 shadow-sm border border-gray-200">
              <div className="flex items-center">
                <div className="p-2 bg-green-100 rounded-lg">
                  <Bell className="h-6 w-6 text-green-600" />
                </div>
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-600">Pending Tasks</p>
                  <p className="text-2xl font-bold text-gray-900">23</p>
                </div>
              </div>
            </div>
            
            <div className="bg-white rounded-lg p-6 shadow-sm border border-gray-200">
              <div className="flex items-center">
                <div className="p-2 bg-orange-100 rounded-lg">
                  <FileText className="h-6 w-6 text-orange-600" />
                </div>
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-600">This Month's Filings</p>
                  <p className="text-2xl font-bold text-gray-900">89</p>
                </div>
              </div>
            </div>
          </div>

          {/* Modules Grid */}
          <div className="mb-8">
            <h2 className="text-2xl font-bold text-gray-900 mb-6">Portal Modules</h2>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
              {modules.map((module) => (
                <ModuleCard
                  key={module.id}
                  module={module}
                  onClick={() => handleModuleClick(module)}
                />
              ))}
            </div>
          </div>

          {/* Recent Activity */}
          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <h3 className="text-xl font-bold text-gray-900 mb-4">Recent Activity</h3>
            <div className="space-y-4">
              <div className="flex items-center p-3 bg-gray-50 rounded-lg">
                <div className="p-2 bg-green-100 rounded-full">
                  <FileText className="h-4 w-4 text-green-600" />
                </div>
                <div className="ml-3 flex-1">
                  <p className="text-sm font-medium text-gray-900">GST Return filed for ABC Enterprises</p>
                  <p className="text-xs text-gray-500">2 hours ago</p>
                </div>
              </div>
              
              <div className="flex items-center p-3 bg-gray-50 rounded-lg">
                <div className="p-2 bg-blue-100 rounded-full">
                  <Bell className="h-4 w-4 text-blue-600" />
                </div>
                <div className="ml-3 flex-1">
                  <p className="text-sm font-medium text-gray-900">TDS reminder sent to 15 clients</p>
                  <p className="text-xs text-gray-500">4 hours ago</p>
                </div>
              </div>
              
              <div className="flex items-center p-3 bg-gray-50 rounded-lg">
                <div className="p-2 bg-purple-100 rounded-full">
                  <Upload className="h-4 w-4 text-purple-600" />
                </div>
                <div className="ml-3 flex-1">
                  <p className="text-sm font-medium text-gray-900">New documents uploaded for XYZ Ltd audit</p>
                  <p className="text-xs text-gray-500">6 hours ago</p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </main>
    </div>
  );
};

export default Index;
