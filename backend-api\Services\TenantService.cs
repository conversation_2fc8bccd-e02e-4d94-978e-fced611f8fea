using CAPortalAPI.Models.DTOs;
using Newtonsoft.Json;

namespace CAPortalAPI.Services
{
    public class TenantService : ITenantService
    {
        private readonly IDbService _dbService;
        private readonly ILogger<TenantService> _logger;
        private const string MasterConnectionName = "TenantRegistryConnection";

        public TenantService(IDbService dbService, ILogger<TenantService> logger)
        {
            _dbService = dbService;
            _logger = logger;
        }

        public async Task<TenantDto?> ResolveTenantByDomainAsync(string domain)
        {
            try
            {
                var payload = JsonConvert.SerializeObject(new { Domain = domain });
                return await _dbService.ExecuteStoredProcedureAsync<TenantDto>("sp_GetTenantByDomain", payload, MasterConnectionName);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error resolving tenant by domain {Domain}", domain);
                return null;
            }
        }

        public async Task<TenantDto?> ResolveTenantByKeyAsync(string tenantKey)
        {
            try
            {
                var payload = JsonConvert.SerializeObject(new { TenantKey = tenantKey });
                return await _dbService.ExecuteStoredProcedureForTenantRegistryAsync<TenantDto>("sp_GetTenantByKey", payload, MasterConnectionName);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error resolving tenant by key {TenantKey}", tenantKey);
                return null;
            }
        }

        public async Task<string?> GetTenantConnectionStringAsync(string tenantKey)
        {
            try
            {
                var tenant = await GetTenantWithConnectionStringAsync(tenantKey);
                return tenant?.ConnectionString;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting connection string for tenant {TenantKey}", tenantKey);
                return null;
            }
        }

        public async Task<TenantDto?> GetTenantWithConnectionStringAsync(string tenantKey)
        {
            try
            {
                var payload = JsonConvert.SerializeObject(new { TenantKey = tenantKey });
                return await _dbService.ExecuteStoredProcedureForTenantRegistryAsync<TenantDto>("sp_GetTenantWithConnectionString", payload, MasterConnectionName);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting tenant with connection string for {TenantKey}", tenantKey);
                return null;
            }
        }

        public async Task<bool> ValidateTenantAsync(string tenantKey)
        {
            try
            {
                var tenant = await ResolveTenantByKeyAsync(tenantKey);
                return tenant != null && tenant.IsActive;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error validating tenant {TenantKey}", tenantKey);
                return false;
            }
        }

        public async Task<List<TenantDto>> GetActiveTenantsAsync()
        {
            try
            {
                var payload = JsonConvert.SerializeObject(new { IsActive = true });
                return await _dbService.ExecuteStoredProcedureListForTenantRegistryAsync<TenantDto>("sp_GetActiveTenants", payload, MasterConnectionName);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting active tenants");
                return new List<TenantDto>();
            }
        }

        public async Task<TenantDto?> CreateTenantAsync(CreateTenantRequestDto request)
        {
            try
            {
                var payload = JsonConvert.SerializeObject(request);
                return await _dbService.ExecuteStoredProcedureAsync<TenantDto>("sp_CreateTenant", payload, MasterConnectionName);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error creating tenant {TenantKey}", request.TenantKey);
                throw;
            }
        }

        public async Task<TenantDto?> UpdateTenantAsync(UpdateTenantRequestDto request)
        {
            try
            {
                var payload = JsonConvert.SerializeObject(request);
                return await _dbService.ExecuteStoredProcedureAsync<TenantDto>("sp_UpdateTenant", payload, MasterConnectionName);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating tenant {OrganizationId}", request.OrganizationId);
                throw;
            }
        }

        public async Task<TenantUsageDto?> GetTenantUsageAsync(string tenantKey)
        {
            try
            {
                var payload = JsonConvert.SerializeObject(new { TenantKey = tenantKey });
                return await _dbService.ExecuteStoredProcedureAsync<TenantUsageDto>("sp_GetTenantUsage", payload, MasterConnectionName);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting tenant usage for {TenantKey}", tenantKey);
                return null;
            }
        }
    }
}
