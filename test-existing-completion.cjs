// Test completion with an existing InProgress compliance item
const https = require('https');

// Disable SSL certificate validation for localhost testing
process.env["NODE_TLS_REJECT_UNAUTHORIZED"] = 0;

async function testExistingCompletion() {
  console.log('🧪 Testing Completion with Existing InProgress Item...\n');

  try {
    // 1. First login to get token
    console.log('1️⃣ Logging in...');
    const loginPayload = JSON.stringify({
      email: "<EMAIL>",
      password: "password123",
      tenantKey: "kumar-associates"
    });

    const loginResponse = await makeRequest({
      hostname: 'localhost',
      port: 7000,
      path: '/api/auth/login',
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Content-Length': Buffer.byteLength(loginPayload)
      }
    }, loginPayload);

    if (loginResponse.statusCode !== 200 || !loginResponse.data.success) {
      console.log('❌ Login failed:', loginResponse.data);
      return;
    }

    console.log('✅ Login successful!');
    const token = loginResponse.data.data.token;
    const authHeaders = {
      'Authorization': `Bearer ${token}`,
      'Content-Type': 'application/json'
    };

    // 2. Test completion with existing InProgress item
    console.log('\n2️⃣ Testing Completion with Existing InProgress Item...');
    const complianceId = 'B927BF6C-D9E4-416D-977D-6F06E4631462'; // Existing InProgress item
    const completePayload = JSON.stringify({
      notes: "Successfully completed via API test! 🎉"
    });

    const completeResponse = await makeRequest({
      hostname: 'localhost',
      port: 7000,
      path: `/api/compliance/${complianceId}/complete`,
      method: 'POST',
      headers: {
        ...authHeaders,
        'Content-Length': Buffer.byteLength(completePayload)
      }
    }, completePayload);

    console.log(`📊 Status Code: ${completeResponse.statusCode}`);
    console.log('📥 Response:', JSON.stringify(completeResponse.data, null, 2));

    if (completeResponse.statusCode === 200 && completeResponse.data.success) {
      console.log('\n🎉 SUCCESS! Compliance item marked as completed!');
      console.log(`✅ Compliance ID: ${complianceId}`);
      console.log('📝 Notes: Successfully completed via API test! 🎉');
      
      // 3. Verify the completion in database
      console.log('\n3️⃣ Verifying completion in database...');
      console.log('Please check the database to confirm the item status changed to "Completed"');
      console.log(`Query: SELECT ComplianceId, Status, CompletedAt, Notes FROM ComplianceItems WHERE ComplianceId = '${complianceId}'`);
      
    } else if (completeResponse.statusCode === 404) {
      console.log('\n⚠️  Item not found - this means the stored procedure is working correctly');
    } else if (completeResponse.statusCode === 500) {
      console.log('\n❌ Internal server error - check API logs for details');
    } else {
      console.log('\n❌ Unexpected response');
      if (completeResponse.data.errors) {
        console.log('Errors:', completeResponse.data.errors);
      }
    }

  } catch (error) {
    console.error('❌ Test failed with error:', error.message);
  }
}

// Helper function to make API requests
function makeRequest(options, postData = null) {
  return new Promise((resolve, reject) => {
    const req = https.request(options, (res) => {
      let data = '';
      res.on('data', (chunk) => {
        data += chunk;
      });
      res.on('end', () => {
        try {
          const response = JSON.parse(data);
          resolve({ statusCode: res.statusCode, data: response });
        } catch (error) {
          resolve({ statusCode: res.statusCode, data: data });
        }
      });
    });

    req.on('error', (error) => {
      reject(error);
    });

    if (postData) {
      req.write(postData);
    }
    req.end();
  });
}

testExistingCompletion();
