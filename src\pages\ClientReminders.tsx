
import { useState } from "react";
import { Sidebar } from "@/components/Sidebar";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Calendar } from "@/components/ui/calendar";
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover";
import { CalendarIcon, Bell, MessageSquare, Mail, Users, Clock, Edit, Trash2 } from "lucide-react";
import { format } from "date-fns";
import { cn } from "@/lib/utils";
import { useToast } from "@/hooks/use-toast";

interface ReminderData {
  id: number;
  reminderType: string;
  taxType: string;
  dueDate: string;
  clientGroup: string;
  customMessage: string;
  status: 'scheduled' | 'sent' | 'failed';
  clientCount: number;
  createdAt: string;
}

const ClientReminders = () => {
  const [reminderType, setReminderType] = useState("");
  const [taxType, setTaxType] = useState("");
  const [dueDate, setDueDate] = useState<Date>();
  const [clientGroup, setClientGroup] = useState("");
  const [customMessage, setCustomMessage] = useState("");
  const { toast } = useToast();

  const [reminders, setReminders] = useState<ReminderData[]>([
    { 
      id: 1, 
      reminderType: "WhatsApp", 
      taxType: "GST Return", 
      dueDate: "2024-06-01",
      clientGroup: "Corporate Clients",
      customMessage: "Please submit your May invoices for GST filing",
      status: "sent",
      clientCount: 25, 
      createdAt: "2024-06-01" 
    },
    { 
      id: 2, 
      reminderType: "Email", 
      taxType: "TDS Return", 
      dueDate: "2024-06-05",
      clientGroup: "All Clients",
      customMessage: "",
      status: "scheduled",
      clientCount: 15, 
      createdAt: "2024-06-05" 
    },
    { 
      id: 3, 
      reminderType: "Both WhatsApp & Email", 
      taxType: "ITR Filing", 
      dueDate: "2024-05-28",
      clientGroup: "Individual Clients",
      customMessage: "ITR filing deadline approaching. Please provide Form 16.",
      status: "sent",
      clientCount: 42, 
      createdAt: "2024-05-28" 
    },
    { 
      id: 4, 
      reminderType: "Email", 
      taxType: "ROC Filing", 
      dueDate: "2024-05-25",
      clientGroup: "Corporate Clients",
      customMessage: "",
      status: "sent",
      clientCount: 8, 
      createdAt: "2024-05-25" 
    },
  ]);

  const handleScheduleReminder = () => {
    if (!reminderType || !taxType || !dueDate || !clientGroup) {
      toast({
        title: "Missing Information",
        description: "Please fill in all required fields.",
        variant: "destructive",
      });
      return;
    }

    // Simulate client count based on group
    const getClientCount = (group: string) => {
      switch (group) {
        case "all": return Math.floor(Math.random() * 100) + 50;
        case "corporate": return Math.floor(Math.random() * 30) + 10;
        case "individual": return Math.floor(Math.random() * 50) + 20;
        case "msme": return Math.floor(Math.random() * 25) + 15;
        case "high-value": return Math.floor(Math.random() * 15) + 5;
        default: return 10;
      }
    };

    const newReminder: ReminderData = {
      id: Math.max(...reminders.map(r => r.id)) + 1,
      reminderType,
      taxType,
      dueDate: format(dueDate, "yyyy-MM-dd"),
      clientGroup,
      customMessage,
      status: "scheduled",
      clientCount: getClientCount(clientGroup),
      createdAt: new Date().toISOString().split('T')[0]
    };

    setReminders([newReminder, ...reminders]);

    toast({
      title: "Reminder Scheduled Successfully",
      description: `${reminderType} reminders for ${taxType} scheduled for ${newReminder.clientCount} clients.`,
    });

    // Reset form
    setReminderType("");
    setTaxType("");
    setDueDate(undefined);
    setClientGroup("");
    setCustomMessage("");
  };

  const handleSendNow = (id: number) => {
    setReminders(reminders.map(reminder => 
      reminder.id === id ? { ...reminder, status: 'sent' as const } : reminder
    ));
    toast({
      title: "Reminder Sent",
      description: "Reminder has been sent to all clients"
    });
  };

  const handleDeleteReminder = (id: number) => {
    setReminders(reminders.filter(reminder => reminder.id !== id));
    toast({
      title: "Reminder Deleted",
      description: "Reminder has been deleted successfully"
    });
  };

  const handleResendReminder = (reminder: ReminderData) => {
    const updatedReminder = { ...reminder, status: 'sent' as const, createdAt: new Date().toISOString().split('T')[0] };
    setReminders([updatedReminder, ...reminders.filter(r => r.id !== reminder.id)]);
    toast({
      title: "Reminder Resent",
      description: `Reminder resent to ${reminder.clientCount} clients`
    });
  };

  // Calculate stats
  const stats = {
    whatsappSent: reminders.filter(r => r.reminderType.includes("WhatsApp") && r.status === "sent").reduce((sum, r) => sum + r.clientCount, 0),
    emailsSent: reminders.filter(r => r.reminderType.includes("Email") && r.status === "sent").reduce((sum, r) => sum + r.clientCount, 0),
    scheduled: reminders.filter(r => r.status === "scheduled").length,
    clientsReached: reminders.filter(r => r.status === "sent").reduce((sum, r) => sum + r.clientCount, 0)
  };

  return (
    <div className="flex min-h-screen bg-gray-50">
      <Sidebar />
      
      <main className="flex-1 ml-64 p-8">
        <div className="max-w-7xl mx-auto">
          {/* Header */}
          <div className="mb-8">
            <h1 className="text-3xl font-bold text-gray-900 mb-2">
              Client Reminder Automation
            </h1>
            <p className="text-gray-600 text-lg">
              Automated WhatsApp/email reminders for tax deadlines and compliance
            </p>
          </div>

          {/* Stats Cards */}
          <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
            <Card>
              <CardContent className="p-6">
                <div className="flex items-center">
                  <div className="p-2 bg-green-100 rounded-lg">
                    <MessageSquare className="h-6 w-6 text-green-600" />
                  </div>
                  <div className="ml-4">
                    <p className="text-sm font-medium text-gray-600">WhatsApp Sent</p>
                    <p className="text-2xl font-bold text-gray-900">{stats.whatsappSent}</p>
                  </div>
                </div>
              </CardContent>
            </Card>
            
            <Card>
              <CardContent className="p-6">
                <div className="flex items-center">
                  <div className="p-2 bg-blue-100 rounded-lg">
                    <Mail className="h-6 w-6 text-blue-600" />
                  </div>
                  <div className="ml-4">
                    <p className="text-sm font-medium text-gray-600">Emails Sent</p>
                    <p className="text-2xl font-bold text-gray-900">{stats.emailsSent}</p>
                  </div>
                </div>
              </CardContent>
            </Card>
            
            <Card>
              <CardContent className="p-6">
                <div className="flex items-center">
                  <div className="p-2 bg-orange-100 rounded-lg">
                    <Clock className="h-6 w-6 text-orange-600" />
                  </div>
                  <div className="ml-4">
                    <p className="text-sm font-medium text-gray-600">Scheduled</p>
                    <p className="text-2xl font-bold text-gray-900">{stats.scheduled}</p>
                  </div>
                </div>
              </CardContent>
            </Card>
            
            <Card>
              <CardContent className="p-6">
                <div className="flex items-center">
                  <div className="p-2 bg-purple-100 rounded-lg">
                    <Users className="h-6 w-6 text-purple-600" />
                  </div>
                  <div className="ml-4">
                    <p className="text-sm font-medium text-gray-600">Clients Reached</p>
                    <p className="text-2xl font-bold text-gray-900">{stats.clientsReached}</p>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>

          <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
            {/* Schedule New Reminder */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center">
                  <Bell className="mr-2 h-5 w-5" />
                  Schedule New Reminder
                </CardTitle>
                <CardDescription>
                  Set up automated reminders for your clients
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-6">
                <div className="space-y-2">
                  <Label htmlFor="reminderType">Reminder Type</Label>
                  <Select value={reminderType} onValueChange={setReminderType}>
                    <SelectTrigger>
                      <SelectValue placeholder="Select reminder type" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="whatsapp">WhatsApp</SelectItem>
                      <SelectItem value="email">Email</SelectItem>
                      <SelectItem value="both">Both WhatsApp & Email</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="taxType">Tax/Compliance Type</Label>
                  <Select value={taxType} onValueChange={setTaxType}>
                    <SelectTrigger>
                      <SelectValue placeholder="Select tax type" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="gst">GST Return</SelectItem>
                      <SelectItem value="tds">TDS Return</SelectItem>
                      <SelectItem value="itr">ITR Filing</SelectItem>
                      <SelectItem value="roc">ROC Filing</SelectItem>
                      <SelectItem value="audit">Audit Report</SelectItem>
                      <SelectItem value="advance-tax">Advance Tax</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div className="space-y-2">
                  <Label>Due Date</Label>
                  <Popover>
                    <PopoverTrigger asChild>
                      <Button
                        variant="outline"
                        className={cn(
                          "w-full justify-start text-left font-normal",
                          !dueDate && "text-muted-foreground"
                        )}
                      >
                        <CalendarIcon className="mr-2 h-4 w-4" />
                        {dueDate ? format(dueDate, "PPP") : "Select due date"}
                      </Button>
                    </PopoverTrigger>
                    <PopoverContent className="w-auto p-0" align="start">
                      <Calendar
                        mode="single"
                        selected={dueDate}
                        onSelect={setDueDate}
                        initialFocus
                        className="p-3 pointer-events-auto"
                      />
                    </PopoverContent>
                  </Popover>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="clientGroup">Client Group</Label>
                  <Select value={clientGroup} onValueChange={setClientGroup}>
                    <SelectTrigger>
                      <SelectValue placeholder="Select client group" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">All Clients</SelectItem>
                      <SelectItem value="corporate">Corporate Clients</SelectItem>
                      <SelectItem value="individual">Individual Clients</SelectItem>
                      <SelectItem value="msme">MSME Clients</SelectItem>
                      <SelectItem value="high-value">High Value Clients</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="customMessage">Custom Message (Optional)</Label>
                  <Textarea
                    id="customMessage"
                    placeholder="Add any specific instructions or custom message..."
                    value={customMessage}
                    onChange={(e) => setCustomMessage(e.target.value)}
                    rows={3}
                  />
                </div>

                <Button onClick={handleScheduleReminder} className="w-full">
                  Schedule Reminder
                </Button>
              </CardContent>
            </Card>

            {/* Reminder History */}
            <Card>
              <CardHeader>
                <CardTitle>Reminder History</CardTitle>
                <CardDescription>
                  Manage your scheduled and sent reminders
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4 max-h-96 overflow-y-auto">
                  {reminders.map((reminder) => (
                    <div key={reminder.id} className="p-4 bg-gray-50 rounded-lg">
                      <div className="flex items-start justify-between">
                        <div className="flex items-center space-x-4">
                          <div className={`p-2 rounded-full ${
                            reminder.reminderType.includes("WhatsApp") ? "bg-green-100" : "bg-blue-100"
                          }`}>
                            {reminder.reminderType.includes("WhatsApp") ? (
                              <MessageSquare className="h-4 w-4 text-green-600" />
                            ) : (
                              <Mail className="h-4 w-4 text-blue-600" />
                            )}
                          </div>
                          <div className="flex-1">
                            <p className="font-medium text-gray-900">{reminder.taxType}</p>
                            <p className="text-sm text-gray-500">
                              {reminder.clientCount} clients • {reminder.reminderType}
                            </p>
                            <p className="text-sm text-gray-500">
                              Due: {new Date(reminder.dueDate).toLocaleDateString()}
                            </p>
                            {reminder.customMessage && (
                              <p className="text-xs text-gray-400 mt-1 italic">
                                "{reminder.customMessage}"
                              </p>
                            )}
                          </div>
                        </div>
                        <div className="flex flex-col items-end space-y-2">
                          <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                            reminder.status === "sent" 
                              ? "bg-green-100 text-green-800" 
                              : reminder.status === "scheduled"
                              ? "bg-orange-100 text-orange-800"
                              : "bg-red-100 text-red-800"
                          }`}>
                            {reminder.status}
                          </span>
                          <div className="flex space-x-1">
                            {reminder.status === "scheduled" && (
                              <Button size="sm" variant="outline" onClick={() => handleSendNow(reminder.id)}>
                                Send Now
                              </Button>
                            )}
                            {reminder.status === "sent" && (
                              <Button size="sm" variant="outline" onClick={() => handleResendReminder(reminder)}>
                                Resend
                              </Button>
                            )}
                            <Button size="sm" variant="outline" onClick={() => handleDeleteReminder(reminder.id)}>
                              <Trash2 className="h-3 w-3" />
                            </Button>
                          </div>
                          <p className="text-xs text-gray-500">{reminder.createdAt}</p>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </main>
    </div>
  );
};

export default ClientReminders;
