using System.ComponentModel.DataAnnotations;

namespace CAPortalAPI.Models.DTOs
{
    public class ComplianceItemDto
    {
        public Guid ComplianceId { get; set; }
        public Guid ClientId { get; set; }
        public string ClientName { get; set; } = string.Empty;
        public string ComplianceType { get; set; } = string.Empty;
        public string? SubType { get; set; }
        public DateTime DueDate { get; set; }
        public string? Description { get; set; }
        public string Status { get; set; } = string.Empty;
        public string Priority { get; set; } = string.Empty;
        public Guid? AssignedTo { get; set; }
        public string? AssignedToName { get; set; }
        public DateTime CreatedAt { get; set; }
        public DateTime? CompletedAt { get; set; }
        public string? Notes { get; set; }
        public bool ReminderSent { get; set; }
        public DateTime? LastReminderDate { get; set; }
    }

    public class CreateComplianceItemRequestDto
    {
        [Required]
        public Guid ClientId { get; set; }

        [Required]
        public string ComplianceType { get; set; } = string.Empty;

        public string? SubType { get; set; }

        [Required]
        public DateTime DueDate { get; set; }

        public string? Description { get; set; }

        [Required]
        public string Priority { get; set; } = "Medium";

        public Guid? AssignedTo { get; set; }
        public string? Notes { get; set; }
    }

    public class UpdateComplianceItemRequestDto
    {
        [Required]
        public Guid ComplianceId { get; set; }

        [Required]
        public Guid ClientId { get; set; }

        [Required]
        public string ComplianceType { get; set; } = string.Empty;

        public string? SubType { get; set; }

        [Required]
        public DateTime DueDate { get; set; }

        public string? Description { get; set; }

        [Required]
        public string Status { get; set; } = string.Empty;

        [Required]
        public string Priority { get; set; } = string.Empty;

        public Guid? AssignedTo { get; set; }
        public string? Notes { get; set; }
    }

    public class ComplianceListRequestDto
    {
        public int PageNumber { get; set; } = 1;
        public int PageSize { get; set; } = 20;
        public string? SearchTerm { get; set; }
        public string? ComplianceType { get; set; }
        public string? Status { get; set; }
        public string? Priority { get; set; }
        public Guid? AssignedTo { get; set; }
        public Guid? ClientId { get; set; }
        public DateTime? DueDateFrom { get; set; }
        public DateTime? DueDateTo { get; set; }
        public bool? IsOverdue { get; set; }
    }

    public class ComplianceListResponseDto
    {
        public List<ComplianceItemDto> ComplianceItems { get; set; } = new();
        public int TotalCount { get; set; }
        public int PageNumber { get; set; }
        public int PageSize { get; set; }
        public int TotalPages { get; set; }
    }

    public class ComplianceCalendarRequestDto
    {
        public DateTime StartDate { get; set; }
        public DateTime EndDate { get; set; }
        public string? ComplianceType { get; set; }
        public string? Status { get; set; }
        public Guid? AssignedTo { get; set; }
        public Guid? ClientId { get; set; }
    }

    public class ComplianceCalendarResponseDto
    {
        public List<ComplianceCalendarItemDto> Items { get; set; } = new();
        public int TotalCount { get; set; }
    }

    public class ComplianceCalendarItemDto
    {
        public Guid ComplianceId { get; set; }
        public string Title { get; set; } = string.Empty;
        public string ClientName { get; set; } = string.Empty;
        public string ComplianceType { get; set; } = string.Empty;
        public DateTime DueDate { get; set; }
        public string Status { get; set; } = string.Empty;
        public string Priority { get; set; } = string.Empty;
        public string? AssignedToName { get; set; }
        public bool IsOverdue { get; set; }
        public int DaysUntilDue { get; set; }
    }

    public class ComplianceDashboardDto
    {
        public int TotalItems { get; set; }
        public int PendingItems { get; set; }
        public int CompletedItems { get; set; }
        public int OverdueItems { get; set; }
        public int DueThisWeek { get; set; }
        public int DueThisMonth { get; set; }
        public List<ComplianceTypeStatsDto> TypeStats { get; set; } = new();
        public List<ComplianceCalendarItemDto> UpcomingItems { get; set; } = new();
    }

    public class ComplianceTypeStatsDto
    {
        public string ComplianceType { get; set; } = string.Empty;
        public int TotalCount { get; set; }
        public int PendingCount { get; set; }
        public int CompletedCount { get; set; }
        public int OverdueCount { get; set; }
    }
}
