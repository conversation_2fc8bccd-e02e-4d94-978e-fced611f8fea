using CAPortalAPI.Services;

namespace CAPortalAPI.Helpers
{
    public class TenantResolutionMiddleware
    {
        private readonly RequestDelegate _next;
        private readonly ILogger<TenantResolutionMiddleware> _logger;

        public TenantResolutionMiddleware(RequestDelegate next, ILogger<TenantResolutionMiddleware> logger)
        {
            _next = next;
            _logger = logger;
        }

        public async Task InvokeAsync(HttpContext context, ITenantService tenantService)
        {
            try
            {
                // Skip tenant resolution for certain paths
                if (ShouldSkipTenantResolution(context.Request.Path))
                {
                    await _next(context);
                    return;
                }

                string? tenantKey = null;

                // Strategy 1: Extract from JWT token (for authenticated requests)
                var authHeader = context.Request.Headers["Authorization"].FirstOrDefault();
                if (!string.IsNullOrEmpty(authHeader) && authHeader.StartsWith("Bearer "))
                {
                    var token = authHeader.Substring("Bearer ".Length).Trim();
                    // You would extract tenant key from JWT token here
                    // For now, we'll use other strategies
                }

                // Strategy 2: Extract from custom header
                if (string.IsNullOrEmpty(tenantKey))
                {
                    tenantKey = context.Request.Headers["X-Tenant-ID"].FirstOrDefault();
                }

                // Strategy 3: Extract from subdomain
                if (string.IsNullOrEmpty(tenantKey))
                {
                    var host = context.Request.Host.Host;
                    var parts = host.Split('.');
                    if (parts.Length > 2) // subdomain.domain.com
                    {
                        tenantKey = parts[0];
                    }
                }

                // Strategy 4: Extract from custom domain
                if (string.IsNullOrEmpty(tenantKey))
                {
                    var domain = context.Request.Host.Host;
                    var tenant = await tenantService.ResolveTenantByDomainAsync(domain);
                    if (tenant != null)
                    {
                        tenantKey = tenant.TenantKey;
                    }
                }

                // Strategy 5: Extract from query parameter (fallback for development)
                if (string.IsNullOrEmpty(tenantKey))
                {
                    tenantKey = context.Request.Query["tenant"].FirstOrDefault();
                }

                // Validate tenant if found
                if (!string.IsNullOrEmpty(tenantKey))
                {
                    var isValidTenant = await tenantService.ValidateTenantAsync(tenantKey);
                    if (isValidTenant)
                    {
                        // Add tenant information to request context
                        context.Items["TenantKey"] = tenantKey;
                        
                        // Get full tenant information
                        var tenantInfo = await tenantService.ResolveTenantByKeyAsync(tenantKey);
                        if (tenantInfo != null)
                        {
                            context.Items["TenantInfo"] = tenantInfo;
                            context.Items["TenantConnectionString"] = await tenantService.GetTenantConnectionStringAsync(tenantKey);
                        }

                        _logger.LogInformation("Tenant resolved: {TenantKey} for request {Path}", tenantKey, context.Request.Path);
                    }
                    else
                    {
                        _logger.LogWarning("Invalid tenant key: {TenantKey} for request {Path}", tenantKey, context.Request.Path);
                        context.Response.StatusCode = 404;
                        await context.Response.WriteAsync("Tenant not found or inactive");
                        return;
                    }
                }
                else
                {
                    // For requests that require tenant context but none found
                    if (RequiresTenantContext(context.Request.Path))
                    {
                        _logger.LogWarning("No tenant context found for request {Path}", context.Request.Path);
                        context.Response.StatusCode = 400;
                        await context.Response.WriteAsync("Tenant context required");
                        return;
                    }
                }

                await _next(context);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error in tenant resolution middleware");
                context.Response.StatusCode = 500;
                await context.Response.WriteAsync("Internal server error");
            }
        }

        private static bool ShouldSkipTenantResolution(PathString path)
        {
            var pathsToSkip = new[]
            {
                "/swagger",
                "/health",
                "/api/auth/login",
                "/api/system",
                "/api/admin/tenants"
            };

            return pathsToSkip.Any(skipPath => path.StartsWithSegments(skipPath, StringComparison.OrdinalIgnoreCase));
        }

        private static bool RequiresTenantContext(PathString path)
        {
            var pathsRequiringTenant = new[]
            {
                "/api/users",
                "/api/clients",
                "/api/compliance",
                "/api/documents",
                "/api/tasks",
                "/api/reports",
                "/api/communications"
            };

            return pathsRequiringTenant.Any(requiredPath => path.StartsWithSegments(requiredPath, StringComparison.OrdinalIgnoreCase));
        }
    }

    // Extension method to easily add the middleware
    public static class TenantResolutionMiddlewareExtensions
    {
        public static IApplicationBuilder UseTenantResolution(this IApplicationBuilder builder)
        {
            return builder.UseMiddleware<TenantResolutionMiddleware>();
        }
    }
}
