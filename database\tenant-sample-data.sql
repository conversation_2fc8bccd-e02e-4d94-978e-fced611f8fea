-- =====================================================
-- Sample Data for Tenant Database
-- =====================================================
-- This script inserts realistic sample data for a CA firm
-- Replace {TENANT_ID} with actual tenant organization ID

-- =====================================================
-- Insert Users (Staff Members)
-- =====================================================

DECLARE @TenantId UNIQUEIDENTIFIER = '{TENANT_ID}'; -- Replace with actual tenant ID
DECLARE @AdminUserId UNIQUEIDENTIFIER = NEWID();
DECLARE @ManagerUserId UNIQUEIDENTIFIER = NEWID();
DECLARE @SeniorUserId UNIQUEIDENTIFIER = NEWID();
DECLARE @JuniorUserId UNIQUEIDENTIFIER = NEWID();
DECLARE @AssistantUserId UNIQUEIDENTIFIER = NEWID();

INSERT INTO Users (UserId, Email, PasswordHash, FirstName, LastName, Role, TenantId, Phone, Department, EmployeeCode, CreatedAt)
VALUES 
(@AdminUserId, '<EMAIL>', '$2b$10$rOzJqHZQQQQQQQQQQQQQQu', 'Rajesh', 'Kumar', 'Admin', @TenantId, '+91-9876543210', 'Management', 'EMP001', DATEADD(DAY, -90, GETUTCDATE())),
(@ManagerUserId, '<EMAIL>', '$2b$10$rOzJqHZQQQQQQQQQQQQQQu', 'Priya', 'Sharma', 'Manager', @TenantId, '+91-9876543211', 'Tax & Compliance', 'EMP002', DATEADD(DAY, -85, GETUTCDATE())),
(@SeniorUserId, '<EMAIL>', '$2b$10$rOzJqHZQQQQQQQQQQQQQQu', 'Amit', 'Patel', 'Senior Associate', @TenantId, '+91-9876543212', 'Audit', 'EMP003', DATEADD(DAY, -70, GETUTCDATE())),
(@JuniorUserId, '<EMAIL>', '$2b$10$rOzJqHZQQQQQQQQQQQQQQu', 'Sneha', 'Gupta', 'Junior Associate', @TenantId, '+91-9876543213', 'Tax & Compliance', 'EMP004', DATEADD(DAY, -60, GETUTCDATE())),
(@AssistantUserId, '<EMAIL>', '$2b$10$rOzJqHZQQQQQQQQQQQQQQu', 'Ravi', 'Singh', 'Assistant', @TenantId, '+91-9876543214', 'Documentation', 'EMP005', DATEADD(DAY, -45, GETUTCDATE()));

-- =====================================================
-- Insert Clients
-- =====================================================

DECLARE @Client1Id UNIQUEIDENTIFIER = NEWID();
DECLARE @Client2Id UNIQUEIDENTIFIER = NEWID();
DECLARE @Client3Id UNIQUEIDENTIFIER = NEWID();
DECLARE @Client4Id UNIQUEIDENTIFIER = NEWID();
DECLARE @Client5Id UNIQUEIDENTIFIER = NEWID();
DECLARE @Client6Id UNIQUEIDENTIFIER = NEWID();
DECLARE @Client7Id UNIQUEIDENTIFIER = NEWID();
DECLARE @Client8Id UNIQUEIDENTIFIER = NEWID();

INSERT INTO Clients (
    ClientId, ClientCode, CompanyName, ContactPerson, Email, Phone, Address, City, State, PinCode,
    GSTNumber, PANNumber, ClientType, BusinessType, AnnualTurnover, CreatedBy, CreatedAt
)
VALUES 
(@Client1Id, 'CLI001', 'ABC Manufacturing Pvt Ltd', 'Suresh Agarwal', '<EMAIL>', '+91-9876501001', 
 '123 Industrial Area, Sector 15', 'Gurgaon', 'Haryana', '122001', '06**********1Z5', '**********', 
 'Company', 'Manufacturing', 50000000.00, @AdminUserId, DATEADD(DAY, -80, GETUTCDATE())),

(@Client2Id, 'CLI002', 'XYZ Traders', 'Meera Jain', '<EMAIL>', '+91-9876501002', 
 '456 Market Street, Karol Bagh', 'New Delhi', 'Delhi', '110005', '07**********2L6', '**********', 
 'Partnership', 'Trading', 25000000.00, @ManagerUserId, DATEADD(DAY, -75, GETUTCDATE())),

(@Client3Id, 'CLI003', 'Tech Solutions India LLP', 'Vikram Reddy', '<EMAIL>', '+91-9876501003', 
 '789 IT Park, Cyber City', 'Hyderabad', 'Telangana', '500081', '36**********3S7', '**********', 
 'LLP', 'IT Services', 75000000.00, @SeniorUserId, DATEADD(DAY, -70, GETUTCDATE())),

(@Client4Id, 'CLI004', 'Green Energy Solutions', 'Anita Verma', '<EMAIL>', '+91-9876501004', 
 '321 Solar Park, MIDC Area', 'Pune', 'Maharashtra', '411019', '27**********4Y8', '**********', 
 'Company', 'Renewable Energy', *********.00, @ManagerUserId, DATEADD(DAY, -65, GETUTCDATE())),

(@Client5Id, 'CLI005', 'Rajesh Kumar (Individual)', 'Rajesh Kumar', '<EMAIL>', '+91-9876501005', 
 '654 Residential Colony, Sector 22', 'Noida', 'Uttar Pradesh', '201301', NULL, '**********', 
 'Individual', 'Professional Services', 1500000.00, @JuniorUserId, DATEADD(DAY, -60, GETUTCDATE())),

(@Client6Id, 'CLI006', 'Mumbai Logistics Pvt Ltd', 'Deepak Shah', '<EMAIL>', '+91-9876501006', 
 '987 Port Area, Nhava Sheva', 'Mumbai', 'Maharashtra', '400707', '27**********5E9', '**********', 
 'Company', 'Logistics', 80000000.00, @SeniorUserId, DATEADD(DAY, -55, GETUTCDATE())),

(@Client7Id, 'CLI007', 'Foodie Restaurant Chain', 'Sanjay Kapoor', '<EMAIL>', '+91-**********', 
 '147 Commercial Complex, CP', 'New Delhi', 'Delhi', '110001', '07**********6L7', '**********', 
 'Company', 'Food & Beverage', 30000000.00, @JuniorUserId, DATEADD(DAY, -50, GETUTCDATE())),

(@Client8Id, 'CLI008', 'Wellness Healthcare Trust', 'Dr. Kavita Nair', '<EMAIL>', '+91-**********', 
 '258 Medical District, Koramangala', 'Bangalore', 'Karnataka', '560034', '29**********7S8', '**********', 
 'Trust', 'Healthcare', 45000000.00, @ManagerUserId, DATEADD(DAY, -45, GETUTCDATE()));

-- =====================================================
-- Insert Compliance Items
-- =====================================================

INSERT INTO ComplianceItems (
    ClientId, ComplianceType, SubType, DueDate, Description, Status, Priority, AssignedTo, CreatedAt, Notes
)
VALUES 
-- ABC Manufacturing compliance items
(@Client1Id, 'GST', 'GSTR-1', '2024-01-11', 'Monthly GST Return - December 2023', 'Completed', 'High', @SeniorUserId, DATEADD(DAY, -25, GETUTCDATE()), 'Filed on time'),
(@Client1Id, 'GST', 'GSTR-3B', '2024-01-20', 'Monthly GST Return - December 2023', 'Completed', 'High', @SeniorUserId, DATEADD(DAY, -25, GETUTCDATE()), 'Filed with late fee'),
(@Client1Id, 'TDS', 'TDS Return', '2024-01-31', 'Quarterly TDS Return - Q3 FY24', 'InProgress', 'Medium', @JuniorUserId, DATEADD(DAY, -20, GETUTCDATE()), 'Data collection in progress'),
(@Client1Id, 'ITR', 'ITR-6', '2024-03-31', 'Income Tax Return FY 2022-23', 'Pending', 'High', @ManagerUserId, DATEADD(DAY, -15, GETUTCDATE()), 'Awaiting financial statements'),

-- XYZ Traders compliance items
(@Client2Id, 'GST', 'GSTR-1', '2024-01-11', 'Monthly GST Return - December 2023', 'Completed', 'High', @JuniorUserId, DATEADD(DAY, -25, GETUTCDATE()), 'Filed successfully'),
(@Client2Id, 'GST', 'GSTR-3B', '2024-01-20', 'Monthly GST Return - December 2023', 'Completed', 'High', @JuniorUserId, DATEADD(DAY, -25, GETUTCDATE()), 'No issues'),
(@Client2Id, 'ITR', 'ITR-5', '2024-03-31', 'Partnership Return FY 2022-23', 'Pending', 'Medium', @SeniorUserId, DATEADD(DAY, -15, GETUTCDATE()), 'Profit & Loss statement pending'),

-- Tech Solutions compliance items
(@Client3Id, 'GST', 'GSTR-1', '2024-01-11', 'Monthly GST Return - December 2023', 'Completed', 'High', @SeniorUserId, DATEADD(DAY, -25, GETUTCDATE()), 'Filed on time'),
(@Client3Id, 'GST', 'GSTR-3B', '2024-01-20', 'Monthly GST Return - December 2023', 'Completed', 'High', @SeniorUserId, DATEADD(DAY, -25, GETUTCDATE()), 'Filed successfully'),
(@Client3Id, 'TDS', 'TDS Return', '2024-01-31', 'Quarterly TDS Return - Q3 FY24', 'InProgress', 'High', @ManagerUserId, DATEADD(DAY, -20, GETUTCDATE()), 'Large volume of transactions'),
(@Client3Id, 'ROC', 'Annual Filing', '2024-02-29', 'Annual Return & Financial Statements', 'Pending', 'Critical', @ManagerUserId, DATEADD(DAY, -10, GETUTCDATE()), 'Board resolution required'),

-- Individual client compliance
(@Client5Id, 'ITR', 'ITR-1', '2024-03-31', 'Individual Income Tax Return FY 2022-23', 'Pending', 'Medium', @JuniorUserId, DATEADD(DAY, -15, GETUTCDATE()), 'Salary and other income sources'),

-- Green Energy compliance items
(@Client4Id, 'GST', 'GSTR-1', '2024-01-11', 'Monthly GST Return - December 2023', 'Overdue', 'Critical', @ManagerUserId, DATEADD(DAY, -25, GETUTCDATE()), 'Client delayed providing data'),
(@Client4Id, 'ESI', 'Monthly Return', '2024-01-15', 'ESI Contribution - December 2023', 'Completed', 'Medium', @AssistantUserId, DATEADD(DAY, -22, GETUTCDATE()), 'Filed online'),
(@Client4Id, 'PF', 'Monthly Return', '2024-01-15', 'PF Contribution - December 2023', 'Completed', 'Medium', @AssistantUserId, DATEADD(DAY, -22, GETUTCDATE()), 'No issues');

-- =====================================================
-- Insert Communication Templates
-- =====================================================

INSERT INTO CommunicationTemplates (TemplateName, TemplateType, Subject, Content, Category, CreatedBy, CreatedAt)
VALUES 
('GST Filing Reminder', 'Email', 'GST Return Filing Due - Action Required', 
'Dear [CLIENT_NAME],

This is a friendly reminder that your GST return filing is due on [DUE_DATE].

Please provide the following documents:
- Sales invoices for the period
- Purchase invoices
- Credit/Debit notes
- Bank statements

Please submit these documents by [SUBMISSION_DATE] to ensure timely filing.

Best regards,
[CA_NAME]
[FIRM_NAME]', 'Reminder', @AdminUserId, DATEADD(DAY, -30, GETUTCDATE())),

('Welcome New Client', 'Email', 'Welcome to [FIRM_NAME] - Your Trusted CA Partner',
'Dear [CLIENT_NAME],

Welcome to [FIRM_NAME]! We are delighted to have you as our client.

Our team is committed to providing you with:
- Timely compliance management
- Expert tax advisory services
- Regular updates on regulatory changes
- Dedicated support for all your financial needs

Your dedicated relationship manager is [MANAGER_NAME] who can be reached at [MANAGER_EMAIL] or [MANAGER_PHONE].

We look forward to a long and successful partnership.

Best regards,
[CA_NAME]
[FIRM_NAME]', 'Welcome', @AdminUserId, DATEADD(DAY, -25, GETUTCDATE())),

('Document Request', 'Email', 'Document Submission Required for [COMPLIANCE_TYPE]',
'Dear [CLIENT_NAME],

We need the following documents to proceed with your [COMPLIANCE_TYPE] filing:

[DOCUMENT_LIST]

Please submit these documents by [DEADLINE] to avoid any delays.

You can:
- Email the documents to [EMAIL]
- Upload them to our client portal
- Drop them off at our office

For any queries, please contact [CONTACT_PERSON] at [CONTACT_NUMBER].

Best regards,
[CA_NAME]
[FIRM_NAME]', 'Document Request', @ManagerUserId, DATEADD(DAY, -20, GETUTCDATE()));

-- =====================================================
-- Insert Tasks
-- =====================================================

INSERT INTO Tasks (
    ClientId, Title, Description, Priority, Status, AssignedTo, CreatedBy, DueDate,
    CreatedAt, Category, EstimatedHours, Tags
)
VALUES
(@Client1Id, 'Prepare Annual Financial Statements', 'Compile and prepare annual financial statements for ABC Manufacturing',
 'High', 'InProgress', @SeniorUserId, @ManagerUserId, DATEADD(DAY, 15, GETUTCDATE()),
 DATEADD(DAY, -10, GETUTCDATE()), 'Audit', 40.0, 'annual,financial,statements'),

(@Client2Id, 'GST Audit Preparation', 'Prepare documents and records for upcoming GST audit',
 'Critical', 'Open', @ManagerUserId, @AdminUserId, DATEADD(DAY, 7, GETUTCDATE()),
 DATEADD(DAY, -5, GETUTCDATE()), 'Compliance', 25.0, 'gst,audit,compliance'),

(@Client3Id, 'Tax Planning Review', 'Review current tax structure and suggest optimization strategies',
 'Medium', 'Open', @SeniorUserId, @ManagerUserId, DATEADD(DAY, 20, GETUTCDATE()),
 DATEADD(DAY, -3, GETUTCDATE()), 'Tax Planning', 15.0, 'tax,planning,optimization'),

(@Client4Id, 'ROC Filing Preparation', 'Prepare and file annual return with ROC',
 'Critical', 'InProgress', @ManagerUserId, @AdminUserId, DATEADD(DAY, 10, GETUTCDATE()),
 DATEADD(DAY, -8, GETUTCDATE()), 'Compliance', 20.0, 'roc,annual,filing'),

(@Client5Id, 'Investment Advisory', 'Provide tax-saving investment advice for FY 2023-24',
 'Low', 'Open', @JuniorUserId, @ManagerUserId, DATEADD(DAY, 30, GETUTCDATE()),
 DATEADD(DAY, -2, GETUTCDATE()), 'Advisory', 8.0, 'investment,tax-saving,advisory');

-- =====================================================
-- Insert Communication Logs
-- =====================================================

INSERT INTO CommunicationLogs (
    ClientId, CommunicationType, Subject, Content, SentBy, SentAt, Status,
    RecipientEmail, TemplateUsed, ResponseReceived
)
VALUES
(@Client1Id, 'Email', 'GST Return Filing Reminder',
 'Dear Mr. Agarwal, This is a reminder for your GST return filing due on 20th Jan 2024...',
 @SeniorUserId, DATEADD(DAY, -5, GETUTCDATE()), 'Sent', '<EMAIL>', 'GST Filing Reminder', 1),

(@Client2Id, 'WhatsApp', 'Document Submission Reminder',
 'Hi Meera, Please submit the pending purchase invoices for GST filing. Thanks!',
 @JuniorUserId, DATEADD(DAY, -3, GETUTCDATE()), 'Delivered', NULL, NULL, 0),

(@Client3Id, 'Email', 'ROC Filing Update',
 'Dear Mr. Reddy, We have initiated the ROC filing process. Board resolution is required...',
 @ManagerUserId, DATEADD(DAY, -2, GETUTCDATE()), 'Sent', '<EMAIL>', NULL, 0),

(@Client4Id, 'Call', 'Urgent: GST Compliance Discussion',
 'Discussed the overdue GST filing and agreed on immediate document submission timeline.',
 @ManagerUserId, DATEADD(DAY, -1, GETUTCDATE()), 'Completed', NULL, NULL, 1),

(@Client5Id, 'Email', 'Welcome to Our Services',
 'Welcome Mr. Kumar! We are excited to serve your tax and compliance needs...',
 @AdminUserId, DATEADD(DAY, -60, GETUTCDATE()), 'Sent', '<EMAIL>', 'Welcome New Client', 1);

-- =====================================================
-- Insert Sample Documents (Metadata only)
-- =====================================================

INSERT INTO Documents (
    ClientId, FileName, OriginalFileName, FileSize, FileType, StoragePath,
    Category, SubCategory, UploadedBy, UploadedAt, Tags, Description
)
VALUES
(@Client1Id, 'abc_mfg_gst_dec2023_001.pdf', 'GST_Return_December_2023.pdf', 2048576, 'PDF',
 '/documents/abc-manufacturing/gst/2023/dec/', 'Tax Returns', 'GST', @SeniorUserId,
 DATEADD(DAY, -20, GETUTCDATE()), 'gst,december,2023', 'GST return for December 2023'),

(@Client1Id, 'abc_mfg_financial_2023_001.xlsx', 'Financial_Statements_2023.xlsx', 5242880, 'XLSX',
 '/documents/abc-manufacturing/financial/2023/', 'Financial Statements', 'Annual', @SeniorUserId,
 DATEADD(DAY, -15, GETUTCDATE()), 'financial,annual,2023', 'Annual financial statements'),

(@Client2Id, 'xyz_traders_invoices_dec2023.zip', 'Purchase_Invoices_December.zip', 10485760, 'ZIP',
 '/documents/xyz-traders/invoices/2023/dec/', 'Invoices', 'Purchase', @JuniorUserId,
 DATEADD(DAY, -18, GETUTCDATE()), 'invoices,purchase,december', 'Purchase invoices for December'),

(@Client3Id, 'tech_solutions_tds_q3_2023.pdf', 'TDS_Certificate_Q3_2023.pdf', 1048576, 'PDF',
 '/documents/tech-solutions/tds/2023/q3/', 'Certificates', 'TDS', @ManagerUserId,
 DATEADD(DAY, -12, GETUTCDATE()), 'tds,certificate,q3', 'TDS certificates for Q3 2023'),

(@Client4Id, 'green_energy_board_resolution.pdf', 'Board_Resolution_ROC_Filing.pdf', 512000, 'PDF',
 '/documents/green-energy/legal/2024/', 'Legal Documents', 'Board Resolution', @ManagerUserId,
 DATEADD(DAY, -8, GETUTCDATE()), 'board,resolution,roc', 'Board resolution for ROC filing'),

(@Client5Id, 'rajesh_kumar_salary_slips.pdf', 'Salary_Slips_2023.pdf', 3145728, 'PDF',
 '/documents/rajesh-kumar/income/2023/', 'Income Documents', 'Salary', @JuniorUserId,
 DATEADD(DAY, -10, GETUTCDATE()), 'salary,income,2023', 'Salary slips for the year 2023');

-- =====================================================
-- Insert Reports
-- =====================================================

INSERT INTO Reports (
    ClientId, ReportType, ReportName, GeneratedBy, GeneratedAt, ReportPeriod,
    PeriodStart, PeriodEnd, FilePath, Status, Notes
)
VALUES
(@Client1Id, 'Financial', 'Monthly Financial Summary - December 2023', @SeniorUserId,
 DATEADD(DAY, -10, GETUTCDATE()), 'Monthly', '2023-12-01', '2023-12-31',
 '/reports/abc-manufacturing/financial/monthly_dec2023.pdf', 'Sent', 'Emailed to client'),

(@Client2Id, 'Tax', 'GST Compliance Report - Q3 2023', @JuniorUserId,
 DATEADD(DAY, -15, GETUTCDATE()), 'Quarterly', '2023-10-01', '2023-12-31',
 '/reports/xyz-traders/tax/gst_q3_2023.pdf', 'Generated', 'Ready for review'),

(@Client3Id, 'KPI', 'Business Performance Dashboard - 2023', @ManagerUserId,
 DATEADD(DAY, -5, GETUTCDATE()), 'Yearly', '2023-01-01', '2023-12-31',
 '/reports/tech-solutions/kpi/annual_2023.pdf', 'Reviewed', 'Presented to management'),

(@Client4Id, 'Compliance', 'Regulatory Compliance Status Report', @ManagerUserId,
 DATEADD(DAY, -7, GETUTCDATE()), 'Custom', '2023-01-01', '2024-01-31',
 '/reports/green-energy/compliance/status_jan2024.pdf', 'Generated', 'Highlights pending items');

PRINT '=====================================================';
PRINT 'TENANT SAMPLE DATA INSERTED SUCCESSFULLY';
PRINT '=====================================================';
PRINT 'Summary of inserted data:';
PRINT '- Users: 5 staff members';
PRINT '- Clients: 8 diverse clients';
PRINT '- Compliance Items: 15+ items with various statuses';
PRINT '- Tasks: 5 ongoing tasks';
PRINT '- Communication Logs: 5 recent communications';
PRINT '- Documents: 6 sample document records';
PRINT '- Reports: 4 generated reports';
PRINT '- Communication Templates: 3 email templates';
PRINT '=====================================================';

GO
