-- =============================================
-- Phase 1: Complete Database Setup for Compliance Calendar Module
-- Creates databases and all required tables
-- =============================================

-- =============================================
-- 1. Create Master Database (TenantRegistry)
-- =============================================
IF NOT EXISTS (SELECT name FROM sys.databases WHERE name = 'TenantRegistry')
BEGIN
    CREATE DATABASE [TenantRegistry];
    PRINT 'TenantRegistry database created.';
END
ELSE
BEGIN
    PRINT 'TenantRegistry database already exists.';
END

USE [TenantRegistry];

-- Create Organizations table for tenant management
IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='Organizations' AND xtype='U')
BEGIN
    CREATE TABLE [dbo].[Organizations] (
        [OrganizationId] UNIQUEIDENTIFIER PRIMARY KEY DEFAULT NEWID(),
        [TenantKey] NVARCHAR(100) NOT NULL UNIQUE,
        [CompanyName] NVARCHAR(200) NOT NULL,
        [DatabaseName] NVARCHAR(100) NOT NULL,
        [ConnectionString] NVARCHAR(500) NOT NULL,
        [IsActive] BIT NOT NULL DEFAULT 1,
        [CreatedAt] DATETIME NOT NULL DEFAULT GETUTCDATE(),
        [UpdatedAt] DATETIME NULL
    );
    
    -- Insert sample tenant
    INSERT INTO [Organizations] (TenantKey, CompanyName, DatabaseName, ConnectionString)
    VALUES ('kumar-associates', 'Kumar & Associates CA', 'KumarAssociatesDB', 
            'Server=localhost;Database=KumarAssociatesDB;Integrated Security=true;TrustServerCertificate=true;');
    
    PRINT 'Organizations table created with sample tenant.';
END

-- =============================================
-- 2. Create Tenant Database
-- =============================================
IF NOT EXISTS (SELECT name FROM sys.databases WHERE name = 'KumarAssociatesDB')
BEGIN
    CREATE DATABASE [KumarAssociatesDB];
    PRINT 'KumarAssociatesDB database created.';
END

USE [KumarAssociatesDB];

-- =============================================
-- 3. Create Users Table
-- =============================================
IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='Users' AND xtype='U')
BEGIN
    CREATE TABLE [dbo].[Users] (
        [UserId] UNIQUEIDENTIFIER PRIMARY KEY DEFAULT NEWID(),
        [Email] NVARCHAR(255) NOT NULL UNIQUE,
        [FirstName] NVARCHAR(100) NOT NULL,
        [LastName] NVARCHAR(100) NOT NULL,
        [PasswordHash] NVARCHAR(255) NOT NULL,
        [Role] NVARCHAR(50) NOT NULL DEFAULT 'Staff',
        [IsActive] BIT NOT NULL DEFAULT 1,
        [CreatedAt] DATETIME NOT NULL DEFAULT GETUTCDATE(),
        [LastLoginAt] DATETIME NULL
    );
    
    -- Insert sample users
    INSERT INTO [Users] (Email, FirstName, LastName, PasswordHash, Role)
    VALUES 
        ('<EMAIL>', 'Admin', 'User', 'hashed_password_admin', 'Admin'),
        ('<EMAIL>', 'Compliance', 'Manager', 'hashed_password_manager', 'Manager'),
        ('<EMAIL>', 'Senior', 'Associate', 'hashed_password_senior', 'Senior'),
        ('<EMAIL>', 'Rahul', 'Sharma', 'hashed_password_junior1', 'Staff'),
        ('<EMAIL>', 'Pooja', 'Gupta', 'hashed_password_junior2', 'Staff'),
        ('<EMAIL>', 'Arjun', 'Patel', 'hashed_password_junior3', 'Staff'),
        ('<EMAIL>', 'Intern', 'Trainee', 'hashed_password_intern', 'Intern');
    
    PRINT 'Users table created with sample users.';
END

-- =============================================
-- 4. Create Clients Table
-- =============================================
IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='Clients' AND xtype='U')
BEGIN
    CREATE TABLE [dbo].[Clients] (
        [ClientId] UNIQUEIDENTIFIER PRIMARY KEY DEFAULT NEWID(),
        [CompanyName] NVARCHAR(200) NOT NULL,
        [ContactPerson] NVARCHAR(100) NULL,
        [Email] NVARCHAR(255) NULL,
        [Phone] NVARCHAR(20) NULL,
        [Address] NVARCHAR(500) NULL,
        [IsActive] BIT NOT NULL DEFAULT 1,
        [CreatedAt] DATETIME NOT NULL DEFAULT GETUTCDATE()
    );
    
    -- Insert sample clients
    INSERT INTO [Clients] (CompanyName, ContactPerson, Email, Phone, Address)
    VALUES 
        ('ABC Manufacturing Ltd', 'Rajesh Kumar', '<EMAIL>', '+91-9876543210', 'Plot 123, Industrial Area, Gurgaon'),
        ('XYZ Services Pvt Ltd', 'Priya Sharma', '<EMAIL>', '+91-9876543211', 'Office 456, Cyber City, Bangalore'),
        ('Tech Solutions Inc', 'Amit Patel', '<EMAIL>', '+91-9876543212', 'Tower A, IT Park, Pune'),
        ('Global Exports Ltd', 'Sunita Gupta', '<EMAIL>', '+91-9876543213', 'Warehouse 789, MIDC, Mumbai'),
        ('Green Energy Corp', 'Vikram Singh', '<EMAIL>', '+91-9876543214', 'Solar Park, Sector 15, Noida'),
        ('Food Processing Co', 'Meera Joshi', '<EMAIL>', '+91-9876543215', 'Factory Road, Indore'),
        ('Textile Mills Ltd', 'Ravi Agarwal', '<EMAIL>', '+91-9876543216', 'Mill Area, Coimbatore'),
        ('Construction Corp', 'Deepak Verma', '<EMAIL>', '+91-9876543217', 'Project Site, Ghaziabad'),
        ('Pharma Industries', 'Kavita Reddy', '<EMAIL>', '+91-9876543218', 'Pharma City, Hyderabad'),
        ('Auto Components Ltd', 'Suresh Yadav', '<EMAIL>', '+91-9876543219', 'Auto Hub, Chennai');
    
    PRINT 'Clients table created with sample clients.';
END

-- =============================================
-- 5. Create ComplianceTypes Table
-- =============================================
IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='ComplianceTypes' AND xtype='U')
BEGIN
    CREATE TABLE [dbo].[ComplianceTypes] (
        [TypeId] UNIQUEIDENTIFIER PRIMARY KEY DEFAULT NEWID(),
        [Name] NVARCHAR(100) NOT NULL UNIQUE,
        [Description] NVARCHAR(MAX) NULL,
        [Category] NVARCHAR(50) NULL,
        [RegulatoryBody] NVARCHAR(100) NULL,
        [DefaultDurationDays] INT NULL,
        [DefaultPriority] NVARCHAR(20) NULL,
        [DefaultEstimatedHours] DECIMAL(5,2) NULL,
        [IsActive] BIT NOT NULL DEFAULT 1,
        [CreatedAt] DATETIME NOT NULL DEFAULT GETUTCDATE(),
        [SortOrder] INT NULL,
        [Color] NVARCHAR(7) NULL,
        [Icon] NVARCHAR(50) NULL,
        
        CONSTRAINT [CK_ComplianceTypes_Priority] CHECK ([DefaultPriority] IN ('Low', 'Medium', 'High', 'Critical') OR [DefaultPriority] IS NULL)
    );
    
    -- Insert compliance types
    INSERT INTO [ComplianceTypes] (
        [Name], [Description], [Category], [RegulatoryBody], [DefaultDurationDays], 
        [DefaultPriority], [DefaultEstimatedHours], [SortOrder], [Color], [Icon]
    )
    VALUES 
        ('Income Tax Return', 'Annual income tax return filing', 'Tax', 'Income Tax Department', 30, 'High', 8.0, 1, '#FF6B6B', 'receipt-tax'),
        ('GST Return - GSTR1', 'Monthly GST return for outward supplies', 'GST', 'GST Council', 11, 'High', 4.0, 2, '#4ECDC4', 'file-text'),
        ('GST Return - GSTR3B', 'Monthly GST return summary', 'GST', 'GST Council', 20, 'High', 6.0, 3, '#45B7D1', 'file-check'),
        ('TDS Return', 'Tax Deducted at Source quarterly return', 'Tax', 'Income Tax Department', 30, 'Medium', 5.0, 4, '#96CEB4', 'minus-circle'),
        ('ESI Return', 'Employee State Insurance monthly return', 'Labor', 'ESIC', 21, 'Medium', 2.0, 5, '#FFEAA7', 'users'),
        ('PF Return', 'Provident Fund monthly return', 'Labor', 'EPFO', 15, 'Medium', 3.0, 6, '#DDA0DD', 'shield'),
        ('ROC Annual Filing', 'Registrar of Companies annual return', 'Corporate', 'MCA', 60, 'High', 12.0, 7, '#98D8C8', 'building'),
        ('Audit Report', 'Annual financial audit report', 'Audit', 'Various', 90, 'Critical', 40.0, 8, '#F7DC6F', 'search'),
        ('VAT Return', 'Value Added Tax return filing', 'Tax', 'State Tax Department', 20, 'Medium', 4.0, 9, '#BB8FCE', 'percent'),
        ('Professional Tax', 'Professional tax payment and return', 'Tax', 'State Government', 10, 'Low', 1.0, 10, '#85C1E9', 'credit-card');
    
    PRINT 'ComplianceTypes table created and populated.';
END

-- =============================================
-- 6. Create Main Compliance Table
-- =============================================
IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='Compliance' AND xtype='U')
BEGIN
    CREATE TABLE [dbo].[Compliance] (
        [ComplianceId] UNIQUEIDENTIFIER PRIMARY KEY DEFAULT NEWID(),
        [ClientId] UNIQUEIDENTIFIER NOT NULL,
        [ComplianceType] NVARCHAR(100) NOT NULL,
        [SubType] NVARCHAR(100) NULL,
        [Description] NVARCHAR(MAX) NULL,
        [DueDate] DATETIME NOT NULL,
        [Status] NVARCHAR(50) NOT NULL DEFAULT 'Pending',
        [Priority] NVARCHAR(20) NOT NULL DEFAULT 'Medium',
        [AssignedTo] UNIQUEIDENTIFIER NULL,
        [CreatedAt] DATETIME NOT NULL DEFAULT GETUTCDATE(),
        [CreatedBy] UNIQUEIDENTIFIER NULL,
        [UpdatedAt] DATETIME NULL,
        [UpdatedBy] UNIQUEIDENTIFIER NULL,
        [CompletedAt] DATETIME NULL,
        [CompletedBy] UNIQUEIDENTIFIER NULL,
        [Notes] NVARCHAR(MAX) NULL,
        [ReminderSent] BIT NOT NULL DEFAULT 0,
        [LastReminderDate] DATETIME NULL,
        [IsActive] BIT NOT NULL DEFAULT 1,
        [EstimatedHours] DECIMAL(5,2) NULL,
        [ActualHours] DECIMAL(5,2) NULL,
        [ComplianceYear] INT NULL,
        [RegulatoryBody] NVARCHAR(100) NULL,
        [IsRecurring] BIT NOT NULL DEFAULT 0,
        [RecurrencePattern] NVARCHAR(50) NULL,
        
        -- Constraints
        CONSTRAINT [CK_Compliance_Status] CHECK ([Status] IN ('Pending', 'In Progress', 'Completed', 'Overdue', 'Cancelled')),
        CONSTRAINT [CK_Compliance_Priority] CHECK ([Priority] IN ('Low', 'Medium', 'High', 'Critical')),
        CONSTRAINT [FK_Compliance_Client] FOREIGN KEY ([ClientId]) REFERENCES [Clients]([ClientId]),
        CONSTRAINT [FK_Compliance_AssignedTo] FOREIGN KEY ([AssignedTo]) REFERENCES [Users]([UserId]),
        CONSTRAINT [FK_Compliance_CreatedBy] FOREIGN KEY ([CreatedBy]) REFERENCES [Users]([UserId])
    );
    
    -- Create indexes
    CREATE INDEX [IX_Compliance_ClientId] ON [Compliance]([ClientId]);
    CREATE INDEX [IX_Compliance_DueDate] ON [Compliance]([DueDate]);
    CREATE INDEX [IX_Compliance_Status] ON [Compliance]([Status]);
    CREATE INDEX [IX_Compliance_AssignedTo] ON [Compliance]([AssignedTo]);
    
    PRINT 'Compliance table created with indexes.';
END

PRINT '';
PRINT '============================================================';
PRINT 'Phase 1: Database Setup Complete!';
PRINT '============================================================';
PRINT 'Databases Created:';
PRINT '  ✅ TenantRegistry (Master database)';
PRINT '  ✅ KumarAssociatesDB (Tenant database)';
PRINT '';
PRINT 'Tables Created:';
PRINT '  ✅ Organizations (1 tenant)';
PRINT '  ✅ Users (7 sample users)';
PRINT '  ✅ Clients (10 sample clients)';
PRINT '  ✅ ComplianceTypes (10 compliance types)';
PRINT '  ✅ Compliance (main table with indexes)';
PRINT '';
PRINT 'Ready for sample data population...';
PRINT '============================================================';
