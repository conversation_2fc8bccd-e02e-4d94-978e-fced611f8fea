# 🚀 Quick Start Guide - CA Portal API

## ⚡ 5-Minute Setup

### Prerequisites (Install if not already available)
1. **[.NET 6.0 SDK](https://dotnet.microsoft.com/download/dotnet/6.0)** - Download and install
2. **[SQL Server Express](https://www.microsoft.com/en-us/sql-server/sql-server-downloads)** - Free edition
3. **[SQL Server Management Studio (SSMS)](https://docs.microsoft.com/en-us/sql/ssms/download-sql-server-management-studio-ssms)** - For database management

### 🎯 Option 1: Automated Setup (Recommended)

**For PowerShell users:**
```powershell
# Navigate to the backend-api directory
cd "d:\DataOps Sync\Projects\CA-Portal-main\backend-api"

# Run the automated setup script
.\setup-and-run.ps1
```

**For Command Prompt users:**
```cmd
# Navigate to the backend-api directory
cd "d:\DataOps Sync\Projects\CA-Portal-main\backend-api"

# Run the automated setup script
setup-and-run.bat
```

### 🎯 Option 2: Manual Setup

#### Step 1: Database Setup (5 minutes)
1. **Open SQL Server Management Studio (SSMS)**
2. **Connect to your SQL Server instance** (usually `localhost` or `localhost\SQLEXPRESS`)
3. **Run these SQL scripts in order:**
   ```sql
   -- 1. Create master database
   -- Execute: database/master-database-schema.sql
   
   -- 2. Insert sample tenants
   -- Execute: database/dummy-data-insert.sql
   
   -- 3. Create tenant database
   -- Execute: database/tenant-database-template.sql
   
   -- 4. Insert sample data
   -- Execute: database/tenant-sample-data.sql
   ```

#### Step 2: Update Connection Strings (1 minute)
Edit `appsettings.json` with your SQL Server details:

**For SQL Server Express:**
```json
{
  "ConnectionStrings": {
    "TenantRegistryConnection": "Server=localhost\\SQLEXPRESS;Database=TenantRegistry;Integrated Security=true;TrustServerCertificate=true;",
    "CA_Portal_kumar_associatesConnection": "Server=localhost\\SQLEXPRESS;Database=CA_Portal_kumar_associates;Integrated Security=true;TrustServerCertificate=true;"
  }
}
```

#### Step 3: Run the API (1 minute)
```bash
# Navigate to backend-api directory
cd "d:\DataOps Sync\Projects\CA-Portal-main\backend-api"

# Restore packages
dotnet restore

# Run the application
dotnet run
```

## 🌐 Access the Application

Once running, open your browser to:
- **Swagger UI**: https://localhost:7000
- **API Documentation**: Interactive API testing interface

## 🧪 Quick Test

### 1. Test Login Endpoint
In Swagger UI, try the `/api/auth/login` endpoint with:
```json
{
  "email": "<EMAIL>",
  "password": "password123",
  "tenantKey": "kumar-associates"
}
```

### 2. Expected Response
```json
{
  "success": true,
  "message": "Login successful",
  "data": {
    "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
    "user": {
      "email": "<EMAIL>",
      "firstName": "Rajesh",
      "lastName": "Kumar",
      "role": "Admin"
    },
    "tenant": {
      "tenantKey": "kumar-associates",
      "organizationName": "Kumar & Associates Chartered Accountants"
    }
  }
}
```

### 3. Test Protected Endpoints
1. Copy the `token` from login response
2. Click "Authorize" button in Swagger UI
3. Enter: `Bearer YOUR_TOKEN_HERE`
4. Try endpoints like `/api/users` or `/api/clients`

## 🔧 Troubleshooting

### Common Issues:

**❌ Port 7000 already in use**
```bash
# Use different port
dotnet run --urls "https://localhost:7001;http://localhost:5001"
```

**❌ Database connection failed**
- Ensure SQL Server is running
- Check connection strings in `appsettings.json`
- Verify databases exist in SSMS

**❌ SSL certificate issues**
```bash
# Trust development certificates
dotnet dev-certs https --trust
```

**❌ Build errors**
```bash
# Clean and rebuild
dotnet clean
dotnet build
```

## 📊 What You Get

✅ **Multi-tenant API** with tenant isolation  
✅ **JWT Authentication** with role-based access  
✅ **Swagger Documentation** for easy testing  
✅ **Database Integration** with ADO.NET  
✅ **Sample Data** for immediate testing  
✅ **Production-ready** architecture  

## 🎯 Next Steps

1. **Explore Swagger UI** - Test all available endpoints
2. **Create Stored Procedures** - See main README.md for required SPs
3. **Integrate with React** - Connect your frontend
4. **Add More Tenants** - Scale to multiple organizations
5. **Deploy to Production** - Ready for cloud deployment

## 📞 Need Help?

If you encounter issues:
1. Check console output for error messages
2. Verify all prerequisites are installed
3. Ensure databases are created correctly
4. Test connection strings in SSMS
5. Check Windows Firewall settings

**The API should be running at https://localhost:7000 within 5 minutes!** 🎉
