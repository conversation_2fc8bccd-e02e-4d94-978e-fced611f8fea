// Test the original completion request that was failing
const https = require('https');

// Disable SSL certificate validation for localhost testing
process.env["NODE_TLS_REJECT_UNAUTHORIZED"] = 0;

async function testOriginalCompletion() {
  console.log('🧪 Testing Original Completion Request...\n');

  try {
    // 1. First login to get token
    console.log('1️⃣ Logging in...');
    const loginPayload = JSON.stringify({
      email: "<EMAIL>",
      password: "password123",
      tenantKey: "kumar-associates"
    });

    const loginResponse = await makeRequest({
      hostname: 'localhost',
      port: 7000,
      path: '/api/auth/login',
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Content-Length': Buffer.byteLength(loginPayload)
      }
    }, loginPayload);

    if (loginResponse.statusCode !== 200 || !loginResponse.data.success) {
      console.log('❌ Login failed:', loginResponse.data);
      return;
    }

    console.log('✅ Login successful!');
    const token = loginResponse.data.data.token;
    const authHeaders = {
      'Authorization': `Bearer ${token}`,
      'Content-Type': 'application/json'
    };

    // 2. Test the original completion request
    console.log('\n2️⃣ Testing Original Completion Request...');
    const complianceId = '50f5fd3d-a55e-4f20-a198-7a9b955c9341'; // Your original ID
    const completePayload = JSON.stringify({
      notes: "Marked as completed"
    });

    const completeResponse = await makeRequest({
      hostname: 'localhost',
      port: 7000,
      path: `/api/compliance/${complianceId}/complete`,
      method: 'POST',
      headers: {
        ...authHeaders,
        'Content-Length': Buffer.byteLength(completePayload)
      }
    }, completePayload);

    console.log(`📊 Status Code: ${completeResponse.statusCode}`);
    console.log('📥 Response:', JSON.stringify(completeResponse.data, null, 2));

    if (completeResponse.statusCode === 200 && completeResponse.data.success) {
      console.log('\n✅ SUCCESS! Original compliance request now works!');
      console.log(`🆔 Compliance ID: ${complianceId}`);
      console.log('📝 Notes: Marked as completed');
    } else if (completeResponse.statusCode === 404) {
      console.log('\n⚠️  Item not found - this is expected if the compliance ID doesn\'t exist');
    } else {
      console.log('\n❌ ERROR: Unexpected response');
      if (completeResponse.data.errors) {
        console.log('Errors:', completeResponse.data.errors);
      }
    }

    // 3. Test with a known existing compliance item
    console.log('\n3️⃣ Testing with a known existing item...');
    
    // First, let's create a new compliance item to test completion
    const createPayload = JSON.stringify({
      clientId: "ea7be4a7-f542-41e3-baa3-0005b145761e",
      complianceType: "Test Completion",
      subType: "API Test",
      description: "Testing completion API functionality",
      dueDate: "2025-06-20T23:59:59Z",
      priority: "Medium",
      assignedTo: "48e45269-09cb-44fc-83df-02e3f43723ea",
      notes: "Created for completion testing"
    });

    const createResponse = await makeRequest({
      hostname: 'localhost',
      port: 7000,
      path: '/api/compliance',
      method: 'POST',
      headers: {
        ...authHeaders,
        'Content-Length': Buffer.byteLength(createPayload)
      }
    }, createPayload);

    if (createResponse.statusCode === 201 && createResponse.data.success) {
      const newComplianceId = createResponse.data.data.complianceId;
      console.log(`✅ Created new compliance item: ${newComplianceId}`);
      
      // Now test completion on the newly created item
      const newCompletePayload = JSON.stringify({
        notes: "Completed via API test - this should work!"
      });

      const newCompleteResponse = await makeRequest({
        hostname: 'localhost',
        port: 7000,
        path: `/api/compliance/${newComplianceId}/complete`,
        method: 'POST',
        headers: {
          ...authHeaders,
          'Content-Length': Buffer.byteLength(newCompletePayload)
        }
      }, newCompletePayload);

      console.log(`📊 Completion Status Code: ${newCompleteResponse.statusCode}`);
      console.log('📥 Completion Response:', JSON.stringify(newCompleteResponse.data, null, 2));

      if (newCompleteResponse.statusCode === 200 && newCompleteResponse.data.success) {
        console.log('\n🎉 PERFECT! Completion API is working correctly!');
        console.log(`✅ Successfully completed compliance item: ${newComplianceId}`);
      } else {
        console.log('\n❌ Completion failed for newly created item');
      }
    } else {
      console.log('❌ Failed to create test compliance item');
    }

  } catch (error) {
    console.error('❌ Test failed with error:', error.message);
  }
}

// Helper function to make API requests
function makeRequest(options, postData = null) {
  return new Promise((resolve, reject) => {
    const req = https.request(options, (res) => {
      let data = '';
      res.on('data', (chunk) => {
        data += chunk;
      });
      res.on('end', () => {
        try {
          const response = JSON.parse(data);
          resolve({ statusCode: res.statusCode, data: response });
        } catch (error) {
          resolve({ statusCode: res.statusCode, data: data });
        }
      });
    });

    req.on('error', (error) => {
      reject(error);
    });

    if (postData) {
      req.write(postData);
    }
    req.end();
  });
}

testOriginalCompletion();
