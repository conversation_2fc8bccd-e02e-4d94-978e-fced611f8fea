namespace CAPortalAPI.Services
{
    public interface IDbService
    {
        /// <summary>
        /// Executes a stored procedure with JSON payload and returns JSON result
        /// </summary>
        /// <param name="storedProcedureName">Name of the stored procedure</param>
        /// <param name="jsonPayload">JSON payload to pass to the stored procedure</param>
        /// <param name="connectionName">Connection string name from appsettings</param>
        /// <returns>JSON result from stored procedure</returns>
        Task<string> ExecuteStoredProcedureAsync(string storedProcedureName, string jsonPayload, string connectionName);

        /// <summary>
        /// Executes a stored procedure with JSON payload and returns deserialized object
        /// </summary>
        /// <typeparam name="T">Type to deserialize the result to</typeparam>
        /// <param name="storedProcedureName">Name of the stored procedure</param>
        /// <param name="jsonPayload">JSON payload to pass to the stored procedure</param>
        /// <param name="connectionName">Connection string name from appsettings</param>
        /// <returns>Deserialized object of type T</returns>
        Task<T?> ExecuteStoredProcedureAsync<T>(string storedProcedureName, string jsonPayload, string connectionName) where T : class;

        /// <summary>
        /// Executes a stored procedure with JSON payload and returns list of deserialized objects
        /// </summary>
        /// <typeparam name="T">Type to deserialize the result to</typeparam>
        /// <param name="storedProcedureName">Name of the stored procedure</param>
        /// <param name="jsonPayload">JSON payload to pass to the stored procedure</param>
        /// <param name="connectionName">Connection string name from appsettings</param>
        /// <returns>List of deserialized objects of type T</returns>
        Task<List<T>> ExecuteStoredProcedureListAsync<T>(string storedProcedureName, string jsonPayload, string connectionName) where T : class;

        /// <summary>
        /// Executes a stored procedure without parameters and returns JSON result
        /// </summary>
        /// <param name="storedProcedureName">Name of the stored procedure</param>
        /// <param name="connectionName">Connection string name from appsettings</param>
        /// <returns>JSON result from stored procedure</returns>
        Task<string> ExecuteStoredProcedureAsync(string storedProcedureName, string connectionName);

        /// <summary>
        /// Executes a stored procedure that doesn't return data (INSERT, UPDATE, DELETE)
        /// </summary>
        /// <param name="storedProcedureName">Name of the stored procedure</param>
        /// <param name="jsonPayload">JSON payload to pass to the stored procedure</param>
        /// <param name="connectionName">Connection string name from appsettings</param>
        /// <returns>Number of rows affected</returns>
        Task<int> ExecuteStoredProcedureNonQueryAsync(string storedProcedureName, string jsonPayload, string connectionName);

        /// <summary>
        /// Executes a stored procedure and returns a scalar value
        /// </summary>
        /// <typeparam name="T">Type of the scalar value</typeparam>
        /// <param name="storedProcedureName">Name of the stored procedure</param>
        /// <param name="jsonPayload">JSON payload to pass to the stored procedure</param>
        /// <param name="connectionName">Connection string name from appsettings</param>
        /// <returns>Scalar value of type T</returns>
        Task<T?> ExecuteStoredProcedureScalarAsync<T>(string storedProcedureName, string jsonPayload, string connectionName);

        // Methods that accept connection string directly (for dynamic tenant connections)

        /// <summary>
        /// Executes a stored procedure with JSON payload and returns deserialized object using direct connection string
        /// </summary>
        /// <typeparam name="T">Type to deserialize the result to</typeparam>
        /// <param name="storedProcedureName">Name of the stored procedure</param>
        /// <param name="jsonPayload">JSON payload to pass to the stored procedure</param>
        /// <param name="connectionString">Direct connection string</param>
        /// <returns>Deserialized object of type T</returns>
        Task<T?> ExecuteStoredProcedureWithConnectionStringAsync<T>(string storedProcedureName, string jsonPayload, string connectionString) where T : class;

        /// <summary>
        /// Executes a stored procedure that doesn't return data using direct connection string
        /// </summary>
        /// <param name="storedProcedureName">Name of the stored procedure</param>
        /// <param name="jsonPayload">JSON payload to pass to the stored procedure</param>
        /// <param name="connectionString">Direct connection string</param>
        /// <returns>Number of rows affected</returns>
        Task<int> ExecuteStoredProcedureNonQueryWithConnectionStringAsync(string storedProcedureName, string jsonPayload, string connectionString);

        /// <summary>
        /// Executes a stored procedure with JSON payload and returns list of deserialized objects using direct connection string
        /// </summary>
        /// <typeparam name="T">Type to deserialize the result to</typeparam>
        /// <param name="storedProcedureName">Name of the stored procedure</param>
        /// <param name="jsonPayload">JSON payload to pass to the stored procedure</param>
        /// <param name="connectionString">Direct connection string</param>
        /// <returns>List of deserialized objects of type T</returns>
        Task<List<T>> ExecuteStoredProcedureListWithConnectionStringAsync<T>(string storedProcedureName, string jsonPayload, string connectionString) where T : class;

        // Special methods for TenantRegistry stored procedures that use @JsonPayload parameter

        /// <summary>
        /// Executes a TenantRegistry stored procedure with @JsonPayload parameter and returns deserialized object
        /// </summary>
        /// <typeparam name="T">Type to deserialize the result to</typeparam>
        /// <param name="storedProcedureName">Name of the stored procedure</param>
        /// <param name="jsonPayload">JSON payload to pass to the stored procedure</param>
        /// <param name="connectionName">Connection string name from appsettings</param>
        /// <returns>Deserialized object of type T</returns>
        Task<T?> ExecuteStoredProcedureForTenantRegistryAsync<T>(string storedProcedureName, string jsonPayload, string connectionName) where T : class;

        /// <summary>
        /// Executes a TenantRegistry stored procedure with @JsonPayload parameter and returns list of deserialized objects
        /// </summary>
        /// <typeparam name="T">Type to deserialize the result to</typeparam>
        /// <param name="storedProcedureName">Name of the stored procedure</param>
        /// <param name="jsonPayload">JSON payload to pass to the stored procedure</param>
        /// <param name="connectionName">Connection string name from appsettings</param>
        /// <returns>List of deserialized objects of type T</returns>
        Task<List<T>> ExecuteStoredProcedureListForTenantRegistryAsync<T>(string storedProcedureName, string jsonPayload, string connectionName) where T : class;
    }
}
