using System.ComponentModel.DataAnnotations;

namespace CAPortalAPI.Models.DTOs
{
    public class TenantDto
    {
        public Guid OrganizationId { get; set; }
        public string TenantKey { get; set; } = string.Empty;
        public string OrganizationName { get; set; } = string.Empty;
        public string Domain { get; set; } = string.Empty;
        public string DatabaseName { get; set; } = string.Empty;
        public string ConnectionString { get; set; } = string.Empty;
        public bool IsActive { get; set; }
        public DateTime CreatedAt { get; set; }
        public DateTime UpdatedAt { get; set; }
        public string SubscriptionTier { get; set; } = string.Empty;
        public int MaxUsers { get; set; }
        public long StorageLimit { get; set; }
    }

    public class CreateTenantRequestDto
    {
        [Required]
        [MinLength(3)]
        [MaxLength(50)]
        public string TenantKey { get; set; } = string.Empty;

        [Required]
        [MinLength(2)]
        [MaxLength(255)]
        public string OrganizationName { get; set; } = string.Empty;

        [Required]
        [MinLength(3)]
        [MaxLength(255)]
        public string Domain { get; set; } = string.Empty;

        [Required]
        public string SubscriptionTier { get; set; } = "Basic";

        public int MaxUsers { get; set; } = 10;
        public long StorageLimit { get; set; } = 5368709120; // 5GB
    }

    public class UpdateTenantRequestDto
    {
        [Required]
        public Guid OrganizationId { get; set; }

        [Required]
        [MinLength(2)]
        [MaxLength(255)]
        public string OrganizationName { get; set; } = string.Empty;

        [Required]
        public string SubscriptionTier { get; set; } = string.Empty;

        public int MaxUsers { get; set; }
        public long StorageLimit { get; set; }
        public bool IsActive { get; set; } = true;
    }

    public class TenantListRequestDto
    {
        public int PageNumber { get; set; } = 1;
        public int PageSize { get; set; } = 20;
        public string? SearchTerm { get; set; }
        public string? SubscriptionTier { get; set; }
        public string? Status { get; set; }
        public bool? IsActive { get; set; }
    }

    public class TenantListResponseDto
    {
        public List<TenantDto> Tenants { get; set; } = new();
        public int TotalCount { get; set; }
        public int PageNumber { get; set; }
        public int PageSize { get; set; }
        public int TotalPages { get; set; }
    }

    public class TenantUsageDto
    {
        public Guid OrganizationId { get; set; }
        public string TenantKey { get; set; } = string.Empty;
        public string OrganizationName { get; set; } = string.Empty;
        public int ActiveUsers { get; set; }
        public int TotalClients { get; set; }
        public int TotalDocuments { get; set; }
        public long StorageUsedBytes { get; set; }
        public decimal StorageUsedGB { get; set; }
        public decimal StorageLimitGB { get; set; }
        public decimal StorageUsagePercent { get; set; }
        public DateTime LastStatsDate { get; set; }
    }
}
