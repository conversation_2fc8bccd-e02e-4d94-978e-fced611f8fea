
import { useState } from "react";
import { Sidebar } from "@/components/Sidebar";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Badge } from "@/components/ui/badge";
import { Search, User, FileText, MessageSquare, Clock, CheckCircle, AlertCircle, Phone, Mail } from "lucide-react";

const ClientHistory = () => {
  const [selectedClient, setSelectedClient] = useState("");
  const [searchQuery, setSearchQuery] = useState("");

  const clients = [
    { id: 1, name: "ABC Enterprises", type: "Corporate", status: "Active" },
    { id: 2, name: "XYZ Corp", type: "MSME", status: "Active" },
    { id: 3, name: "<PERSON>", type: "Individual", status: "Pending" },
    { id: 4, name: "DEF Limited", type: "Corporate", status: "Active" },
    { id: 5, name: "Jane Smith", type: "Individual", status: "Active" },
  ];

  const filings = [
    { id: 1, type: "GST Return", period: "May 2024", status: "Filed", dueDate: "2024-06-20", filedDate: "2024-06-18" },
    { id: 2, type: "TDS Return", period: "Q1 2024", status: "Pending", dueDate: "2024-07-31", filedDate: null },
    { id: 3, type: "ITR Filing", period: "FY 2023-24", status: "Filed", dueDate: "2024-07-31", filedDate: "2024-07-15" },
    { id: 4, type: "Audit Report", period: "FY 2023-24", status: "In Progress", dueDate: "2024-09-30", filedDate: null },
  ];

  const documents = [
    { id: 1, name: "Form 16 - FY 2023-24", type: "Tax Document", uploadDate: "2024-06-01", status: "Verified" },
    { id: 2, name: "GSTR-1 May 2024", type: "GST Return", uploadDate: "2024-06-15", status: "Processed" },
    { id: 3, name: "Bank Statement - May 2024", type: "Financial", uploadDate: "2024-06-10", status: "Pending Review" },
    { id: 4, name: "PAN Card Copy", type: "Identity", uploadDate: "2024-01-15", status: "Verified" },
  ];

  const communications = [
    { id: 1, type: "WhatsApp", message: "GST return filing reminder", date: "2024-06-18", direction: "Outgoing" },
    { id: 2, type: "Email", message: "TDS documentation required", date: "2024-06-15", direction: "Outgoing" },
    { id: 3, type: "Phone", message: "Discussed audit requirements", date: "2024-06-12", direction: "Incoming" },
    { id: 4, type: "WhatsApp", message: "Thank you for the quick filing", date: "2024-06-19", direction: "Incoming" },
  ];

  const getStatusColor = (status: string) => {
    switch (status) {
      case "Filed":
      case "Verified":
      case "Processed":
      case "Active":
        return "bg-green-100 text-green-800";
      case "Pending":
      case "Pending Review":
        return "bg-orange-100 text-orange-800";
      case "In Progress":
        return "bg-blue-100 text-blue-800";
      default:
        return "bg-gray-100 text-gray-800";
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case "Filed":
      case "Verified":
      case "Processed":
        return <CheckCircle className="h-4 w-4 text-green-600" />;
      case "Pending":
      case "Pending Review":
        return <Clock className="h-4 w-4 text-orange-600" />;
      case "In Progress":
        return <AlertCircle className="h-4 w-4 text-blue-600" />;
      default:
        return null;
    }
  };

  const getCommunicationIcon = (type: string) => {
    switch (type) {
      case "WhatsApp":
        return <MessageSquare className="h-4 w-4 text-green-600" />;
      case "Email":
        return <Mail className="h-4 w-4 text-blue-600" />;
      case "Phone":
        return <Phone className="h-4 w-4 text-purple-600" />;
      default:
        return null;
    }
  };

  const filteredClients = clients.filter(client =>
    client.name.toLowerCase().includes(searchQuery.toLowerCase())
  );

  return (
    <div className="flex min-h-screen bg-gray-50">
      <Sidebar />
      
      <main className="flex-1 ml-64 p-8">
        <div className="max-w-7xl mx-auto">
          {/* Header */}
          <div className="mb-8">
            <h1 className="text-3xl font-bold text-gray-900 mb-2">
              Client History View
            </h1>
            <p className="text-gray-600 text-lg">
              Complete view of client filings, documents, and communication history
            </p>
          </div>

          {/* Client Selection */}
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-6 mb-8">
            <Card className="lg:col-span-1">
              <CardHeader>
                <CardTitle className="flex items-center">
                  <User className="mr-2 h-5 w-5" />
                  Select Client
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="relative">
                  <Search className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
                  <Input
                    placeholder="Search clients..."
                    value={searchQuery}
                    onChange={(e) => setSearchQuery(e.target.value)}
                    className="pl-10"
                  />
                </div>
                
                <div className="space-y-2 max-h-96 overflow-y-auto">
                  {filteredClients.map((client) => (
                    <div
                      key={client.id}
                      onClick={() => setSelectedClient(client.name)}
                      className={`p-3 rounded-lg border cursor-pointer transition-colors ${
                        selectedClient === client.name
                          ? "border-blue-500 bg-blue-50"
                          : "border-gray-200 hover:bg-gray-50"
                      }`}
                    >
                      <div className="flex items-center justify-between">
                        <div>
                          <p className="font-medium text-gray-900">{client.name}</p>
                          <p className="text-sm text-gray-500">{client.type}</p>
                        </div>
                        <Badge className={getStatusColor(client.status)}>
                          {client.status}
                        </Badge>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>

            {/* Client Details */}
            <Card className="lg:col-span-2">
              <CardHeader>
                <CardTitle>
                  {selectedClient || "Select a client to view history"}
                </CardTitle>
                {selectedClient && (
                  <CardDescription>
                    Complete history and current status
                  </CardDescription>
                )}
              </CardHeader>
              <CardContent>
                {selectedClient ? (
                  <Tabs defaultValue="filings" className="w-full">
                    <TabsList className="grid w-full grid-cols-3">
                      <TabsTrigger value="filings">Filings</TabsTrigger>
                      <TabsTrigger value="documents">Documents</TabsTrigger>
                      <TabsTrigger value="communications">Communications</TabsTrigger>
                    </TabsList>
                    
                    <TabsContent value="filings" className="space-y-4">
                      {filings.map((filing) => (
                        <div key={filing.id} className="flex items-center justify-between p-4 bg-white border rounded-lg">
                          <div className="flex items-center space-x-4">
                            {getStatusIcon(filing.status)}
                            <div>
                              <p className="font-medium text-gray-900">{filing.type}</p>
                              <p className="text-sm text-gray-500">Period: {filing.period}</p>
                              <p className="text-sm text-gray-500">Due: {filing.dueDate}</p>
                            </div>
                          </div>
                          <div className="text-right">
                            <Badge className={getStatusColor(filing.status)}>
                              {filing.status}
                            </Badge>
                            {filing.filedDate && (
                              <p className="text-sm text-gray-500 mt-1">Filed: {filing.filedDate}</p>
                            )}
                          </div>
                        </div>
                      ))}
                    </TabsContent>
                    
                    <TabsContent value="documents" className="space-y-4">
                      {documents.map((document) => (
                        <div key={document.id} className="flex items-center justify-between p-4 bg-white border rounded-lg">
                          <div className="flex items-center space-x-4">
                            <FileText className="h-5 w-5 text-gray-400" />
                            <div>
                              <p className="font-medium text-gray-900">{document.name}</p>
                              <p className="text-sm text-gray-500">{document.type}</p>
                              <p className="text-sm text-gray-500">Uploaded: {document.uploadDate}</p>
                            </div>
                          </div>
                          <Badge className={getStatusColor(document.status)}>
                            {document.status}
                          </Badge>
                        </div>
                      ))}
                    </TabsContent>
                    
                    <TabsContent value="communications" className="space-y-4">
                      {communications.map((comm) => (
                        <div key={comm.id} className="flex items-start space-x-4 p-4 bg-white border rounded-lg">
                          {getCommunicationIcon(comm.type)}
                          <div className="flex-1">
                            <div className="flex items-center justify-between">
                              <p className="font-medium text-gray-900">{comm.type}</p>
                              <div className="flex items-center space-x-2">
                                <Badge variant={comm.direction === "Outgoing" ? "default" : "secondary"}>
                                  {comm.direction}
                                </Badge>
                                <span className="text-sm text-gray-500">{comm.date}</span>
                              </div>
                            </div>
                            <p className="text-sm text-gray-600 mt-1">{comm.message}</p>
                          </div>
                        </div>
                      ))}
                    </TabsContent>
                  </Tabs>
                ) : (
                  <div className="text-center py-12">
                    <User className="mx-auto h-12 w-12 text-gray-400" />
                    <p className="text-gray-500 mt-2">Select a client from the list to view their complete history</p>
                  </div>
                )}
              </CardContent>
            </Card>
          </div>
        </div>
      </main>
    </div>
  );
};

export default ClientHistory;
