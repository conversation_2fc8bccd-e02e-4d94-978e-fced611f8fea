
import { useState } from "react";
import { Sidebar } from "@/components/Sidebar";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Upload, FileText, Download, CheckCircle, Clock, AlertCircle } from "lucide-react";
import { useToast } from "@/hooks/use-toast";

const FilingAssistant = () => {
  const [selectedFiles, setSelectedFiles] = useState<FileList | null>(null);
  const [returnType, setReturnType] = useState("");
  const [clientName, setClientName] = useState("");
  const [isProcessing, setIsProcessing] = useState(false);
  const { toast } = useToast();

  const handleFileUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    const files = event.target.files;
    setSelectedFiles(files);
  };

  const handleGenerateReturn = async () => {
    if (!selectedFiles || !returnType || !clientName) {
      toast({
        title: "Missing Information",
        description: "Please fill in all fields and upload invoices.",
        variant: "destructive",
      });
      return;
    }

    setIsProcessing(true);
    
    // Simulate processing time
    setTimeout(() => {
      setIsProcessing(false);
      toast({
        title: "Return Generated Successfully",
        description: `${returnType} return file generated for ${clientName}`,
      });
      
      // Reset form
      setSelectedFiles(null);
      setReturnType("");
      setClientName("");
      const fileInput = document.getElementById('file-upload') as HTMLInputElement;
      if (fileInput) fileInput.value = '';
    }, 3000);
  };

  const recentReturns = [
    { id: 1, client: "ABC Enterprises", type: "GSTR-1", status: "Completed", date: "2024-06-01", size: "2.3 MB" },
    { id: 2, client: "XYZ Corp", type: "TDS Return", status: "Processing", date: "2024-06-02", size: "1.8 MB" },
    { id: 3, client: "DEF Limited", type: "GSTR-3B", status: "Completed", date: "2024-05-30", size: "3.1 MB" },
    { id: 4, client: "GHI Industries", type: "TDS Return", status: "Failed", date: "2024-05-28", size: "2.7 MB" },
  ];

  const getStatusIcon = (status: string) => {
    switch (status) {
      case "Completed":
        return <CheckCircle className="h-4 w-4 text-green-600" />;
      case "Processing":
        return <Clock className="h-4 w-4 text-orange-600" />;
      case "Failed":
        return <AlertCircle className="h-4 w-4 text-red-600" />;
      default:
        return null;
    }
  };

  return (
    <div className="flex min-h-screen bg-gray-50">
      <Sidebar />
      
      <main className="flex-1 ml-64 p-8">
        <div className="max-w-7xl mx-auto">
          {/* Header */}
          <div className="mb-8">
            <h1 className="text-3xl font-bold text-gray-900 mb-2">
              Filing Assistant for Return Formats
            </h1>
            <p className="text-gray-600 text-lg">
              Upload invoices and generate GST/TDS return files automatically
            </p>
          </div>

          {/* Stats Cards */}
          <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
            <Card>
              <CardContent className="p-6">
                <div className="flex items-center">
                  <div className="p-2 bg-green-100 rounded-lg">
                    <FileText className="h-6 w-6 text-green-600" />
                  </div>
                  <div className="ml-4">
                    <p className="text-sm font-medium text-gray-600">Returns Generated</p>
                    <p className="text-2xl font-bold text-gray-900">342</p>
                  </div>
                </div>
              </CardContent>
            </Card>
            
            <Card>
              <CardContent className="p-6">
                <div className="flex items-center">
                  <div className="p-2 bg-blue-100 rounded-lg">
                    <Upload className="h-6 w-6 text-blue-600" />
                  </div>
                  <div className="ml-4">
                    <p className="text-sm font-medium text-gray-600">Files Processed</p>
                    <p className="text-2xl font-bold text-gray-900">1,247</p>
                  </div>
                </div>
              </CardContent>
            </Card>
            
            <Card>
              <CardContent className="p-6">
                <div className="flex items-center">
                  <div className="p-2 bg-orange-100 rounded-lg">
                    <Clock className="h-6 w-6 text-orange-600" />
                  </div>
                  <div className="ml-4">
                    <p className="text-sm font-medium text-gray-600">Hours Saved</p>
                    <p className="text-2xl font-bold text-gray-900">684</p>
                  </div>
                </div>
              </CardContent>
            </Card>
            
            <Card>
              <CardContent className="p-6">
                <div className="flex items-center">
                  <div className="p-2 bg-purple-100 rounded-lg">
                    <CheckCircle className="h-6 w-6 text-purple-600" />
                  </div>
                  <div className="ml-4">
                    <p className="text-sm font-medium text-gray-600">Success Rate</p>
                    <p className="text-2xl font-bold text-gray-900">98.5%</p>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>

          <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
            {/* Generate New Return */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center">
                  <Upload className="mr-2 h-5 w-5" />
                  Generate New Return
                </CardTitle>
                <CardDescription>
                  Upload invoices and generate return files automatically
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-6">
                <div className="space-y-2">
                  <Label htmlFor="clientName">Client Name</Label>
                  <Input
                    id="clientName"
                    placeholder="Enter client name"
                    value={clientName}
                    onChange={(e) => setClientName(e.target.value)}
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="returnType">Return Type</Label>
                  <Select value={returnType} onValueChange={setReturnType}>
                    <SelectTrigger>
                      <SelectValue placeholder="Select return type" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="gstr-1">GSTR-1 (Outward Supplies)</SelectItem>
                      <SelectItem value="gstr-3b">GSTR-3B (Monthly Return)</SelectItem>
                      <SelectItem value="gstr-9">GSTR-9 (Annual Return)</SelectItem>
                      <SelectItem value="tds-quarterly">TDS Quarterly Return</SelectItem>
                      <SelectItem value="tds-annual">TDS Annual Return</SelectItem>
                      <SelectItem value="tcs-return">TCS Return</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="file-upload">Upload Invoices/Documents</Label>
                  <div className="border-2 border-dashed border-gray-300 rounded-lg p-6 text-center">
                    <Upload className="mx-auto h-12 w-12 text-gray-400" />
                    <div className="mt-2">
                      <label htmlFor="file-upload" className="cursor-pointer">
                        <span className="text-blue-600 hover:text-blue-500">Upload files</span>
                        <span className="text-gray-500"> or drag and drop</span>
                      </label>
                      <input
                        id="file-upload"
                        type="file"
                        multiple
                        accept=".pdf,.xlsx,.xls,.csv"
                        className="hidden"
                        onChange={handleFileUpload}
                      />
                    </div>
                    <p className="text-xs text-gray-500 mt-2">
                      PDF, Excel, CSV up to 10MB each
                    </p>
                  </div>
                  {selectedFiles && (
                    <div className="mt-2">
                      <p className="text-sm text-gray-600">
                        {selectedFiles.length} file(s) selected
                      </p>
                      {Array.from(selectedFiles).map((file, index) => (
                        <p key={index} className="text-xs text-gray-500">
                          {file.name} ({(file.size / 1024 / 1024).toFixed(2)} MB)
                        </p>
                      ))}
                    </div>
                  )}
                </div>

                <Button 
                  onClick={handleGenerateReturn} 
                  className="w-full"
                  disabled={isProcessing}
                >
                  {isProcessing ? (
                    <>
                      <Clock className="mr-2 h-4 w-4 animate-spin" />
                      Processing...
                    </>
                  ) : (
                    <>
                      <FileText className="mr-2 h-4 w-4" />
                      Generate Return File
                    </>
                  )}
                </Button>
              </CardContent>
            </Card>

            {/* Recent Returns */}
            <Card>
              <CardHeader>
                <CardTitle>Recent Return Files</CardTitle>
                <CardDescription>
                  Latest generated return files
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {recentReturns.map((returnFile) => (
                    <div key={returnFile.id} className="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
                      <div className="flex items-center space-x-4">
                        {getStatusIcon(returnFile.status)}
                        <div>
                          <p className="font-medium text-gray-900">{returnFile.client}</p>
                          <p className="text-sm text-gray-500">{returnFile.type} • {returnFile.size}</p>
                        </div>
                      </div>
                      <div className="text-right">
                        <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                          returnFile.status === "Completed" 
                            ? "bg-green-100 text-green-800" 
                            : returnFile.status === "Processing"
                            ? "bg-orange-100 text-orange-800"
                            : "bg-red-100 text-red-800"
                        }`}>
                          {returnFile.status}
                        </span>
                        <p className="text-sm text-gray-500 mt-1">{returnFile.date}</p>
                        {returnFile.status === "Completed" && (
                          <Button variant="ghost" size="sm" className="mt-1">
                            <Download className="h-3 w-3 mr-1" />
                            Download
                          </Button>
                        )}
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </main>
    </div>
  );
};

export default FilingAssistant;
