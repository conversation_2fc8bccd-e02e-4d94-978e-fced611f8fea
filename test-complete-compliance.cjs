// Test Complete Compliance API Endpoint
const https = require('https');

// Disable SSL certificate validation for localhost testing
process.env["NODE_TLS_REJECT_UNAUTHORIZED"] = 0;

async function testCompleteCompliance() {
  console.log('🧪 Testing Complete Compliance API...\n');

  try {
    // 1. First login to get token
    console.log('1️⃣ Logging in...');
    const loginPayload = JSON.stringify({
      email: "<EMAIL>",
      password: "password123",
      tenantKey: "kumar-associates"
    });

    const loginResponse = await makeRequest({
      hostname: 'localhost',
      port: 7000,
      path: '/api/auth/login',
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Content-Length': Buffer.byteLength(loginPayload)
      }
    }, loginPayload);

    if (loginResponse.statusCode !== 200 || !loginResponse.data.success) {
      console.log('❌ Login failed:', loginResponse.data);
      return;
    }

    console.log('✅ Login successful!');
    const token = loginResponse.data.data.token;
    const authHeaders = {
      'Authorization': `Bearer ${token}`,
      'Content-Type': 'application/json'
    };

    // 2. Test complete compliance API
    console.log('\n2️⃣ Testing Complete Compliance API...');
    const complianceId = 'D7F59DB9-CA4A-4134-8F1A-982654D2118E'; // ITR item from database (Pending status)
    const completePayload = JSON.stringify({
      notes: "Marked as completed via API test"
    });

    const completeResponse = await makeRequest({
      hostname: 'localhost',
      port: 7000,
      path: `/api/compliance/${complianceId}/complete`,
      method: 'POST',
      headers: {
        ...authHeaders,
        'Content-Length': Buffer.byteLength(completePayload)
      }
    }, completePayload);

    console.log(`📊 Status Code: ${completeResponse.statusCode}`);
    console.log('📥 Response:', JSON.stringify(completeResponse.data, null, 2));

    if (completeResponse.statusCode === 200 && completeResponse.data.success) {
      console.log('\n✅ SUCCESS! Compliance item marked as completed!');
      console.log(`🆔 Compliance ID: ${complianceId}`);
      console.log('📝 Notes: Marked as completed via API test');
    } else {
      console.log('\n❌ ERROR: Failed to mark compliance as completed');
      if (completeResponse.data.errors) {
        console.log('Errors:', completeResponse.data.errors);
      }
    }

    // 3. Verify the status was updated
    console.log('\n3️⃣ Verifying status update...');
    const verifyResponse = await makeRequest({
      hostname: 'localhost',
      port: 7000,
      path: '/api/compliance?pageSize=50',
      method: 'GET',
      headers: authHeaders
    });

    if (verifyResponse.statusCode === 200 && verifyResponse.data.success) {
      const updatedItem = verifyResponse.data.data.complianceItems.find(item => 
        item.complianceId === complianceId
      );
      
      if (updatedItem) {
        console.log('✅ Status verification successful!');
        console.log(`📊 Current Status: ${updatedItem.status}`);
        console.log(`📅 Completed At: ${updatedItem.completedAt || 'Not set'}`);
        console.log(`📝 Notes: ${updatedItem.notes || 'No notes'}`);
      } else {
        console.log('❌ Could not find the updated compliance item');
      }
    } else {
      console.log('❌ Failed to verify status update');
    }

  } catch (error) {
    console.error('❌ Test failed with error:', error.message);
  }
}

// Helper function to make API requests
function makeRequest(options, postData = null) {
  return new Promise((resolve, reject) => {
    const req = https.request(options, (res) => {
      let data = '';
      res.on('data', (chunk) => {
        data += chunk;
      });
      res.on('end', () => {
        try {
          const response = JSON.parse(data);
          resolve({ statusCode: res.statusCode, data: response });
        } catch (error) {
          resolve({ statusCode: res.statusCode, data: data });
        }
      });
    });

    req.on('error', (error) => {
      reject(error);
    });

    if (postData) {
      req.write(postData);
    }
    req.end();
  });
}

testCompleteCompliance();
