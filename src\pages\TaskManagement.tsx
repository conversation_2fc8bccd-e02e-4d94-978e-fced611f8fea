
import { useState } from "react";
import { Sidebar } from "@/components/Sidebar";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { Badge } from "@/components/ui/badge";
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Plus, Search, Clock, CheckCircle, AlertCircle, User, Edit, Trash2, Eye } from "lucide-react";
import { useToast } from "@/hooks/use-toast";

interface Task {
  id: number;
  title: string;
  description: string;
  assignee: string;
  client: string;
  status: "Pending" | "In Progress" | "Completed";
  priority: "High" | "Medium" | "Low";
  dueDate: string;
  createdDate: string;
  notes: string;
}

const TaskManagement = () => {
  const [searchTerm, setSearchTerm] = useState("");
  const [showCreateTask, setShowCreateTask] = useState(false);
  const [editingTask, setEditingTask] = useState<Task | null>(null);
  const [viewingTask, setViewingTask] = useState<Task | null>(null);
  const { toast } = useToast();

  const [tasks, setTasks] = useState<Task[]>([
    {
      id: 1,
      title: "Prepare GSTR-1 for ABC Enterprises",
      description: "Monthly GST return filing for March 2024",
      assignee: "Priya Sharma",
      client: "ABC Enterprises",
      status: "In Progress",
      priority: "High",
      dueDate: "2024-04-15",
      createdDate: "2024-04-01",
      notes: "Client has provided all invoices. Processing in GSTN portal."
    },
    {
      id: 2,
      title: "TDS Return Filing - XYZ Ltd",
      description: "Quarterly TDS return for Q4 2023-24",
      assignee: "Rahul Patel",
      client: "XYZ Ltd",
      status: "Completed",
      priority: "Medium",
      dueDate: "2024-04-10",
      createdDate: "2024-03-25",
      notes: "Filed successfully. Acknowledgment received."
    },
    {
      id: 3,
      title: "Audit Report Preparation",
      description: "Annual audit for financial year 2023-24",
      assignee: "Anjali Gupta",
      client: "Tech Solutions Pvt Ltd",
      status: "Pending",
      priority: "High",
      dueDate: "2024-04-30",
      createdDate: "2024-04-02",
      notes: "Waiting for bank statements from client."
    },
    {
      id: 4,
      title: "ITR Filing - Individual",
      description: "Income tax return for Mr. Suresh Kumar",
      assignee: "Vikash Singh",
      client: "Suresh Kumar",
      status: "In Progress",
      priority: "Low",
      dueDate: "2024-04-20",
      createdDate: "2024-04-03",
      notes: "Form 16 received. Computing tax liability."
    }
  ]);

  const [newTask, setNewTask] = useState<Partial<Task>>({
    title: "",
    description: "",
    assignee: "",
    client: "",
    priority: "Medium",
    dueDate: "",
    notes: ""
  });

  const staff = ["Priya Sharma", "Rahul Patel", "Anjali Gupta", "Vikash Singh", "Neha Agarwal"];

  const handleCreateTask = () => {
    if (!newTask.title || !newTask.assignee || !newTask.client || !newTask.dueDate) {
      toast({
        title: "Missing Information",
        description: "Please fill in all required fields",
        variant: "destructive"
      });
      return;
    }

    const task: Task = {
      id: Math.max(...tasks.map(t => t.id)) + 1,
      title: newTask.title!,
      description: newTask.description || "",
      assignee: newTask.assignee!,
      client: newTask.client!,
      status: "Pending",
      priority: newTask.priority as "High" | "Medium" | "Low",
      dueDate: newTask.dueDate!,
      createdDate: new Date().toISOString().split('T')[0],
      notes: newTask.notes || ""
    };

    setTasks([...tasks, task]);
    setNewTask({
      title: "",
      description: "",
      assignee: "",
      client: "",
      priority: "Medium",
      dueDate: "",
      notes: ""
    });
    setShowCreateTask(false);

    toast({
      title: "Task Created",
      description: `Task assigned to ${task.assignee} successfully`
    });
  };

  const handleUpdateTask = () => {
    if (!editingTask) return;

    setTasks(tasks.map(task => 
      task.id === editingTask.id ? editingTask : task
    ));
    setEditingTask(null);

    toast({
      title: "Task Updated",
      description: "Task has been updated successfully"
    });
  };

  const handleDeleteTask = (id: number) => {
    setTasks(tasks.filter(task => task.id !== id));
    toast({
      title: "Task Deleted",
      description: "Task has been deleted successfully"
    });
  };

  const handleStatusChange = (id: number, newStatus: "Pending" | "In Progress" | "Completed") => {
    setTasks(tasks.map(task => 
      task.id === id ? { ...task, status: newStatus } : task
    ));
    toast({
      title: "Status Updated",
      description: `Task status changed to ${newStatus}`
    });
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case "Completed": return "bg-green-100 text-green-800";
      case "In Progress": return "bg-blue-100 text-blue-800";
      case "Pending": return "bg-yellow-100 text-yellow-800";
      default: return "bg-gray-100 text-gray-800";
    }
  };

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case "High": return "bg-red-100 text-red-800";
      case "Medium": return "bg-orange-100 text-orange-800";
      case "Low": return "bg-green-100 text-green-800";
      default: return "bg-gray-100 text-gray-800";
    }
  };

  const filteredTasks = tasks.filter(task =>
    task.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
    task.client.toLowerCase().includes(searchTerm.toLowerCase()) ||
    task.assignee.toLowerCase().includes(searchTerm.toLowerCase())
  );

  const taskStats = {
    total: tasks.length,
    completed: tasks.filter(t => t.status === "Completed").length,
    inProgress: tasks.filter(t => t.status === "In Progress").length,
    pending: tasks.filter(t => t.status === "Pending").length
  };

  const TaskForm = ({ task, setTask, isEdit = false }: { 
    task: Partial<Task>, 
    setTask: (task: Partial<Task>) => void,
    isEdit?: boolean 
  }) => (
    <div className="space-y-4">
      <div>
        <label className="block text-sm font-medium text-gray-700 mb-2">Task Title *</label>
        <Input
          value={task.title || ""}
          onChange={(e) => setTask({...task, title: e.target.value})}
          placeholder="e.g., Prepare GSTR-1 for Client"
        />
      </div>
      <div>
        <label className="block text-sm font-medium text-gray-700 mb-2">Client Name *</label>
        <Input
          value={task.client || ""}
          onChange={(e) => setTask({...task, client: e.target.value})}
          placeholder="e.g., ABC Enterprises"
        />
      </div>
      <div className="grid grid-cols-2 gap-4">
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">Assign To *</label>
          <Select value={task.assignee} onValueChange={(value) => setTask({...task, assignee: value})}>
            <SelectTrigger>
              <SelectValue placeholder="Select Staff Member" />
            </SelectTrigger>
            <SelectContent>
              {staff.map(member => (
                <SelectItem key={member} value={member}>{member}</SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">Priority</label>
          <Select value={task.priority} onValueChange={(value) => setTask({...task, priority: value as any})}>
            <SelectTrigger>
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="High">High</SelectItem>
              <SelectItem value="Medium">Medium</SelectItem>
              <SelectItem value="Low">Low</SelectItem>
            </SelectContent>
          </Select>
        </div>
      </div>
      <div>
        <label className="block text-sm font-medium text-gray-700 mb-2">Due Date *</label>
        <Input
          type="date"
          value={task.dueDate || ""}
          onChange={(e) => setTask({...task, dueDate: e.target.value})}
        />
      </div>
      <div>
        <label className="block text-sm font-medium text-gray-700 mb-2">Task Description</label>
        <Textarea
          value={task.description || ""}
          onChange={(e) => setTask({...task, description: e.target.value})}
          placeholder="Detailed description of the task..."
          rows={3}
        />
      </div>
      {isEdit && (
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">Status</label>
          <Select value={task.status} onValueChange={(value) => setTask({...task, status: value as any})}>
            <SelectTrigger>
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="Pending">Pending</SelectItem>
              <SelectItem value="In Progress">In Progress</SelectItem>
              <SelectItem value="Completed">Completed</SelectItem>
            </SelectContent>
          </Select>
        </div>
      )}
      <div>
        <label className="block text-sm font-medium text-gray-700 mb-2">Notes</label>
        <Textarea
          value={task.notes || ""}
          onChange={(e) => setTask({...task, notes: e.target.value})}
          placeholder="Additional notes or comments..."
          rows={2}
        />
      </div>
    </div>
  );

  return (
    <div className="flex min-h-screen bg-gray-50">
      <Sidebar />
      
      <main className="flex-1 ml-64 p-8">
        <div className="max-w-7xl mx-auto">
          {/* Header */}
          <div className="flex justify-between items-center mb-8">
            <div>
              <h1 className="text-3xl font-bold text-gray-900 mb-2">Task Assignment & Tracking</h1>
              <p className="text-gray-600">Assign work to staff, track completion, and manage internal notes</p>
            </div>
            <Dialog open={showCreateTask} onOpenChange={setShowCreateTask}>
              <DialogTrigger asChild>
                <Button className="bg-blue-600 hover:bg-blue-700">
                  <Plus className="h-4 w-4 mr-2" />
                  Create Task
                </Button>
              </DialogTrigger>
              <DialogContent className="max-w-2xl">
                <DialogHeader>
                  <DialogTitle>Create New Task</DialogTitle>
                  <DialogDescription>Assign a new task to your team members</DialogDescription>
                </DialogHeader>
                <TaskForm task={newTask} setTask={setNewTask} />
                <div className="flex justify-end space-x-4 mt-6">
                  <Button variant="outline" onClick={() => setShowCreateTask(false)}>
                    Cancel
                  </Button>
                  <Button onClick={handleCreateTask} className="bg-blue-600 hover:bg-blue-700">
                    Create Task
                  </Button>
                </div>
              </DialogContent>
            </Dialog>
          </div>

          {/* Stats Cards */}
          <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
            <Card>
              <CardContent className="p-6">
                <div className="flex items-center">
                  <Clock className="h-8 w-8 text-blue-600" />
                  <div className="ml-4">
                    <p className="text-sm font-medium text-gray-600">Total Tasks</p>
                    <p className="text-2xl font-bold text-gray-900">{taskStats.total}</p>
                  </div>
                </div>
              </CardContent>
            </Card>
            
            <Card>
              <CardContent className="p-6">
                <div className="flex items-center">
                  <CheckCircle className="h-8 w-8 text-green-600" />
                  <div className="ml-4">
                    <p className="text-sm font-medium text-gray-600">Completed</p>
                    <p className="text-2xl font-bold text-gray-900">{taskStats.completed}</p>
                  </div>
                </div>
              </CardContent>
            </Card>
            
            <Card>
              <CardContent className="p-6">
                <div className="flex items-center">
                  <AlertCircle className="h-8 w-8 text-blue-600" />
                  <div className="ml-4">
                    <p className="text-sm font-medium text-gray-600">In Progress</p>
                    <p className="text-2xl font-bold text-gray-900">{taskStats.inProgress}</p>
                  </div>
                </div>
              </CardContent>
            </Card>
            
            <Card>
              <CardContent className="p-6">
                <div className="flex items-center">
                  <Clock className="h-8 w-8 text-yellow-600" />
                  <div className="ml-4">
                    <p className="text-sm font-medium text-gray-600">Pending</p>
                    <p className="text-2xl font-bold text-gray-900">{taskStats.pending}</p>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Search and Filter */}
          <Card className="mb-8">
            <CardContent className="p-6">
              <div className="flex items-center space-x-4">
                <div className="flex-1">
                  <div className="relative">
                    <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                    <Input
                      placeholder="Search tasks by title, client, or assignee..."
                      value={searchTerm}
                      onChange={(e) => setSearchTerm(e.target.value)}
                      className="pl-10"
                    />
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Tasks Table */}
          <Card>
            <CardHeader>
              <CardTitle>Task List</CardTitle>
              <CardDescription>All assigned tasks and their current status</CardDescription>
            </CardHeader>
            <CardContent>
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Task</TableHead>
                    <TableHead>Client</TableHead>
                    <TableHead>Assignee</TableHead>
                    <TableHead>Status</TableHead>
                    <TableHead>Priority</TableHead>
                    <TableHead>Due Date</TableHead>
                    <TableHead>Actions</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {filteredTasks.map((task) => (
                    <TableRow key={task.id}>
                      <TableCell>
                        <div>
                          <p className="font-medium">{task.title}</p>
                          <p className="text-sm text-gray-500">{task.description}</p>
                        </div>
                      </TableCell>
                      <TableCell>{task.client}</TableCell>
                      <TableCell>
                        <div className="flex items-center">
                          <User className="h-4 w-4 mr-2 text-gray-400" />
                          {task.assignee}
                        </div>
                      </TableCell>
                      <TableCell>
                        <Select value={task.status} onValueChange={(value) => handleStatusChange(task.id, value as any)}>
                          <SelectTrigger className="w-32">
                            <SelectValue />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="Pending">Pending</SelectItem>
                            <SelectItem value="In Progress">In Progress</SelectItem>
                            <SelectItem value="Completed">Completed</SelectItem>
                          </SelectContent>
                        </Select>
                      </TableCell>
                      <TableCell>
                        <Badge className={getPriorityColor(task.priority)}>
                          {task.priority}
                        </Badge>
                      </TableCell>
                      <TableCell>{task.dueDate}</TableCell>
                      <TableCell>
                        <div className="flex space-x-2">
                          <Button size="sm" variant="outline" onClick={() => setViewingTask(task)}>
                            <Eye className="h-3 w-3" />
                          </Button>
                          <Button size="sm" variant="outline" onClick={() => setEditingTask(task)}>
                            <Edit className="h-3 w-3" />
                          </Button>
                          <Button size="sm" variant="outline" onClick={() => handleDeleteTask(task.id)}>
                            <Trash2 className="h-3 w-3" />
                          </Button>
                        </div>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </CardContent>
          </Card>

          {/* Edit Task Dialog */}
          <Dialog open={!!editingTask} onOpenChange={() => setEditingTask(null)}>
            <DialogContent className="max-w-2xl">
              <DialogHeader>
                <DialogTitle>Edit Task</DialogTitle>
                <DialogDescription>Update task details and status</DialogDescription>
              </DialogHeader>
              {editingTask && (
                <TaskForm 
                  task={editingTask} 
                  setTask={(updatedTask) => setEditingTask({ ...editingTask, ...updatedTask })} 
                  isEdit={true} 
                />
              )}
              <div className="flex justify-end space-x-4 mt-6">
                <Button variant="outline" onClick={() => setEditingTask(null)}>
                  Cancel
                </Button>
                <Button onClick={handleUpdateTask}>Update Task</Button>
              </div>
            </DialogContent>
          </Dialog>

          {/* View Task Dialog */}
          <Dialog open={!!viewingTask} onOpenChange={() => setViewingTask(null)}>
            <DialogContent className="max-w-2xl">
              <DialogHeader>
                <DialogTitle>Task Details</DialogTitle>
                <DialogDescription>Complete task information</DialogDescription>
              </DialogHeader>
              {viewingTask && (
                <div className="space-y-4">
                  <div>
                    <h3 className="font-semibold text-lg">{viewingTask.title}</h3>
                    <p className="text-gray-600">{viewingTask.description}</p>
                  </div>
                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <p className="font-medium">Client:</p>
                      <p className="text-gray-600">{viewingTask.client}</p>
                    </div>
                    <div>
                      <p className="font-medium">Assignee:</p>
                      <p className="text-gray-600">{viewingTask.assignee}</p>
                    </div>
                    <div>
                      <p className="font-medium">Status:</p>
                      <Badge className={getStatusColor(viewingTask.status)}>
                        {viewingTask.status}
                      </Badge>
                    </div>
                    <div>
                      <p className="font-medium">Priority:</p>
                      <Badge className={getPriorityColor(viewingTask.priority)}>
                        {viewingTask.priority}
                      </Badge>
                    </div>
                    <div>
                      <p className="font-medium">Due Date:</p>
                      <p className="text-gray-600">{viewingTask.dueDate}</p>
                    </div>
                    <div>
                      <p className="font-medium">Created:</p>
                      <p className="text-gray-600">{viewingTask.createdDate}</p>
                    </div>
                  </div>
                  {viewingTask.notes && (
                    <div>
                      <p className="font-medium">Notes:</p>
                      <p className="text-gray-600 bg-gray-50 p-3 rounded">{viewingTask.notes}</p>
                    </div>
                  )}
                </div>
              )}
              <div className="flex justify-end mt-6">
                <Button onClick={() => setViewingTask(null)}>Close</Button>
              </div>
            </DialogContent>
          </Dialog>
        </div>
      </main>
    </div>
  );
};

export default TaskManagement;
