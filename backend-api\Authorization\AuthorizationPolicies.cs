using Microsoft.AspNetCore.Authorization;

namespace CAPortalAPI.Authorization
{
    /// <summary>
    /// Authorization policies for role-based access control
    /// </summary>
    public static class AuthorizationPolicies
    {
        // Policy Names
        public const string AdminOnly = "AdminOnly";
        public const string ManagerOrAdmin = "ManagerOrAdmin";
        public const string SeniorStaffOrAbove = "SeniorStaffOrAbove";
        public const string AllStaff = "AllStaff";
        public const string ClientAccess = "ClientAccess";
        public const string DocumentAccess = "DocumentAccess";
        public const string ReportAccess = "ReportAccess";
        public const string UserManagement = "UserManagement";
        public const string ClientManagement = "ClientManagement";
        public const string ComplianceManagement = "ComplianceManagement";
        public const string TaskManagement = "TaskManagement";
        public const string SystemConfiguration = "SystemConfiguration";

        // Role Names
        public const string AdminRole = "Admin";
        public const string ManagerRole = "Manager";
        public const string SeniorAssociateRole = "Senior Associate";
        public const string JuniorAssociateRole = "Junior Associate";
        public const string InternRole = "Intern";
        public const string ClientRole = "Client";

        /// <summary>
        /// Configure authorization policies
        /// </summary>
        /// <param name="options">Authorization options</param>
        public static void ConfigurePolicies(AuthorizationOptions options)
        {
            // Admin Only - Only Admin role
            options.AddPolicy(AdminOnly, policy =>
                policy.RequireRole("Admin"));

            // Manager or Admin - Manager and Admin roles
            options.AddPolicy(ManagerOrAdmin, policy =>
                policy.RequireRole("Admin", "Manager"));

            // Senior Staff or Above - Senior Associate, Manager, Admin
            options.AddPolicy(SeniorStaffOrAbove, policy =>
                policy.RequireRole(AdminRole, ManagerRole, SeniorAssociateRole));

            // All Staff - All internal staff roles
            options.AddPolicy(AllStaff, policy =>
                policy.RequireRole(AdminRole, ManagerRole, SeniorAssociateRole, JuniorAssociateRole, InternRole));

            // Client Access - Clients and staff
            options.AddPolicy(ClientAccess, policy =>
                policy.RequireRole(AdminRole, ManagerRole, SeniorAssociateRole, JuniorAssociateRole, ClientRole));

            // Document Access - Staff who can access documents
            options.AddPolicy(DocumentAccess, policy =>
                policy.RequireRole(AdminRole, ManagerRole, SeniorAssociateRole, JuniorAssociateRole));

            // Report Access - Staff who can generate reports
            options.AddPolicy(ReportAccess, policy =>
                policy.RequireRole(AdminRole, ManagerRole, SeniorAssociateRole));

            // User Management - Who can manage users
            options.AddPolicy(UserManagement, policy =>
                policy.RequireRole(AdminRole, ManagerRole));

            // Client Management - Who can manage clients
            options.AddPolicy(ClientManagement, policy =>
                policy.RequireRole(AdminRole, ManagerRole, SeniorAssociateRole));

            // Compliance Management - Who can manage compliance
            options.AddPolicy(ComplianceManagement, policy =>
                policy.RequireRole(AdminRole, ManagerRole, SeniorAssociateRole));

            // Task Management - Who can manage tasks
            options.AddPolicy(TaskManagement, policy =>
                policy.RequireRole(AdminRole, ManagerRole, SeniorAssociateRole, JuniorAssociateRole));

            // System Configuration - Who can configure system settings
            options.AddPolicy(SystemConfiguration, policy =>
                policy.RequireRole(AdminRole));
        }
    }

    /// <summary>
    /// Custom authorization requirements
    /// </summary>
    public class TenantAccessRequirement : IAuthorizationRequirement
    {
        public string TenantId { get; }

        public TenantAccessRequirement(string tenantId)
        {
            TenantId = tenantId;
        }
    }

    /// <summary>
    /// Handler for tenant access requirement
    /// </summary>
    public class TenantAccessHandler : AuthorizationHandler<TenantAccessRequirement>
    {
        protected override Task HandleRequirementAsync(
            AuthorizationHandlerContext context,
            TenantAccessRequirement requirement)
        {
            var userTenantId = context.User.FindFirst("TenantId")?.Value;

            if (userTenantId == requirement.TenantId)
            {
                context.Succeed(requirement);
            }

            return Task.CompletedTask;
        }
    }

    /// <summary>
    /// Resource-based authorization requirement
    /// </summary>
    public class ResourceOwnerRequirement : IAuthorizationRequirement
    {
    }

    /// <summary>
    /// Handler for resource owner requirement
    /// </summary>
    public class ResourceOwnerHandler : AuthorizationHandler<ResourceOwnerRequirement>
    {
        protected override Task HandleRequirementAsync(
            AuthorizationHandlerContext context,
            ResourceOwnerRequirement requirement)
        {
            var userId = context.User.FindFirst("UserId")?.Value;
            var userRole = context.User.FindFirst("Role")?.Value;

            // Admin and Manager can access all resources
            if (userRole == "Admin" || userRole == "Manager")
            {
                context.Succeed(requirement);
                return Task.CompletedTask;
            }

            // For other roles, check if they own the resource
            // This would need to be implemented based on the specific resource
            // For now, we'll allow access if user is authenticated
            if (!string.IsNullOrEmpty(userId))
            {
                context.Succeed(requirement);
            }

            return Task.CompletedTask;
        }
    }

    /// <summary>
    /// Department-based authorization requirement
    /// </summary>
    public class DepartmentAccessRequirement : IAuthorizationRequirement
    {
        public string[] AllowedDepartments { get; }

        public DepartmentAccessRequirement(params string[] allowedDepartments)
        {
            AllowedDepartments = allowedDepartments;
        }
    }

    /// <summary>
    /// Handler for department access requirement
    /// </summary>
    public class DepartmentAccessHandler : AuthorizationHandler<DepartmentAccessRequirement>
    {
        protected override Task HandleRequirementAsync(
            AuthorizationHandlerContext context,
            DepartmentAccessRequirement requirement)
        {
            var userDepartment = context.User.FindFirst("Department")?.Value;
            var userRole = context.User.FindFirst("Role")?.Value;

            // Admin can access all departments
            if (userRole == "Admin")
            {
                context.Succeed(requirement);
                return Task.CompletedTask;
            }

            // Check if user's department is in allowed departments
            if (!string.IsNullOrEmpty(userDepartment) && 
                requirement.AllowedDepartments.Contains(userDepartment))
            {
                context.Succeed(requirement);
            }

            return Task.CompletedTask;
        }
    }
}
