-- =============================================
-- Sample Data Enhancement for CA_Portal_kumar_associates
-- Adds comprehensive compliance data to existing database
-- =============================================

USE [CA_Portal_kumar_associates];

PRINT '============================================================';
PRINT 'Adding Sample Compliance Data';
PRINT '============================================================';

-- =============================================
-- 1. Enhance ComplianceTypes Table
-- =============================================
PRINT '';
PRINT '1. ENHANCING COMPLIANCE TYPES';
PRINT '------------------------------';

-- Add missing fields to ComplianceTypes if they don't exist
IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID('ComplianceTypes') AND name = 'DefaultDurationDays')
BEGIN
    ALTER TABLE ComplianceTypes ADD DefaultDurationDays INT NULL;
    PRINT '✅ Added DefaultDurationDays to ComplianceTypes';
END

IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID('ComplianceTypes') AND name = 'DefaultPriority')
BEGIN
    ALTER TABLE ComplianceTypes ADD DefaultPriority NVARCHAR(20) NULL;
    PRINT '✅ Added DefaultPriority to ComplianceTypes';
END

IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID('ComplianceTypes') AND name = 'DefaultEstimatedHours')
BEGIN
    ALTER TABLE ComplianceTypes ADD DefaultEstimatedHours DECIMAL(5,2) NULL;
    PRINT '✅ Added DefaultEstimatedHours to ComplianceTypes';
END

IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID('ComplianceTypes') AND name = 'RegulatoryBody')
BEGIN
    ALTER TABLE ComplianceTypes ADD RegulatoryBody NVARCHAR(100) NULL;
    PRINT '✅ Added RegulatoryBody to ComplianceTypes';
END

IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID('ComplianceTypes') AND name = 'Color')
BEGIN
    ALTER TABLE ComplianceTypes ADD Color NVARCHAR(7) NULL;
    PRINT '✅ Added Color to ComplianceTypes';
END

IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID('ComplianceTypes') AND name = 'Icon')
BEGIN
    ALTER TABLE ComplianceTypes ADD Icon NVARCHAR(50) NULL;
    PRINT '✅ Added Icon to ComplianceTypes';
END

-- =============================================
-- 2. Populate Enhanced ComplianceTypes
-- =============================================
PRINT '';
PRINT '2. POPULATING COMPLIANCE TYPES';
PRINT '-------------------------------';

-- Clear existing and add comprehensive compliance types
DELETE FROM ComplianceTypes;

INSERT INTO [ComplianceTypes] (
    [ComplianceTypeId], [Name], [Description], [Category], [RegulatoryBody], 
    [DefaultDurationDays], [DefaultPriority], [DefaultEstimatedHours], [Color], [Icon], [IsActive]
)
VALUES 
    (NEWID(), 'Income Tax Return', 'Annual income tax return filing', 'Tax', 'Income Tax Department', 30, 'High', 8.0, '#FF6B6B', 'receipt-tax', 1),
    (NEWID(), 'GST Return - GSTR1', 'Monthly GST return for outward supplies', 'GST', 'GST Council', 11, 'High', 4.0, '#4ECDC4', 'file-text', 1),
    (NEWID(), 'GST Return - GSTR3B', 'Monthly GST return summary', 'GST', 'GST Council', 20, 'High', 6.0, '#45B7D1', 'file-check', 1),
    (NEWID(), 'TDS Return', 'Tax Deducted at Source quarterly return', 'Tax', 'Income Tax Department', 30, 'Medium', 5.0, '#96CEB4', 'minus-circle', 1),
    (NEWID(), 'ESI Return', 'Employee State Insurance monthly return', 'Labor', 'ESIC', 21, 'Medium', 2.0, '#FFEAA7', 'users', 1),
    (NEWID(), 'PF Return', 'Provident Fund monthly return', 'Labor', 'EPFO', 15, 'Medium', 3.0, '#DDA0DD', 'shield', 1),
    (NEWID(), 'ROC Annual Filing', 'Registrar of Companies annual return', 'Corporate', 'MCA', 60, 'High', 12.0, '#98D8C8', 'building', 1),
    (NEWID(), 'Audit Report', 'Annual financial audit report', 'Audit', 'Various', 90, 'Critical', 40.0, '#F7DC6F', 'search', 1),
    (NEWID(), 'VAT Return', 'Value Added Tax return filing', 'Tax', 'State Tax Department', 20, 'Medium', 4.0, '#BB8FCE', 'percent', 1),
    (NEWID(), 'Professional Tax', 'Professional tax payment and return', 'Tax', 'State Government', 10, 'Low', 1.0, '#85C1E9', 'credit-card', 1),
    (NEWID(), 'Labor License Renewal', 'Annual labor license renewal', 'Labor', 'Labor Department', 30, 'Medium', 6.0, '#F8C471', 'award', 1),
    (NEWID(), 'Environmental Clearance', 'Environmental compliance certificate', 'Environmental', 'Pollution Board', 45, 'High', 15.0, '#82E0AA', 'leaf', 1),
    (NEWID(), 'Fire Safety Certificate', 'Annual fire safety compliance', 'Safety', 'Fire Department', 30, 'High', 8.0, '#EC7063', 'shield-alt', 1),
    (NEWID(), 'Trade License Renewal', 'Municipal trade license renewal', 'Legal', 'Municipal Corporation', 30, 'Medium', 4.0, '#AED6F1', 'store', 1),
    (NEWID(), 'FSSAI License Renewal', 'Food safety license renewal', 'Food Safety', 'FSSAI', 30, 'Medium', 5.0, '#A9DFBF', 'utensils', 1);

PRINT '✅ ComplianceTypes populated with 15 comprehensive types';

-- =============================================
-- 3. Add Comprehensive Compliance Items
-- =============================================
PRINT '';
PRINT '3. ADDING COMPLIANCE ITEMS';
PRINT '---------------------------';

-- Get existing data for reference
DECLARE @ClientIds TABLE (ClientId UNIQUEIDENTIFIER, CompanyName NVARCHAR(200), RowNum INT);
DECLARE @UserIds TABLE (UserId UNIQUEIDENTIFIER, FirstName NVARCHAR(100), RowNum INT);
DECLARE @TypeNames TABLE (Name NVARCHAR(100), Priority NVARCHAR(20), EstimatedHours DECIMAL(5,2), RegulatoryBody NVARCHAR(100), RowNum INT);

-- Get client IDs with row numbers
INSERT INTO @ClientIds 
SELECT ClientId, CompanyName, ROW_NUMBER() OVER (ORDER BY CompanyName)
FROM Clients WHERE IsActive = 1;

-- Get user IDs with row numbers
INSERT INTO @UserIds 
SELECT UserId, FirstName, ROW_NUMBER() OVER (ORDER BY FirstName)
FROM Users WHERE IsActive = 1;

-- Get compliance types with row numbers
INSERT INTO @TypeNames 
SELECT Name, DefaultPriority, DefaultEstimatedHours, RegulatoryBody, ROW_NUMBER() OVER (ORDER BY Name)
FROM ComplianceTypes WHERE IsActive = 1;

-- Add 50+ new compliance items (keeping existing 4)
DECLARE @Counter INT = 1;
DECLARE @MaxItems INT = 50;

WHILE @Counter <= @MaxItems
BEGIN
    DECLARE @ClientId UNIQUEIDENTIFIER;
    DECLARE @UserId UNIQUEIDENTIFIER;
    DECLARE @TypeName NVARCHAR(100);
    DECLARE @Priority NVARCHAR(20);
    DECLARE @EstimatedHours DECIMAL(5,2);
    DECLARE @RegulatoryBody NVARCHAR(100);
    DECLARE @DueDate DATETIME;
    DECLARE @Status NVARCHAR(50);
    
    -- Select random client, user, and type
    SELECT @ClientId = ClientId FROM @ClientIds WHERE RowNum = ((@Counter - 1) % (SELECT COUNT(*) FROM @ClientIds)) + 1;
    SELECT @UserId = UserId FROM @UserIds WHERE RowNum = ((@Counter - 1) % (SELECT COUNT(*) FROM @UserIds)) + 1;
    SELECT @TypeName = Name, @Priority = Priority, @EstimatedHours = EstimatedHours, @RegulatoryBody = RegulatoryBody 
    FROM @TypeNames WHERE RowNum = ((@Counter - 1) % (SELECT COUNT(*) FROM @TypeNames)) + 1;
    
    -- Generate varied due dates
    SET @DueDate = CASE 
        WHEN @Counter % 10 = 1 THEN DATEADD(DAY, 5, GETUTCDATE())
        WHEN @Counter % 10 = 2 THEN DATEADD(DAY, 15, GETUTCDATE())
        WHEN @Counter % 10 = 3 THEN DATEADD(DAY, -3, GETUTCDATE()) -- Overdue
        WHEN @Counter % 10 = 4 THEN DATEADD(DAY, 30, GETUTCDATE())
        WHEN @Counter % 10 = 5 THEN DATEADD(DAY, 7, GETUTCDATE())
        WHEN @Counter % 10 = 6 THEN DATEADD(DAY, -7, GETUTCDATE()) -- Overdue
        WHEN @Counter % 10 = 7 THEN DATEADD(DAY, 45, GETUTCDATE())
        WHEN @Counter % 10 = 8 THEN DATEADD(DAY, 60, GETUTCDATE())
        WHEN @Counter % 10 = 9 THEN DATEADD(DAY, 21, GETUTCDATE())
        ELSE DATEADD(DAY, 35, GETUTCDATE())
    END;
    
    -- Generate varied statuses
    SET @Status = CASE 
        WHEN @Counter % 8 = 1 THEN 'Completed'
        WHEN @Counter % 8 = 2 THEN 'In Progress'
        WHEN @Counter % 8 = 3 THEN 'Pending'
        WHEN @Counter % 8 = 4 THEN 'Pending'
        WHEN @Counter % 8 = 5 THEN 'In Progress'
        WHEN @Counter % 8 = 6 THEN 'Completed'
        WHEN @Counter % 8 = 7 THEN 'Pending'
        ELSE 'In Progress'
    END;
    
    -- Insert compliance item
    INSERT INTO Compliance (
        ComplianceId, ClientId, ComplianceType, SubType, Description, DueDate, Status, Priority,
        AssignedTo, CreatedBy, CreatedAt, EstimatedHours, ComplianceYear, RegulatoryBody, IsRecurring
    )
    VALUES (
        NEWID(), @ClientId, @TypeName, 
        CASE WHEN @TypeName LIKE '%GST%' THEN 'Monthly Filing' 
             WHEN @TypeName LIKE '%Tax%' THEN 'Annual Filing' 
             ELSE 'Standard Compliance' END,
        @TypeName + ' for compliance period ' + CAST(@Counter AS NVARCHAR),
        @DueDate, @Status, ISNULL(@Priority, 'Medium'), @UserId, @UserId, GETUTCDATE(),
        @EstimatedHours, YEAR(GETUTCDATE()), @RegulatoryBody,
        CASE WHEN @TypeName LIKE '%Monthly%' OR @TypeName LIKE '%GST%' THEN 1 ELSE 0 END
    );
    
    SET @Counter = @Counter + 1;
END

PRINT '✅ Added 50 comprehensive compliance items';

-- =============================================
-- 4. Update Overdue Status
-- =============================================
UPDATE Compliance 
SET Status = 'Overdue'
WHERE DueDate < GETUTCDATE() AND Status NOT IN ('Completed', 'Cancelled');

-- Update completed items
UPDATE Compliance 
SET CompletedAt = DATEADD(DAY, -ABS(CHECKSUM(NEWID()) % 30), GETUTCDATE()),
    CompletedBy = CreatedBy,
    ActualHours = EstimatedHours * (0.8 + (ABS(CHECKSUM(NEWID()) % 40) / 100.0))
WHERE Status = 'Completed';

PRINT '✅ Updated compliance statuses and completion data';

-- =============================================
-- 5. Summary Report
-- =============================================
PRINT '';
PRINT '============================================================';
PRINT 'MIGRATION SUMMARY REPORT';
PRINT '============================================================';

SELECT 
    'Total Compliance Items' AS [Metric],
    COUNT(*) AS [Count]
FROM Compliance
UNION ALL
SELECT 'Pending Items', COUNT(*) FROM Compliance WHERE Status = 'Pending'
UNION ALL
SELECT 'In Progress Items', COUNT(*) FROM Compliance WHERE Status = 'In Progress'
UNION ALL
SELECT 'Completed Items', COUNT(*) FROM Compliance WHERE Status = 'Completed'
UNION ALL
SELECT 'Overdue Items', COUNT(*) FROM Compliance WHERE Status = 'Overdue'
UNION ALL
SELECT 'High Priority Items', COUNT(*) FROM Compliance WHERE Priority = 'High'
UNION ALL
SELECT 'Critical Priority Items', COUNT(*) FROM Compliance WHERE Priority = 'Critical'
UNION ALL
SELECT 'Total Clients', COUNT(*) FROM Clients WHERE IsActive = 1
UNION ALL
SELECT 'Total Users', COUNT(*) FROM Users WHERE IsActive = 1
UNION ALL
SELECT 'Total Compliance Types', COUNT(*) FROM ComplianceTypes WHERE IsActive = 1;

PRINT '';
PRINT '✅ Sample data migration completed successfully!';
PRINT 'Database ready for API integration.';
PRINT '============================================================';
