using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using CAPortalAPI.Models.DTOs;
using CAPortalAPI.Models.Common;
using CAPortalAPI.Services;
using Newtonsoft.Json;
using System.ComponentModel.DataAnnotations;

namespace CAPortalAPI.Controllers
{
    [ApiController]
    [Route("api/[controller]")]
    public class AuthController : ControllerBase
    {
        private readonly IDbService _dbService;
        private readonly IJwtService _jwtService;
        private readonly ITenantService _tenantService;
        private readonly ILogger<AuthController> _logger;

        public AuthController(
            IDbService dbService, 
            IJwtService jwtService, 
            ITenantService tenantService,
            ILogger<AuthController> logger)
        {
            _dbService = dbService;
            _jwtService = jwtService;
            _tenantService = tenantService;
            _logger = logger;
        }

        [HttpPost("login")]
        public async Task<IActionResult> Login([FromBody] LoginRequestDto request)
        {
            try
            {
                if (!ModelState.IsValid)
                {
                    return BadRequest(ApiResponse.ErrorResponse("Invalid request data", 
                        ModelState.Values.SelectMany(v => v.Errors.Select(e => e.ErrorMessage)).ToList()));
                }

                // Resolve tenant
                TenantDto? tenant = null;
                if (!string.IsNullOrEmpty(request.TenantKey))
                {
                    tenant = await _tenantService.ResolveTenantByKeyAsync(request.TenantKey);
                }
                else
                {
                    // Try to get tenant from context (resolved by middleware)
                    if (HttpContext.Items.TryGetValue("TenantInfo", out var tenantInfo))
                    {
                        tenant = tenantInfo as TenantDto;
                    }
                }

                if (tenant == null)
                {
                    return BadRequest(ApiResponse.ErrorResponse("Tenant not found or inactive"));
                }

                // Prepare login payload
                var loginPayload = JsonConvert.SerializeObject(new
                {
                    Email = request.Email,
                    Password = request.Password,
                    TenantId = tenant.OrganizationId
                });

                // Get tenant connection string from database
                var tenantConnectionString = await _tenantService.GetTenantConnectionStringAsync(tenant.TenantKey);
                if (string.IsNullOrEmpty(tenantConnectionString))
                {
                    return BadRequest(ApiResponse.ErrorResponse("Tenant database connection not configured"));
                }

                // Execute login stored procedure using dynamic connection string
                var userResult = await _dbService.ExecuteStoredProcedureWithConnectionStringAsync<UserDto>(
                    "sp_AuthenticateUser",
                    loginPayload,
                    tenantConnectionString);

                if (userResult == null)
                {
                    return Unauthorized(ApiResponse.ErrorResponse("Invalid email or password"));
                }

                // Generate JWT token
                var token = _jwtService.GenerateToken(userResult, tenant);
                var refreshToken = _jwtService.GenerateRefreshToken();
                var expiresAt = _jwtService.GetTokenExpiry();

                // Update last login time
                var updateLoginPayload = JsonConvert.SerializeObject(new
                {
                    UserId = userResult.UserId,
                    LastLoginAt = DateTime.UtcNow
                });

                await _dbService.ExecuteStoredProcedureNonQueryWithConnectionStringAsync(
                    "sp_UpdateUserLastLogin",
                    updateLoginPayload,
                    tenantConnectionString);

                var response = new LoginResponseDto
                {
                    Token = token,
                    RefreshToken = refreshToken,
                    ExpiresAt = expiresAt,
                    User = userResult,
                    Tenant = tenant
                };

                _logger.LogInformation("User {Email} logged in successfully for tenant {TenantKey}", 
                    request.Email, tenant.TenantKey);

                return Ok(ApiResponse<LoginResponseDto>.SuccessResponse(response, "Login successful"));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error during login for email {Email}", request.Email);
                return StatusCode(500, ApiResponse.ErrorResponse("An error occurred during login"));
            }
        }

        [HttpPost("refresh-token")]
        public Task<IActionResult> RefreshToken([FromBody] RefreshTokenRequestDto request)
        {
            try
            {
                if (!ModelState.IsValid)
                {
                    return Task.FromResult<IActionResult>(BadRequest(ApiResponse.ErrorResponse("Invalid request data")));
                }

                // Validate refresh token (implement your refresh token validation logic)
                // For now, we'll generate a new token based on the current user context

                var authHeader = Request.Headers["Authorization"].FirstOrDefault();
                if (string.IsNullOrEmpty(authHeader) || !authHeader.StartsWith("Bearer "))
                {
                    return Task.FromResult<IActionResult>(Unauthorized(ApiResponse.ErrorResponse("Invalid authorization header")));
                }

                var token = authHeader.Substring("Bearer ".Length).Trim();
                var userId = _jwtService.GetUserIdFromToken(token);
                var tenantId = _jwtService.GetTenantIdFromToken(token);

                if (userId == null || tenantId == null)
                {
                    return Task.FromResult<IActionResult>(Unauthorized(ApiResponse.ErrorResponse("Invalid token")));
                }

                // Get user and tenant information
                // Implementation depends on your refresh token storage strategy

                return Task.FromResult<IActionResult>(Ok(ApiResponse.SuccessResponse("Token refresh not implemented yet")));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error during token refresh");
                return Task.FromResult<IActionResult>(StatusCode(500, ApiResponse.ErrorResponse("An error occurred during token refresh")));
            }
        }

        [HttpPost("change-password")]
        [Authorize]
        public async Task<IActionResult> ChangePassword([FromBody] ChangePasswordRequestDto request)
        {
            try
            {
                if (!ModelState.IsValid)
                {
                    return BadRequest(ApiResponse.ErrorResponse("Invalid request data",
                        ModelState.Values.SelectMany(v => v.Errors.Select(e => e.ErrorMessage)).ToList()));
                }

                var userId = GetCurrentUserId();
                var tenantKey = GetCurrentTenantKey();

                if (userId == null || string.IsNullOrEmpty(tenantKey))
                {
                    return Unauthorized(ApiResponse.ErrorResponse("Invalid user context"));
                }

                var payload = JsonConvert.SerializeObject(new
                {
                    UserId = userId,
                    CurrentPassword = request.CurrentPassword,
                    NewPassword = request.NewPassword
                });

                var connectionName = $"CA_Portal_{tenantKey.Replace("-", "_")}Connection";
                
                var result = await _dbService.ExecuteStoredProcedureScalarAsync<bool>(
                    "sp_ChangeUserPassword", 
                    payload, 
                    connectionName);

                if (result == true)
                {
                    _logger.LogInformation("Password changed successfully for user {UserId}", userId);
                    return Ok(ApiResponse.SuccessResponse("Password changed successfully"));
                }
                else
                {
                    return BadRequest(ApiResponse.ErrorResponse("Current password is incorrect"));
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error changing password for user {UserId}", GetCurrentUserId());
                return StatusCode(500, ApiResponse.ErrorResponse("An error occurred while changing password"));
            }
        }

        [HttpPost("forgot-password")]
        public Task<IActionResult> ForgotPassword([FromBody] ForgotPasswordRequestDto request)
        {
            try
            {
                if (!ModelState.IsValid)
                {
                    return Task.FromResult<IActionResult>(BadRequest(ApiResponse.ErrorResponse("Invalid request data")));
                }

                // Implement forgot password logic
                // This would typically involve sending an email with a reset token

                return Task.FromResult<IActionResult>(Ok(ApiResponse.SuccessResponse("Password reset instructions sent to your email")));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error during forgot password for email {Email}", request.Email);
                return Task.FromResult<IActionResult>(StatusCode(500, ApiResponse.ErrorResponse("An error occurred during password reset")));
            }
        }

        [HttpPost("reset-password")]
        public Task<IActionResult> ResetPassword([FromBody] ResetPasswordRequestDto request)
        {
            try
            {
                if (!ModelState.IsValid)
                {
                    return Task.FromResult<IActionResult>(BadRequest(ApiResponse.ErrorResponse("Invalid request data",
                        ModelState.Values.SelectMany(v => v.Errors.Select(e => e.ErrorMessage)).ToList())));
                }

                // Implement password reset logic
                // This would validate the reset token and update the password

                return Task.FromResult<IActionResult>(Ok(ApiResponse.SuccessResponse("Password reset successfully")));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error during password reset for email {Email}", request.Email);
                return Task.FromResult<IActionResult>(StatusCode(500, ApiResponse.ErrorResponse("An error occurred during password reset")));
            }
        }

        [HttpPost("logout")]
        [Authorize]
        public Task<IActionResult> Logout()
        {
            try
            {
                var userId = GetCurrentUserId();
                _logger.LogInformation("User {UserId} logged out", userId);

                // Implement logout logic (e.g., invalidate refresh tokens)

                return Task.FromResult<IActionResult>(Ok(ApiResponse.SuccessResponse("Logged out successfully")));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error during logout");
                return Task.FromResult<IActionResult>(StatusCode(500, ApiResponse.ErrorResponse("An error occurred during logout")));
            }
        }

        [HttpGet("me")]
        [Authorize]
        public async Task<IActionResult> GetCurrentUser()
        {
            try
            {
                var userId = GetCurrentUserId();
                var tenantKey = GetCurrentTenantKey();

                if (userId == null || string.IsNullOrEmpty(tenantKey))
                {
                    return Unauthorized(ApiResponse.ErrorResponse("Invalid user context"));
                }

                var payload = JsonConvert.SerializeObject(new { UserId = userId });
                var connectionName = $"CA_Portal_{tenantKey.Replace("-", "_")}Connection";

                var user = await _dbService.ExecuteStoredProcedureAsync<UserDto>(
                    "sp_GetUserById", 
                    payload, 
                    connectionName);

                if (user == null)
                {
                    return NotFound(ApiResponse.ErrorResponse("User not found"));
                }

                return Ok(ApiResponse<UserDto>.SuccessResponse(user));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting current user");
                return StatusCode(500, ApiResponse.ErrorResponse("An error occurred while getting user information"));
            }
        }

        private Guid? GetCurrentUserId()
        {
            var userIdClaim = User.FindFirst(System.Security.Claims.ClaimTypes.NameIdentifier)?.Value;
            return Guid.TryParse(userIdClaim, out var userId) ? userId : null;
        }

        private string? GetCurrentTenantKey()
        {
            return User.FindFirst("TenantKey")?.Value;
        }
    }
}
