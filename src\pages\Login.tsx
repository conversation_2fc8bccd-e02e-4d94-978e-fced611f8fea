import { useState } from "react";
import { <PERSON>, Eye, EyeOff, Info } from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { useToast } from "@/hooks/use-toast";
import { useNavigate } from "react-router-dom";
import { useAuth } from "@/contexts/AuthContext";
import { authService } from "@/services/authService";
import { handleApiError } from "@/services/api";
import { env } from "@/config/env";

// Demo user credentials for testing
const demoUsers = [
  {
    email: "<EMAIL>",
    password: "password123",
    role: "Admin",
    name: "<PERSON>"
  },
  {
    email: "<EMAIL>",
    password: "password123",
    role: "Manager",
    name: "<PERSON><PERSON>"
  },
  {
    email: "<EMAIL>",
    password: "password123",
    role: "Junior Associate",
    name: "<PERSON><PERSON>"
  }
];

const Login = () => {
  const [email, setEmail] = useState("");
  const [password, setPassword] = useState("");
  const [showPassword, setShowPassword] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState("");
  const { toast } = useToast();
  const navigate = useNavigate();
  const { login } = useAuth();

  const handleLogin = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsLoading(true);
    setError("");

    try {
      // Get tenant key from environment or use default
      const tenantKey = env.TENANT_KEY;

      // Call API login
      const loginResponse = await authService.login({
        email,
        password,
        tenantKey
      });

      // Use auth context to login
      login({
        email: loginResponse.user.email,
        role: loginResponse.user.role,
        name: `${loginResponse.user.firstName} ${loginResponse.user.lastName}`,
        userId: loginResponse.user.userId,
        tenant: loginResponse.tenant
      });

      toast({
        title: "Login Successful",
        description: `Welcome back, ${loginResponse.user.firstName}!`,
      });

      // Redirect to dashboard
      navigate("/");
    } catch (error: any) {
      const errorMessage = handleApiError(error);
      setError(errorMessage);

      toast({
        title: "Login Failed",
        description: errorMessage,
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  const fillDemoCredentials = (userType: 'admin' | 'staff') => {
    if (userType === 'admin') {
      setEmail("<EMAIL>");
      setPassword("password123");
    } else {
      setEmail("<EMAIL>");
      setPassword("password123");
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center p-4">
      <div className="max-w-md w-full space-y-8">
        <div className="bg-white rounded-lg shadow-xl p-8">
          {/* Logo and Title */}
          <div className="text-center mb-8">
            <div className="mx-auto w-16 h-16 bg-blue-600 rounded-lg flex items-center justify-center mb-4">
              <Shield className="w-8 h-8 text-white" />
            </div>
            <h2 className="text-3xl font-bold text-gray-900">ComplianceDesk Pro</h2>
            <p className="text-gray-600 mt-2">CA Firm Portal Login</p>
          </div>

          {/* Demo Credentials Info */}
          <div className="mb-6 p-4 bg-blue-50 rounded-lg border border-blue-200">
            <div className="flex items-start">
              <Info className="w-5 h-5 text-blue-600 mt-0.5 mr-2" />
              <div>
                <h4 className="text-sm font-medium text-blue-800 mb-2">Demo Credentials</h4>
                <div className="space-y-2 text-xs text-blue-700">
                  <div>
                    <strong>Admin:</strong> <EMAIL> / password123
                    <Button
                      size="sm"
                      variant="outline"
                      className="ml-2 h-6 text-xs"
                      onClick={() => fillDemoCredentials('admin')}
                    >
                      Use
                    </Button>
                  </div>
                  <div>
                    <strong>Manager:</strong> <EMAIL> / password123
                    <Button
                      size="sm"
                      variant="outline"
                      className="ml-2 h-6 text-xs"
                      onClick={() => fillDemoCredentials('staff')}
                    >
                      Use
                    </Button>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Error Display */}
          {error && (
            <div className="mb-4 p-3 bg-red-50 border border-red-200 rounded-md">
              <p className="text-sm text-red-600">{error}</p>
            </div>
          )}

          {/* Login Form */}
          <form onSubmit={handleLogin} className="space-y-6">
            <div>
              <label htmlFor="email" className="block text-sm font-medium text-gray-700 mb-2">
                Email Address
              </label>
              <Input
                id="email"
                type="email"
                value={email}
                onChange={(e) => setEmail(e.target.value)}
                placeholder="Enter your email"
                className="w-full"
                required
              />
            </div>

            <div>
              <label htmlFor="password" className="block text-sm font-medium text-gray-700 mb-2">
                Password
              </label>
              <div className="relative">
                <Input
                  id="password"
                  type={showPassword ? "text" : "password"}
                  value={password}
                  onChange={(e) => setPassword(e.target.value)}
                  placeholder="Enter your password"
                  className="w-full pr-10"
                  required
                />
                <button
                  type="button"
                  onClick={() => setShowPassword(!showPassword)}
                  className="absolute inset-y-0 right-0 pr-3 flex items-center"
                >
                  {showPassword ? (
                    <EyeOff className="h-4 w-4 text-gray-400" />
                  ) : (
                    <Eye className="h-4 w-4 text-gray-400" />
                  )}
                </button>
              </div>
            </div>

            <Button
              type="submit"
              className="w-full bg-blue-600 hover:bg-blue-700"
              disabled={isLoading}
            >
              {isLoading ? "Signing in..." : "Sign In"}
            </Button>
          </form>

          {/* Footer */}
          <div className="mt-6 text-center">
            <p className="text-xs text-gray-500">
              Access is restricted to authorized CA firm staff only.
              <br />
              Contact your administrator for access.
            </p>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Login;
