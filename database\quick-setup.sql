-- =============================================
-- Quick Compliance Setup for Testing
-- =============================================

-- Create Compliance table if it doesn't exist
IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='Compliance' AND xtype='U')
BEGIN
    CREATE TABLE [dbo].[Compliance] (
        [ComplianceId] UNIQUEIDENTIFIER PRIMARY KEY DEFAULT NEWID(),
        [ClientId] UNIQUEIDENTIFIER NOT NULL,
        [ComplianceType] NVARCHAR(100) NOT NULL,
        [SubType] NVARCHAR(100) NULL,
        [Description] NVARCHAR(MAX) NULL,
        [DueDate] DATETIME NOT NULL,
        [Status] NVARCHAR(50) NOT NULL DEFAULT 'Pending',
        [Priority] NVARCHAR(20) NOT NULL DEFAULT 'Medium',
        [AssignedTo] UNIQUEIDENTIFIER NULL,
        [CreatedAt] DATETIME NOT NULL DEFAULT GETUTCDATE(),
        [CreatedBy] UNIQUEIDENTIFIER NULL,
        [UpdatedAt] DATETIME NULL,
        [UpdatedBy] UNIQUEIDENTIFIER NULL,
        [CompletedAt] DATETIME NULL,
        [CompletedBy] UNIQUEIDENTIFIER NULL,
        [Notes] NVARCHAR(MAX) NULL,
        [ReminderSent] BIT NOT NULL DEFAULT 0,
        [LastReminderDate] DATETIME NULL,
        [IsActive] BIT NOT NULL DEFAULT 1
    );
    
    PRINT 'Compliance table created successfully.';
END
ELSE
BEGIN
    PRINT 'Compliance table already exists.';
END

-- Create basic indexes
IF NOT EXISTS (SELECT * FROM sys.indexes WHERE name = 'IX_Compliance_ClientId')
BEGIN
    CREATE INDEX [IX_Compliance_ClientId] ON [Compliance]([ClientId]);
END

IF NOT EXISTS (SELECT * FROM sys.indexes WHERE name = 'IX_Compliance_DueDate')
BEGIN
    CREATE INDEX [IX_Compliance_DueDate] ON [Compliance]([DueDate]);
END

IF NOT EXISTS (SELECT * FROM sys.indexes WHERE name = 'IX_Compliance_Status')
BEGIN
    CREATE INDEX [IX_Compliance_Status] ON [Compliance]([Status]);
END

-- Insert some test data
DECLARE @TestClientId UNIQUEIDENTIFIER = '123e4567-e89b-12d3-a456-426614174000';
DECLARE @TestUserId UNIQUEIDENTIFIER = '123e4567-e89b-12d3-a456-426614174001';

-- Only insert if table is empty
IF NOT EXISTS (SELECT 1 FROM [Compliance])
BEGIN
    INSERT INTO [Compliance] (
        [ClientId], [ComplianceType], [SubType], [Description], [DueDate], 
        [Status], [Priority], [AssignedTo], [CreatedBy], [Notes]
    )
    VALUES 
        (@TestClientId, 'Tax Filing', 'Annual Return', 'Annual income tax return filing', 
         DATEADD(DAY, 30, GETUTCDATE()), 'Pending', 'High', @TestUserId, @TestUserId, 
         'Test compliance item'),
        
        (@TestClientId, 'GST Return', 'GSTR-1', 'Monthly GST return filing', 
         DATEADD(DAY, 15, GETUTCDATE()), 'In Progress', 'High', @TestUserId, @TestUserId, 
         'In progress'),
        
        (@TestClientId, 'Audit', 'Internal Audit', 'Quarterly audit review', 
         DATEADD(DAY, 45, GETUTCDATE()), 'Pending', 'Medium', @TestUserId, @TestUserId, 
         'Scheduled for next month'),
        
        (@TestClientId, 'ROC Filing', 'Annual Filing', 'Annual return filing', 
         DATEADD(DAY, -5, GETUTCDATE()), 'Overdue', 'High', @TestUserId, @TestUserId, 
         'Urgent filing required'),
        
        (@TestClientId, 'TDS Return', 'Quarterly Return', 'TDS return filing', 
         DATEADD(DAY, 20, GETUTCDATE()), 'Pending', 'Medium', @TestUserId, @TestUserId, 
         'Awaiting documents');
    
    PRINT 'Test data inserted successfully.';
END

PRINT 'Quick setup completed! You can now test the Compliance API.';

-- Show what was created
SELECT 
    'Compliance Items Created' AS [Result],
    COUNT(*) AS [Count]
FROM [Compliance];

SELECT TOP 3
    ComplianceType,
    Status,
    Priority,
    DueDate
FROM [Compliance]
ORDER BY DueDate;
