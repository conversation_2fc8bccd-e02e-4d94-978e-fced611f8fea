-- Test Create Compliance Item Stored Procedure
USE [CA_Portal_kumar_associates];

PRINT '🧪 Testing sp_CreateComplianceItem stored procedure...';

DECLARE @TestPayload NVARCHAR(MAX) = '{
  "clientId": "EA7BE4A7-F542-41E3-BAA3-0005B145761E",
  "complianceType": "Direct SP Test",
  "subType": "Database Test",
  "description": "Testing stored procedure directly from SQL",
  "dueDate": "2024-12-31T23:59:59Z",
  "priority": "High",
  "notes": "Created via direct SQL test"
}';

PRINT 'Payload:';
PRINT @TestPayload;

PRINT '';
PRINT 'Executing stored procedure...';

EXEC sp_CreateComplianceItem @TestPayload;

PRINT '';
PRINT '✅ Stored procedure executed successfully!';

PRINT '';
PRINT '📊 Updated compliance count:';
SELECT COUNT(*) AS TotalComplianceItems FROM Compliance;

PRINT '';
PRINT '🔍 Latest compliance items:';
SELECT TOP 3 
    ComplianceType, 
    Description, 
    Status, 
    Priority, 
    CreatedAt 
FROM Compliance 
ORDER BY CreatedAt DESC;
