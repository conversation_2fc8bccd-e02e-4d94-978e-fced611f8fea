{"Logging": {"LogLevel": {"Default": "Information", "Microsoft.AspNetCore": "Warning"}}, "ConnectionStrings": {"TenantRegistryConnection": "Server=localhost;Database=TenantRegistry;Integrated Security=true;TrustServerCertificate=true;"}, "JwtSettings": {"Key": "super_secret_key_for_ca_portal_application_2024_minimum_32_characters", "Issuer": "CAPortalAPI", "Audience": "CAPortalUsers", "ExpiryInMinutes": 1440}, "ApiSettings": {"DefaultPageSize": 20, "MaxPageSize": 100, "EnableSwagger": true}}