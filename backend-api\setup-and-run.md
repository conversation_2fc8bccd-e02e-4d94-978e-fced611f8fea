# 🚀 CA Portal API - Complete Setup and Run Guide

## 📋 Prerequisites Checklist

### 1. Install .NET 6.0 SDK
- **Download**: https://dotnet.microsoft.com/download/dotnet/6.0
- **Verify Installation**: Open Command Prompt/PowerShell and run:
  ```bash
  dotnet --version
  ```
  Should show version 6.0.x or later

### 2. Install SQL Server
Choose one of these options:

**Option A: SQL Server Express (Recommended for development)**
- Download: https://www.microsoft.com/en-us/sql-server/sql-server-downloads
- Select "Express" edition (free)

**Option B: SQL Server LocalDB (Lightweight)**
- Comes with Visual Studio
- Or download separately: https://docs.microsoft.com/en-us/sql/database-engine/configure-windows/sql-server-express-localdb

**Option C: SQL Server Developer Edition (Full features)**
- Download: https://www.microsoft.com/en-us/sql-server/sql-server-downloads
- Select "Developer" edition (free)

### 3. Install SQL Server Management Studio (SSMS)
- Download: https://docs.microsoft.com/en-us/sql/ssms/download-sql-server-management-studio-ssms
- Used to run database scripts and manage databases

### 4. Install Visual Studio or VS Code (Optional but recommended)
**Option A: Visual Studio 2022 Community (Full IDE)**
- Download: https://visualstudio.microsoft.com/vs/community/
- Select "ASP.NET and web development" workload during installation

**Option B: VS Code (Lightweight)**
- Download: https://code.visualstudio.com/
- Install C# extension

## 🗄️ Database Setup

### Step 1: Create Master Database
1. Open SQL Server Management Studio (SSMS)
2. Connect to your SQL Server instance
3. Navigate to your project's `database` folder
4. Run the following scripts in order:

```sql
-- 1. Create master database and tables
-- File: database/master-database-schema.sql
-- This creates the TenantRegistry database
```

```sql
-- 2. Insert sample tenant data
-- File: database/dummy-data-insert.sql
-- This adds sample organizations/tenants
```

### Step 2: Create Tenant Database
```sql
-- 3. Create tenant database schema
-- File: database/tenant-database-template.sql
-- This creates the CA_Portal_kumar_associates database
```

```sql
-- 4. Insert sample tenant data
-- File: database/tenant-sample-data.sql
-- This adds sample users, clients, compliance items, etc.
```

### Step 3: Verify Database Setup
Run this query to confirm databases exist:
```sql
SELECT name FROM sys.databases 
WHERE name IN ('TenantRegistry', 'CA_Portal_kumar_associates');
```

## ⚙️ Project Configuration

### Step 1: Update Connection Strings
Edit `appsettings.json` and `appsettings.Development.json`:

**For SQL Server Express:**
```json
{
  "ConnectionStrings": {
    "TenantRegistryConnection": "Server=localhost\\SQLEXPRESS;Database=TenantRegistry;Integrated Security=true;TrustServerCertificate=true;",
    "CA_Portal_kumar_associatesConnection": "Server=localhost\\SQLEXPRESS;Database=CA_Portal_kumar_associates;Integrated Security=true;TrustServerCertificate=true;"
  }
}
```

**For SQL Server LocalDB:**
```json
{
  "ConnectionStrings": {
    "TenantRegistryConnection": "Server=(localdb)\\MSSQLLocalDB;Database=TenantRegistry;Integrated Security=true;TrustServerCertificate=true;",
    "CA_Portal_kumar_associatesConnection": "Server=(localdb)\\MSSQLLocalDB;Database=CA_Portal_kumar_associates;Integrated Security=true;TrustServerCertificate=true;"
  }
}
```

**For SQL Server with Username/Password:**
```json
{
  "ConnectionStrings": {
    "TenantRegistryConnection": "Server=localhost;Database=TenantRegistry;User Id=sa;Password=YourPassword;TrustServerCertificate=true;",
    "CA_Portal_kumar_associatesConnection": "Server=localhost;Database=CA_Portal_kumar_associates;User Id=sa;Password=YourPassword;TrustServerCertificate=true;"
  }
}
```

## 🏃‍♂️ Running the Project

### Method 1: Command Line (Recommended)

1. **Open Command Prompt or PowerShell**
2. **Navigate to the project directory:**
   ```bash
   cd "d:\DataOps Sync\Projects\CA-Portal-main\backend-api"
   ```

3. **Restore NuGet packages:**
   ```bash
   dotnet restore
   ```

4. **Build the project:**
   ```bash
   dotnet build
   ```

5. **Run the project:**
   ```bash
   dotnet run
   ```

### Method 2: Visual Studio

1. **Open Visual Studio 2022**
2. **Open Project:**
   - File → Open → Project/Solution
   - Navigate to `backend-api` folder
   - Select `CAPortalAPI.csproj`

3. **Build and Run:**
   - Press `F5` or click "Start Debugging"
   - Or press `Ctrl+F5` for "Start Without Debugging"

### Method 3: VS Code

1. **Open VS Code**
2. **Open Folder:**
   - File → Open Folder
   - Select the `backend-api` folder

3. **Run:**
   - Press `F5` to start debugging
   - Or open terminal (`Ctrl+``) and run: `dotnet run`

## 🌐 Accessing the Application

Once the application starts, you'll see output like:
```
info: Microsoft.Hosting.Lifetime[14]
      Now listening on: https://localhost:7000
      Now listening on: http://localhost:5000
info: Microsoft.Hosting.Lifetime[0]
      Application started. Press Ctrl+C to shut down.
```

### Access Points:
- **Swagger UI**: https://localhost:7000
- **API Base URL**: https://localhost:7000/api
- **Health Check**: https://localhost:7000/health (if implemented)

## 🧪 Testing the API

### 1. Using Swagger UI
1. Navigate to https://localhost:7000
2. You'll see the interactive API documentation
3. Test the login endpoint first
4. Use the "Authorize" button to add JWT tokens for protected endpoints

### 2. Test Login Endpoint

**Sample Login Request:**
```json
POST https://localhost:7000/api/auth/login
Content-Type: application/json

{
  "email": "<EMAIL>",
  "password": "password123",
  "tenantKey": "kumar-associates"
}
```

**Expected Response:**
```json
{
  "success": true,
  "message": "Login successful",
  "data": {
    "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
    "refreshToken": "refresh_token_here",
    "expiresAt": "2024-01-01T12:00:00Z",
    "user": {
      "userId": "guid-here",
      "email": "<EMAIL>",
      "firstName": "Admin",
      "lastName": "User",
      "role": "Admin"
    },
    "tenant": {
      "tenantKey": "kumar-associates",
      "organizationName": "Kumar & Associates"
    }
  }
}
```

### 3. Test Protected Endpoints

Use the JWT token from login response:
```json
GET https://localhost:7000/api/users
Authorization: Bearer YOUR_JWT_TOKEN_HERE
X-Tenant-ID: kumar-associates
```

## 🔧 Troubleshooting

### Common Issues and Solutions:

#### 1. **Port Already in Use**
```bash
# Error: Unable to bind to https://localhost:7000
# Solution: Change port
dotnet run --urls "https://localhost:7001;http://localhost:5001"
```

#### 2. **Database Connection Failed**
- Verify SQL Server is running
- Check connection strings in appsettings.json
- Test connection in SSMS
- Ensure databases exist

#### 3. **SSL Certificate Issues**
```bash
# Trust the development certificate
dotnet dev-certs https --trust
```

#### 4. **Missing Packages**
```bash
# Clear cache and restore
dotnet nuget locals all --clear
dotnet restore
```

#### 5. **Build Errors**
```bash
# Clean and rebuild
dotnet clean
dotnet build
```

### Verify Setup:

#### Check .NET Installation:
```bash
dotnet --info
```

#### Check SQL Server Connection:
```bash
# Test connection string in SSMS or run this query:
SELECT @@VERSION;
```

#### Check Project Dependencies:
```bash
dotnet list package
```

## 📊 Expected Behavior

When everything is set up correctly:

1. **Application starts without errors**
2. **Swagger UI loads** at https://localhost:7000
3. **Login endpoint works** with sample credentials
4. **JWT tokens are generated** successfully
5. **Protected endpoints require authentication**
6. **Multi-tenant resolution works** via headers or subdomains
7. **Database operations succeed** through stored procedures

## 🎯 Next Steps

1. **Create Required Stored Procedures** (see main README.md)
2. **Test All Endpoints** using Swagger UI
3. **Integrate with React Frontend**
4. **Add More Tenants** as needed
5. **Deploy to Production** environment

## 📞 Support

If you encounter issues:
1. Check the console output for error messages
2. Verify all prerequisites are installed
3. Ensure databases are set up correctly
4. Test connection strings independently
5. Check firewall/antivirus settings

The API should now be running successfully and ready for integration with your React frontend!
