-- =====================================================
-- TenantRegistry Database - Required Stored Procedures
-- =====================================================
-- Run these stored procedures in your TenantRegistry database

USE TenantRegistry;
GO

-- =====================================================
-- sp_GetTenantByDomain - Get tenant by domain
-- =====================================================
CREATE OR ALTER PROCEDURE sp_GetTenantByDomain
    @JsonPayload NVARCHAR(MAX)
AS
BEGIN
    SET NOCOUNT ON;
    
    DECLARE @Domain NVARCHAR(255);
    
    -- Parse JSON input
    SELECT @Domain = JSON_VALUE(@JsonPayload, '$.Domain');
    
    -- Get tenant by primary domain
    SELECT
        OrganizationId,
        TenantKey,
        OrganizationName,
        Domain,
        DatabaseName,
        ConnectionString,
        IsActive,
        CreatedAt,
        UpdatedAt,
        SubscriptionTier,
        MaxUsers,
        StorageLimit
    FROM Organizations
    WHERE Domain = @Domain AND IsActive = 1

    UNION

    -- Also check additional domains
    SELECT
        o.OrganizationId,
        o.TenantKey,
        o.OrganizationName,
        o.Domain,
        o.DatabaseName,
        o.ConnectionString,
        o.IsActive,
        o.CreatedAt,
        o.UpdatedAt,
        o.SubscriptionTier,
        o.MaxUsers,
        o.StorageLimit
    FROM Organizations o
    INNER JOIN OrganizationDomains od ON o.OrganizationId = od.OrganizationId
    WHERE od.Domain = @Domain AND od.IsVerified = 1 AND o.IsActive = 1
    FOR JSON PATH, WITHOUT_ARRAY_WRAPPER;
END
GO

-- =====================================================
-- sp_GetTenantByKey - Get tenant by tenant key
-- =====================================================
CREATE OR ALTER PROCEDURE sp_GetTenantByKey
    @JsonPayload NVARCHAR(MAX)
AS
BEGIN
    SET NOCOUNT ON;
    
    DECLARE @TenantKey NVARCHAR(50);
    
    -- Parse JSON input
    SELECT @TenantKey = JSON_VALUE(@JsonPayload, '$.TenantKey');
    
    -- Get tenant by tenant key
    SELECT
        OrganizationId,
        TenantKey,
        OrganizationName,
        Domain,
        DatabaseName,
        ConnectionString,
        IsActive,
        CreatedAt,
        UpdatedAt,
        SubscriptionTier,
        MaxUsers,
        StorageLimit
    FROM Organizations
    WHERE TenantKey = @TenantKey AND IsActive = 1
    FOR JSON PATH, WITHOUT_ARRAY_WRAPPER;
END
GO

-- =====================================================
-- sp_GetTenantWithConnectionString - Get tenant with connection string
-- =====================================================
CREATE OR ALTER PROCEDURE sp_GetTenantWithConnectionString
    @JsonPayload NVARCHAR(MAX)
AS
BEGIN
    SET NOCOUNT ON;
    
    DECLARE @TenantKey NVARCHAR(50);
    
    -- Parse JSON input
    SELECT @TenantKey = JSON_VALUE(@JsonPayload, '$.TenantKey');
    
    -- Get tenant with connection string
    SELECT
        OrganizationId,
        TenantKey,
        OrganizationName,
        Domain,
        DatabaseName,
        ConnectionString,
        IsActive,
        CreatedAt,
        UpdatedAt,
        SubscriptionTier,
        MaxUsers,
        StorageLimit
    FROM Organizations
    WHERE TenantKey = @TenantKey AND IsActive = 1
    FOR JSON PATH, WITHOUT_ARRAY_WRAPPER;
END
GO

-- =====================================================
-- sp_GetActiveTenants - Get all active tenants
-- =====================================================
CREATE OR ALTER PROCEDURE sp_GetActiveTenants
    @JsonPayload NVARCHAR(MAX)
AS
BEGIN
    SET NOCOUNT ON;
    
    -- Get all active tenants
    SELECT
        OrganizationId,
        TenantKey,
        OrganizationName,
        Domain,
        DatabaseName,
        ConnectionString,
        IsActive,
        CreatedAt,
        UpdatedAt,
        SubscriptionTier,
        MaxUsers,
        StorageLimit
    FROM Organizations
    WHERE IsActive = 1
    ORDER BY OrganizationName
    FOR JSON PATH;
END
GO

-- =====================================================
-- sp_CreateTenant - Create new tenant
-- =====================================================
CREATE OR ALTER PROCEDURE sp_CreateTenant
    @JsonPayload NVARCHAR(MAX)
AS
BEGIN
    SET NOCOUNT ON;
    BEGIN TRY
        BEGIN TRANSACTION;
        
        DECLARE @TenantKey NVARCHAR(50);
        DECLARE @OrganizationName NVARCHAR(255);
        DECLARE @Domain NVARCHAR(255);
        DECLARE @SubscriptionTier NVARCHAR(50);
        DECLARE @MaxUsers INT;
        DECLARE @StorageLimit BIGINT;
        DECLARE @ContactEmail NVARCHAR(255);
        DECLARE @ContactPhone NVARCHAR(20);
        DECLARE @ContactPerson NVARCHAR(255);
        DECLARE @BillingAddress NVARCHAR(500);
        DECLARE @BillingCity NVARCHAR(100);
        DECLARE @BillingState NVARCHAR(100);
        DECLARE @BillingPinCode NVARCHAR(10);
        
        DECLARE @NewOrgId UNIQUEIDENTIFIER = NEWID();
        DECLARE @DatabaseName NVARCHAR(128);
        DECLARE @ConnectionString NVARCHAR(1000);
        
        -- Parse JSON input
        SELECT 
            @TenantKey = JSON_VALUE(@JsonPayload, '$.TenantKey'),
            @OrganizationName = JSON_VALUE(@JsonPayload, '$.OrganizationName'),
            @Domain = JSON_VALUE(@JsonPayload, '$.Domain'),
            @SubscriptionTier = ISNULL(JSON_VALUE(@JsonPayload, '$.SubscriptionTier'), 'Basic'),
            @MaxUsers = ISNULL(CAST(JSON_VALUE(@JsonPayload, '$.MaxUsers') AS INT), 10),
            @StorageLimit = ISNULL(CAST(JSON_VALUE(@JsonPayload, '$.StorageLimit') AS BIGINT), 5368709120),
            @ContactEmail = JSON_VALUE(@JsonPayload, '$.ContactEmail'),
            @ContactPhone = JSON_VALUE(@JsonPayload, '$.ContactPhone'),
            @ContactPerson = JSON_VALUE(@JsonPayload, '$.ContactPerson'),
            @BillingAddress = JSON_VALUE(@JsonPayload, '$.BillingAddress'),
            @BillingCity = JSON_VALUE(@JsonPayload, '$.BillingCity'),
            @BillingState = JSON_VALUE(@JsonPayload, '$.BillingState'),
            @BillingPinCode = JSON_VALUE(@JsonPayload, '$.BillingPinCode');
        
        -- Generate database name and connection string
        SET @DatabaseName = 'CA_Portal_' + REPLACE(@TenantKey, '-', '_');
        SET @ConnectionString = 'Server=localhost;Database=' + @DatabaseName + ';Integrated Security=true;TrustServerCertificate=true;';
        
        -- Insert new organization
        INSERT INTO Organizations (
            OrganizationId, TenantKey, OrganizationName, Domain, DatabaseName, ConnectionString,
            IsActive, CreatedAt, UpdatedAt, SubscriptionTier, MaxUsers, StorageLimit,
            ContactEmail, ContactPhone, ContactPerson, BillingAddress, BillingCity, 
            BillingState, BillingPinCode, Status
        )
        VALUES (
            @NewOrgId, @TenantKey, @OrganizationName, @Domain, @DatabaseName, @ConnectionString,
            1, GETUTCDATE(), GETUTCDATE(), @SubscriptionTier, @MaxUsers, @StorageLimit,
            @ContactEmail, @ContactPhone, @ContactPerson, @BillingAddress, @BillingCity,
            @BillingState, @BillingPinCode, 'Active'
        );
        
        COMMIT TRANSACTION;
        
        -- Return created tenant
        SELECT 
            OrganizationId,
            TenantKey,
            OrganizationName,
            Domain,
            DatabaseName,
            ConnectionString,
            IsActive,
            CreatedAt,
            UpdatedAt,
            SubscriptionTier,
            MaxUsers,
            StorageLimit,
            ContactEmail,
            ContactPhone,
            ContactPerson,
            Status
        FROM Organizations 
        WHERE OrganizationId = @NewOrgId
        FOR JSON PATH, WITHOUT_ARRAY_WRAPPER;
        
    END TRY
    BEGIN CATCH
        IF @@TRANCOUNT > 0
            ROLLBACK TRANSACTION;
        THROW;
    END CATCH
END
GO

-- =====================================================
-- sp_UpdateTenant - Update tenant information
-- =====================================================
CREATE OR ALTER PROCEDURE sp_UpdateTenant
    @JsonPayload NVARCHAR(MAX)
AS
BEGIN
    SET NOCOUNT ON;
    BEGIN TRY
        DECLARE @OrganizationId UNIQUEIDENTIFIER;
        DECLARE @OrganizationName NVARCHAR(255);
        DECLARE @SubscriptionTier NVARCHAR(50);
        DECLARE @MaxUsers INT;
        DECLARE @StorageLimit BIGINT;
        DECLARE @IsActive BIT;
        DECLARE @ContactEmail NVARCHAR(255);
        DECLARE @ContactPhone NVARCHAR(20);
        DECLARE @ContactPerson NVARCHAR(255);
        DECLARE @Status NVARCHAR(50);
        
        -- Parse JSON input
        SELECT 
            @OrganizationId = JSON_VALUE(@JsonPayload, '$.OrganizationId'),
            @OrganizationName = JSON_VALUE(@JsonPayload, '$.OrganizationName'),
            @SubscriptionTier = JSON_VALUE(@JsonPayload, '$.SubscriptionTier'),
            @MaxUsers = CAST(JSON_VALUE(@JsonPayload, '$.MaxUsers') AS INT),
            @StorageLimit = CAST(JSON_VALUE(@JsonPayload, '$.StorageLimit') AS BIGINT),
            @IsActive = CAST(JSON_VALUE(@JsonPayload, '$.IsActive') AS BIT),
            @ContactEmail = JSON_VALUE(@JsonPayload, '$.ContactEmail'),
            @ContactPhone = JSON_VALUE(@JsonPayload, '$.ContactPhone'),
            @ContactPerson = JSON_VALUE(@JsonPayload, '$.ContactPerson'),
            @Status = JSON_VALUE(@JsonPayload, '$.Status');
        
        -- Update organization
        UPDATE Organizations 
        SET 
            OrganizationName = @OrganizationName,
            SubscriptionTier = @SubscriptionTier,
            MaxUsers = @MaxUsers,
            StorageLimit = @StorageLimit,
            IsActive = @IsActive,
            ContactEmail = @ContactEmail,
            ContactPhone = @ContactPhone,
            ContactPerson = @ContactPerson,
            Status = @Status,
            UpdatedAt = GETUTCDATE()
        WHERE OrganizationId = @OrganizationId;
        
        -- Return updated tenant
        SELECT 
            OrganizationId,
            TenantKey,
            OrganizationName,
            Domain,
            DatabaseName,
            ConnectionString,
            IsActive,
            CreatedAt,
            UpdatedAt,
            SubscriptionTier,
            MaxUsers,
            StorageLimit,
            ContactEmail,
            ContactPhone,
            ContactPerson,
            Status
        FROM Organizations 
        WHERE OrganizationId = @OrganizationId
        FOR JSON PATH, WITHOUT_ARRAY_WRAPPER;
        
    END TRY
    BEGIN CATCH
        THROW;
    END CATCH
END
GO

PRINT 'TenantRegistry stored procedures created successfully!';
PRINT 'You can now use dynamic connection string resolution.';
GO
