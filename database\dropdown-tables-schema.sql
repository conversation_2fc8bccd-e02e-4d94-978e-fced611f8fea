-- =====================================================
-- Dropdown Tables Schema for CA Portal Application
-- =====================================================
-- This script creates tables for all dropdown options identified in the React codebase
-- Each table follows the pattern: Name, DisplayName, Status, CreatedDate, UpdatedDate

-- =====================================================
-- 1. User Roles Dropdown
-- =====================================================
CREATE TABLE UserRoles (
    RoleId UNIQUEIDENTIFIER PRIMARY KEY DEFAULT NEWID(),
    Name NVARCHAR(50) UNIQUE NOT NULL,
    DisplayName NVARCHAR(100) NOT NULL,
    Status NVARCHAR(20) DEFAULT 'Active', -- Active, Inactive
    CreatedDate DATETIME2 DEFAULT GETUTCDATE(),
    UpdatedDate DATETIME2 DEFAULT GETUTCDATE(),
    Description NVARCHAR(500),
    SortOrder INT DEFAULT 0
);

-- =====================================================
-- 2. Task Statuses Dropdown
-- =====================================================
CREATE TABLE TaskStatuses (
    StatusId UNIQUEIDENTIFIER PRIMARY KEY DEFAULT NEWID(),
    Name NVARCHAR(50) UNIQUE NOT NULL,
    DisplayName NVARCHAR(100) NOT NULL,
    Status NVARCHAR(20) DEFAULT 'Active', -- Active, Inactive
    CreatedDate DATETIME2 DEFAULT GETUTCDATE(),
    UpdatedDate DATETIME2 DEFAULT GETUTCDATE(),
    ColorCode NVARCHAR(20), -- For UI styling
    SortOrder INT DEFAULT 0
);

-- =====================================================
-- 3. Task Priorities Dropdown
-- =====================================================
CREATE TABLE TaskPriorities (
    PriorityId UNIQUEIDENTIFIER PRIMARY KEY DEFAULT NEWID(),
    Name NVARCHAR(50) UNIQUE NOT NULL,
    DisplayName NVARCHAR(100) NOT NULL,
    Status NVARCHAR(20) DEFAULT 'Active', -- Active, Inactive
    CreatedDate DATETIME2 DEFAULT GETUTCDATE(),
    UpdatedDate DATETIME2 DEFAULT GETUTCDATE(),
    ColorCode NVARCHAR(20), -- For UI styling
    SortOrder INT DEFAULT 0
);

-- =====================================================
-- 4. Compliance Types Dropdown
-- =====================================================
CREATE TABLE ComplianceTypes (
    ComplianceTypeId UNIQUEIDENTIFIER PRIMARY KEY DEFAULT NEWID(),
    Name NVARCHAR(50) UNIQUE NOT NULL,
    DisplayName NVARCHAR(100) NOT NULL,
    Status NVARCHAR(20) DEFAULT 'Active', -- Active, Inactive
    CreatedDate DATETIME2 DEFAULT GETUTCDATE(),
    UpdatedDate DATETIME2 DEFAULT GETUTCDATE(),
    Description NVARCHAR(500),
    Category NVARCHAR(100), -- Tax, Regulatory, Audit, etc.
    SortOrder INT DEFAULT 0
);

-- =====================================================
-- 5. Compliance Sub-Types Dropdown
-- =====================================================
CREATE TABLE ComplianceSubTypes (
    SubTypeId UNIQUEIDENTIFIER PRIMARY KEY DEFAULT NEWID(),
    Name NVARCHAR(50) UNIQUE NOT NULL,
    DisplayName NVARCHAR(100) NOT NULL,
    Status NVARCHAR(20) DEFAULT 'Active', -- Active, Inactive
    CreatedDate DATETIME2 DEFAULT GETUTCDATE(),
    UpdatedDate DATETIME2 DEFAULT GETUTCDATE(),
    ComplianceTypeId UNIQUEIDENTIFIER REFERENCES ComplianceTypes(ComplianceTypeId),
    Description NVARCHAR(500),
    SortOrder INT DEFAULT 0
);

-- =====================================================
-- 6. Client Types Dropdown
-- =====================================================
CREATE TABLE ClientTypes (
    ClientTypeId UNIQUEIDENTIFIER PRIMARY KEY DEFAULT NEWID(),
    Name NVARCHAR(50) UNIQUE NOT NULL,
    DisplayName NVARCHAR(100) NOT NULL,
    Status NVARCHAR(20) DEFAULT 'Active', -- Active, Inactive
    CreatedDate DATETIME2 DEFAULT GETUTCDATE(),
    UpdatedDate DATETIME2 DEFAULT GETUTCDATE(),
    Description NVARCHAR(500),
    SortOrder INT DEFAULT 0
);

-- =====================================================
-- 7. Client Groups Dropdown
-- =====================================================
CREATE TABLE ClientGroups (
    ClientGroupId UNIQUEIDENTIFIER PRIMARY KEY DEFAULT NEWID(),
    Name NVARCHAR(50) UNIQUE NOT NULL,
    DisplayName NVARCHAR(100) NOT NULL,
    Status NVARCHAR(20) DEFAULT 'Active', -- Active, Inactive
    CreatedDate DATETIME2 DEFAULT GETUTCDATE(),
    UpdatedDate DATETIME2 DEFAULT GETUTCDATE(),
    Description NVARCHAR(500),
    SortOrder INT DEFAULT 0
);

-- =====================================================
-- 8. Document Types Dropdown
-- =====================================================
CREATE TABLE DocumentTypes (
    DocumentTypeId UNIQUEIDENTIFIER PRIMARY KEY DEFAULT NEWID(),
    Name NVARCHAR(50) UNIQUE NOT NULL,
    DisplayName NVARCHAR(100) NOT NULL,
    Status NVARCHAR(20) DEFAULT 'Active', -- Active, Inactive
    CreatedDate DATETIME2 DEFAULT GETUTCDATE(),
    UpdatedDate DATETIME2 DEFAULT GETUTCDATE(),
    Category NVARCHAR(100), -- Tax, Identity, Financial, etc.
    FileExtensions NVARCHAR(200), -- Allowed file extensions
    SortOrder INT DEFAULT 0
);

-- =====================================================
-- 9. Document Categories Dropdown
-- =====================================================
CREATE TABLE DocumentCategories (
    CategoryId UNIQUEIDENTIFIER PRIMARY KEY DEFAULT NEWID(),
    Name NVARCHAR(50) UNIQUE NOT NULL,
    DisplayName NVARCHAR(100) NOT NULL,
    Status NVARCHAR(20) DEFAULT 'Active', -- Active, Inactive
    CreatedDate DATETIME2 DEFAULT GETUTCDATE(),
    UpdatedDate DATETIME2 DEFAULT GETUTCDATE(),
    Description NVARCHAR(500),
    SortOrder INT DEFAULT 0
);

-- =====================================================
-- 10. Communication Types Dropdown
-- =====================================================
CREATE TABLE CommunicationTypes (
    CommunicationTypeId UNIQUEIDENTIFIER PRIMARY KEY DEFAULT NEWID(),
    Name NVARCHAR(50) UNIQUE NOT NULL,
    DisplayName NVARCHAR(100) NOT NULL,
    Status NVARCHAR(20) DEFAULT 'Active', -- Active, Inactive
    CreatedDate DATETIME2 DEFAULT GETUTCDATE(),
    UpdatedDate DATETIME2 DEFAULT GETUTCDATE(),
    Description NVARCHAR(500),
    SortOrder INT DEFAULT 0
);

-- =====================================================
-- 11. Communication Statuses Dropdown
-- =====================================================
CREATE TABLE CommunicationStatuses (
    CommunicationStatusId UNIQUEIDENTIFIER PRIMARY KEY DEFAULT NEWID(),
    Name NVARCHAR(50) UNIQUE NOT NULL,
    DisplayName NVARCHAR(100) NOT NULL,
    Status NVARCHAR(20) DEFAULT 'Active', -- Active, Inactive
    CreatedDate DATETIME2 DEFAULT GETUTCDATE(),
    UpdatedDate DATETIME2 DEFAULT GETUTCDATE(),
    ColorCode NVARCHAR(20), -- For UI styling
    SortOrder INT DEFAULT 0
);

-- =====================================================
-- 12. Report Types Dropdown
-- =====================================================
CREATE TABLE ReportTypes (
    ReportTypeId UNIQUEIDENTIFIER PRIMARY KEY DEFAULT NEWID(),
    Name NVARCHAR(50) UNIQUE NOT NULL,
    DisplayName NVARCHAR(100) NOT NULL,
    Status NVARCHAR(20) DEFAULT 'Active', -- Active, Inactive
    CreatedDate DATETIME2 DEFAULT GETUTCDATE(),
    UpdatedDate DATETIME2 DEFAULT GETUTCDATE(),
    Description NVARCHAR(500),
    Category NVARCHAR(100), -- Financial, Tax, Compliance, etc.
    SortOrder INT DEFAULT 0
);

-- =====================================================
-- 13. Filing Return Types Dropdown
-- =====================================================
CREATE TABLE FilingReturnTypes (
    ReturnTypeId UNIQUEIDENTIFIER PRIMARY KEY DEFAULT NEWID(),
    Name NVARCHAR(50) UNIQUE NOT NULL,
    DisplayName NVARCHAR(100) NOT NULL,
    Status NVARCHAR(20) DEFAULT 'Active', -- Active, Inactive
    CreatedDate DATETIME2 DEFAULT GETUTCDATE(),
    UpdatedDate DATETIME2 DEFAULT GETUTCDATE(),
    Description NVARCHAR(500),
    Category NVARCHAR(100), -- GST, TDS, TCS, etc.
    SortOrder INT DEFAULT 0
);

-- =====================================================
-- 14. Reminder Types Dropdown
-- =====================================================
CREATE TABLE ReminderTypes (
    ReminderTypeId UNIQUEIDENTIFIER PRIMARY KEY DEFAULT NEWID(),
    Name NVARCHAR(50) UNIQUE NOT NULL,
    DisplayName NVARCHAR(100) NOT NULL,
    Status NVARCHAR(20) DEFAULT 'Active', -- Active, Inactive
    CreatedDate DATETIME2 DEFAULT GETUTCDATE(),
    UpdatedDate DATETIME2 DEFAULT GETUTCDATE(),
    Description NVARCHAR(500),
    SortOrder INT DEFAULT 0
);

-- =====================================================
-- 15. Staff Modules Dropdown
-- =====================================================
CREATE TABLE StaffModules (
    ModuleId UNIQUEIDENTIFIER PRIMARY KEY DEFAULT NEWID(),
    Name NVARCHAR(50) UNIQUE NOT NULL,
    DisplayName NVARCHAR(100) NOT NULL,
    Status NVARCHAR(20) DEFAULT 'Active', -- Active, Inactive
    CreatedDate DATETIME2 DEFAULT GETUTCDATE(),
    UpdatedDate DATETIME2 DEFAULT GETUTCDATE(),
    Description NVARCHAR(500),
    ModuleRoute NVARCHAR(200), -- URL route for the module
    SortOrder INT DEFAULT 0
);

-- =====================================================
-- 16. Staff Statuses Dropdown
-- =====================================================
CREATE TABLE StaffStatuses (
    StaffStatusId UNIQUEIDENTIFIER PRIMARY KEY DEFAULT NEWID(),
    Name NVARCHAR(50) UNIQUE NOT NULL,
    DisplayName NVARCHAR(100) NOT NULL,
    Status NVARCHAR(20) DEFAULT 'Active', -- Active, Inactive
    CreatedDate DATETIME2 DEFAULT GETUTCDATE(),
    UpdatedDate DATETIME2 DEFAULT GETUTCDATE(),
    ColorCode NVARCHAR(20), -- For UI styling
    SortOrder INT DEFAULT 0
);

-- =====================================================
-- 17. Client Statuses Dropdown
-- =====================================================
CREATE TABLE ClientStatuses (
    ClientStatusId UNIQUEIDENTIFIER PRIMARY KEY DEFAULT NEWID(),
    Name NVARCHAR(50) UNIQUE NOT NULL,
    DisplayName NVARCHAR(100) NOT NULL,
    Status NVARCHAR(20) DEFAULT 'Active', -- Active, Inactive
    CreatedDate DATETIME2 DEFAULT GETUTCDATE(),
    UpdatedDate DATETIME2 DEFAULT GETUTCDATE(),
    ColorCode NVARCHAR(20), -- For UI styling
    SortOrder INT DEFAULT 0
);

-- =====================================================
-- Create Indexes for Performance
-- =====================================================

-- Create indexes on Name columns for fast lookups
CREATE INDEX IX_UserRoles_Name ON UserRoles(Name);
CREATE INDEX IX_TaskStatuses_Name ON TaskStatuses(Name);
CREATE INDEX IX_TaskPriorities_Name ON TaskPriorities(Name);
CREATE INDEX IX_ComplianceTypes_Name ON ComplianceTypes(Name);
CREATE INDEX IX_ComplianceSubTypes_Name ON ComplianceSubTypes(Name);
CREATE INDEX IX_ClientTypes_Name ON ClientTypes(Name);
CREATE INDEX IX_ClientGroups_Name ON ClientGroups(Name);
CREATE INDEX IX_DocumentTypes_Name ON DocumentTypes(Name);
CREATE INDEX IX_DocumentCategories_Name ON DocumentCategories(Name);
CREATE INDEX IX_CommunicationTypes_Name ON CommunicationTypes(Name);
CREATE INDEX IX_CommunicationStatuses_Name ON CommunicationStatuses(Name);
CREATE INDEX IX_ReportTypes_Name ON ReportTypes(Name);
CREATE INDEX IX_FilingReturnTypes_Name ON FilingReturnTypes(Name);
CREATE INDEX IX_ReminderTypes_Name ON ReminderTypes(Name);
CREATE INDEX IX_StaffModules_Name ON StaffModules(Name);
CREATE INDEX IX_StaffStatuses_Name ON StaffStatuses(Name);
CREATE INDEX IX_ClientStatuses_Name ON ClientStatuses(Name);

-- Create indexes on Status columns for filtering
CREATE INDEX IX_UserRoles_Status ON UserRoles(Status);
CREATE INDEX IX_TaskStatuses_Status ON TaskStatuses(Status);
CREATE INDEX IX_TaskPriorities_Status ON TaskPriorities(Status);
CREATE INDEX IX_ComplianceTypes_Status ON ComplianceTypes(Status);
CREATE INDEX IX_ComplianceSubTypes_Status ON ComplianceSubTypes(Status);
CREATE INDEX IX_ClientTypes_Status ON ClientTypes(Status);
CREATE INDEX IX_ClientGroups_Status ON ClientGroups(Status);
CREATE INDEX IX_DocumentTypes_Status ON DocumentTypes(Status);
CREATE INDEX IX_DocumentCategories_Status ON DocumentCategories(Status);
CREATE INDEX IX_CommunicationTypes_Status ON CommunicationTypes(Status);
CREATE INDEX IX_CommunicationStatuses_Status ON CommunicationStatuses(Status);
CREATE INDEX IX_ReportTypes_Status ON ReportTypes(Status);
CREATE INDEX IX_FilingReturnTypes_Status ON FilingReturnTypes(Status);
CREATE INDEX IX_ReminderTypes_Status ON ReminderTypes(Status);
CREATE INDEX IX_StaffModules_Status ON StaffModules(Status);
CREATE INDEX IX_StaffStatuses_Status ON StaffStatuses(Status);
CREATE INDEX IX_ClientStatuses_Status ON ClientStatuses(Status);

GO
