-- =============================================
-- Phase 1.3: Database Verification Script
-- Comprehensive validation of schema and data
-- =============================================

USE [KumarAssociatesDB];

PRINT '============================================================';
PRINT 'Phase 1.3: Database Verification Starting...';
PRINT '============================================================';

-- =============================================
-- 1. Table Existence Verification
-- =============================================
PRINT '';
PRINT '1. TABLE EXISTENCE VERIFICATION';
PRINT '----------------------------------------';

DECLARE @TableCount INT = 0;

IF EXISTS (SELECT * FROM sysobjects WHERE name='Compliance' AND xtype='U')
BEGIN
    PRINT '✅ Compliance table exists';
    SET @TableCount = @TableCount + 1;
END
ELSE
    PRINT '❌ Compliance table missing';

IF EXISTS (SELECT * FROM sysobjects WHERE name='ComplianceTypes' AND xtype='U')
BEGIN
    PRINT '✅ ComplianceTypes table exists';
    SET @TableCount = @TableCount + 1;
END
ELSE
    PRINT '❌ ComplianceTypes table missing';

IF EXISTS (SELECT * FROM sysobjects WHERE name='ComplianceHistory' AND xtype='U')
BEGIN
    PRINT '✅ ComplianceHistory table exists';
    SET @TableCount = @TableCount + 1;
END
ELSE
    PRINT '❌ ComplianceHistory table missing';

IF EXISTS (SELECT * FROM sysobjects WHERE name='ComplianceReminders' AND xtype='U')
BEGIN
    PRINT '✅ ComplianceReminders table exists';
    SET @TableCount = @TableCount + 1;
END
ELSE
    PRINT '❌ ComplianceReminders table missing';

IF EXISTS (SELECT * FROM sysobjects WHERE name='ComplianceAttachments' AND xtype='U')
BEGIN
    PRINT '✅ ComplianceAttachments table exists';
    SET @TableCount = @TableCount + 1;
END
ELSE
    PRINT '❌ ComplianceAttachments table missing';

IF EXISTS (SELECT * FROM sysobjects WHERE name='ComplianceComments' AND xtype='U')
BEGIN
    PRINT '✅ ComplianceComments table exists';
    SET @TableCount = @TableCount + 1;
END
ELSE
    PRINT '❌ ComplianceComments table missing';

PRINT 'Tables Created: ' + CAST(@TableCount AS NVARCHAR) + '/6';

-- =============================================
-- 2. Index Verification
-- =============================================
PRINT '';
PRINT '2. INDEX VERIFICATION';
PRINT '----------------------------------------';

DECLARE @IndexCount INT = 0;

-- Check key indexes
IF EXISTS (SELECT * FROM sys.indexes WHERE name = 'IX_Compliance_ClientId')
BEGIN
    PRINT '✅ IX_Compliance_ClientId exists';
    SET @IndexCount = @IndexCount + 1;
END

IF EXISTS (SELECT * FROM sys.indexes WHERE name = 'IX_Compliance_DueDate')
BEGIN
    PRINT '✅ IX_Compliance_DueDate exists';
    SET @IndexCount = @IndexCount + 1;
END

IF EXISTS (SELECT * FROM sys.indexes WHERE name = 'IX_Compliance_Status')
BEGIN
    PRINT '✅ IX_Compliance_Status exists';
    SET @IndexCount = @IndexCount + 1;
END

IF EXISTS (SELECT * FROM sys.indexes WHERE name = 'IX_Compliance_AssignedTo')
BEGIN
    PRINT '✅ IX_Compliance_AssignedTo exists';
    SET @IndexCount = @IndexCount + 1;
END

PRINT 'Key Indexes Created: ' + CAST(@IndexCount AS NVARCHAR) + '/4';

-- =============================================
-- 3. Foreign Key Constraints Verification
-- =============================================
PRINT '';
PRINT '3. FOREIGN KEY CONSTRAINTS VERIFICATION';
PRINT '----------------------------------------';

DECLARE @FKCount INT = 0;

IF EXISTS (SELECT * FROM sys.foreign_keys WHERE name = 'FK_ComplianceHistory_Compliance')
BEGIN
    PRINT '✅ FK_ComplianceHistory_Compliance exists';
    SET @FKCount = @FKCount + 1;
END

IF EXISTS (SELECT * FROM sys.foreign_keys WHERE name = 'FK_ComplianceReminders_Compliance')
BEGIN
    PRINT '✅ FK_ComplianceReminders_Compliance exists';
    SET @FKCount = @FKCount + 1;
END

IF EXISTS (SELECT * FROM sys.foreign_keys WHERE name = 'FK_ComplianceAttachments_Compliance')
BEGIN
    PRINT '✅ FK_ComplianceAttachments_Compliance exists';
    SET @FKCount = @FKCount + 1;
END

IF EXISTS (SELECT * FROM sys.foreign_keys WHERE name = 'FK_ComplianceComments_Compliance')
BEGIN
    PRINT '✅ FK_ComplianceComments_Compliance exists';
    SET @FKCount = @FKCount + 1;
END

PRINT 'Foreign Keys Created: ' + CAST(@FKCount AS NVARCHAR) + '/4';

-- =============================================
-- 4. Data Population Verification
-- =============================================
PRINT '';
PRINT '4. DATA POPULATION VERIFICATION';
PRINT '----------------------------------------';

DECLARE @ComplianceCount INT = (SELECT COUNT(*) FROM Compliance);
DECLARE @TypesCount INT = (SELECT COUNT(*) FROM ComplianceTypes);
DECLARE @ClientsCount INT = (SELECT COUNT(*) FROM Clients);
DECLARE @UsersCount INT = (SELECT COUNT(*) FROM Users);

PRINT 'Compliance Items: ' + CAST(@ComplianceCount AS NVARCHAR);
PRINT 'Compliance Types: ' + CAST(@TypesCount AS NVARCHAR);
PRINT 'Clients: ' + CAST(@ClientsCount AS NVARCHAR);
PRINT 'Users: ' + CAST(@UsersCount AS NVARCHAR);

-- Verify minimum data requirements
IF @ComplianceCount >= 50
    PRINT '✅ Compliance items meet requirement (50+)';
ELSE
    PRINT '❌ Insufficient compliance items (need 50+)';

IF @TypesCount >= 10
    PRINT '✅ Compliance types populated adequately';
ELSE
    PRINT '❌ Insufficient compliance types';

-- =============================================
-- 5. Data Quality Verification
-- =============================================
PRINT '';
PRINT '5. DATA QUALITY VERIFICATION';
PRINT '----------------------------------------';

-- Check for required fields
DECLARE @NullClientIds INT = (SELECT COUNT(*) FROM Compliance WHERE ClientId IS NULL);
DECLARE @NullDueDates INT = (SELECT COUNT(*) FROM Compliance WHERE DueDate IS NULL);
DECLARE @InvalidStatuses INT = (SELECT COUNT(*) FROM Compliance WHERE Status NOT IN ('Pending', 'In Progress', 'Completed', 'Overdue', 'Cancelled'));
DECLARE @InvalidPriorities INT = (SELECT COUNT(*) FROM Compliance WHERE Priority NOT IN ('Low', 'Medium', 'High', 'Critical'));

IF @NullClientIds = 0
    PRINT '✅ All compliance items have valid ClientId';
ELSE
    PRINT '❌ Found ' + CAST(@NullClientIds AS NVARCHAR) + ' compliance items with NULL ClientId';

IF @NullDueDates = 0
    PRINT '✅ All compliance items have valid DueDate';
ELSE
    PRINT '❌ Found ' + CAST(@NullDueDates AS NVARCHAR) + ' compliance items with NULL DueDate';

IF @InvalidStatuses = 0
    PRINT '✅ All compliance items have valid Status';
ELSE
    PRINT '❌ Found ' + CAST(@InvalidStatuses AS NVARCHAR) + ' compliance items with invalid Status';

IF @InvalidPriorities = 0
    PRINT '✅ All compliance items have valid Priority';
ELSE
    PRINT '❌ Found ' + CAST(@InvalidPriorities AS NVARCHAR) + ' compliance items with invalid Priority';

-- =============================================
-- 6. Business Logic Verification
-- =============================================
PRINT '';
PRINT '6. BUSINESS LOGIC VERIFICATION';
PRINT '----------------------------------------';

-- Check overdue items logic
DECLARE @OverdueCount INT = (SELECT COUNT(*) FROM Compliance WHERE Status = 'Overdue');
DECLARE @PastDueCount INT = (SELECT COUNT(*) FROM Compliance WHERE DueDate < GETUTCDATE() AND Status NOT IN ('Completed', 'Cancelled'));

PRINT 'Items marked as Overdue: ' + CAST(@OverdueCount AS NVARCHAR);
PRINT 'Items past due date: ' + CAST(@PastDueCount AS NVARCHAR);

-- Check completed items have completion dates
DECLARE @CompletedWithoutDate INT = (SELECT COUNT(*) FROM Compliance WHERE Status = 'Completed' AND CompletedAt IS NULL);

IF @CompletedWithoutDate = 0
    PRINT '✅ All completed items have completion dates';
ELSE
    PRINT '⚠️ Found ' + CAST(@CompletedWithoutDate AS NVARCHAR) + ' completed items without completion dates';

-- =============================================
-- 7. Performance Test Queries
-- =============================================
PRINT '';
PRINT '7. PERFORMANCE TEST QUERIES';
PRINT '----------------------------------------';

DECLARE @StartTime DATETIME2 = GETDATE();

-- Test query 1: Get compliance items by client
SELECT COUNT(*) AS ClientComplianceCount
FROM Compliance c
INNER JOIN Clients cl ON c.ClientId = cl.ClientId
WHERE cl.CompanyName LIKE '%ABC%';

DECLARE @Query1Time INT = DATEDIFF(MILLISECOND, @StartTime, GETDATE());
PRINT 'Query 1 (Client filter) execution time: ' + CAST(@Query1Time AS NVARCHAR) + 'ms';

SET @StartTime = GETDATE();

-- Test query 2: Get overdue items
SELECT COUNT(*) AS OverdueCount
FROM Compliance
WHERE Status = 'Overdue' AND DueDate < GETUTCDATE();

DECLARE @Query2Time INT = DATEDIFF(MILLISECOND, @StartTime, GETDATE());
PRINT 'Query 2 (Overdue filter) execution time: ' + CAST(@Query2Time AS NVARCHAR) + 'ms';

SET @StartTime = GETDATE();

-- Test query 3: Get items by date range
SELECT COUNT(*) AS DateRangeCount
FROM Compliance
WHERE DueDate BETWEEN GETUTCDATE() AND DATEADD(DAY, 30, GETUTCDATE());

DECLARE @Query3Time INT = DATEDIFF(MILLISECOND, @StartTime, GETDATE());
PRINT 'Query 3 (Date range) execution time: ' + CAST(@Query3Time AS NVARCHAR) + 'ms';

-- =============================================
-- 8. Detailed Statistics Report
-- =============================================
PRINT '';
PRINT '8. DETAILED STATISTICS REPORT';
PRINT '----------------------------------------';

-- Status distribution
SELECT 
    Status,
    COUNT(*) AS Count,
    CAST(COUNT(*) * 100.0 / (SELECT COUNT(*) FROM Compliance) AS DECIMAL(5,2)) AS Percentage
FROM Compliance
GROUP BY Status
ORDER BY Count DESC;

-- Priority distribution
SELECT 
    Priority,
    COUNT(*) AS Count,
    CAST(COUNT(*) * 100.0 / (SELECT COUNT(*) FROM Compliance) AS DECIMAL(5,2)) AS Percentage
FROM Compliance
GROUP BY Priority
ORDER BY 
    CASE Priority 
        WHEN 'Critical' THEN 1
        WHEN 'High' THEN 2
        WHEN 'Medium' THEN 3
        WHEN 'Low' THEN 4
    END;

-- Compliance type distribution
SELECT TOP 10
    ComplianceType,
    COUNT(*) AS Count
FROM Compliance
GROUP BY ComplianceType
ORDER BY Count DESC;

-- Due date distribution (next 90 days)
SELECT 
    CASE 
        WHEN DueDate < GETUTCDATE() THEN 'Overdue'
        WHEN DueDate <= DATEADD(DAY, 7, GETUTCDATE()) THEN 'Due This Week'
        WHEN DueDate <= DATEADD(DAY, 30, GETUTCDATE()) THEN 'Due This Month'
        WHEN DueDate <= DATEADD(DAY, 90, GETUTCDATE()) THEN 'Due Next 3 Months'
        ELSE 'Future'
    END AS DuePeriod,
    COUNT(*) AS Count
FROM Compliance
GROUP BY 
    CASE 
        WHEN DueDate < GETUTCDATE() THEN 'Overdue'
        WHEN DueDate <= DATEADD(DAY, 7, GETUTCDATE()) THEN 'Due This Week'
        WHEN DueDate <= DATEADD(DAY, 30, GETUTCDATE()) THEN 'Due This Month'
        WHEN DueDate <= DATEADD(DAY, 90, GETUTCDATE()) THEN 'Due Next 3 Months'
        ELSE 'Future'
    END
ORDER BY 
    CASE 
        WHEN DueDate < GETUTCDATE() THEN 1
        WHEN DueDate <= DATEADD(DAY, 7, GETUTCDATE()) THEN 2
        WHEN DueDate <= DATEADD(DAY, 30, GETUTCDATE()) THEN 3
        WHEN DueDate <= DATEADD(DAY, 90, GETUTCDATE()) THEN 4
        ELSE 5
    END;

-- =============================================
-- 9. Final Verification Summary
-- =============================================
PRINT '';
PRINT '============================================================';
PRINT 'PHASE 1 VERIFICATION SUMMARY';
PRINT '============================================================';

DECLARE @OverallScore INT = 0;
DECLARE @MaxScore INT = 100;

-- Calculate score based on various criteria
IF @TableCount = 6 SET @OverallScore = @OverallScore + 20;
IF @IndexCount >= 4 SET @OverallScore = @OverallScore + 15;
IF @FKCount >= 4 SET @OverallScore = @OverallScore + 15;
IF @ComplianceCount >= 50 SET @OverallScore = @OverallScore + 20;
IF @TypesCount >= 10 SET @OverallScore = @OverallScore + 10;
IF @NullClientIds = 0 AND @NullDueDates = 0 SET @OverallScore = @OverallScore + 10;
IF @InvalidStatuses = 0 AND @InvalidPriorities = 0 SET @OverallScore = @OverallScore + 10;

PRINT 'Overall Database Health Score: ' + CAST(@OverallScore AS NVARCHAR) + '/' + CAST(@MaxScore AS NVARCHAR);

IF @OverallScore >= 90
    PRINT '✅ EXCELLENT: Database is ready for Phase 2';
ELSE IF @OverallScore >= 75
    PRINT '✅ GOOD: Database is ready for Phase 2 with minor issues';
ELSE IF @OverallScore >= 60
    PRINT '⚠️ FAIR: Database needs attention before Phase 2';
ELSE
    PRINT '❌ POOR: Database requires significant fixes before Phase 2';

PRINT '';
PRINT 'Phase 1 Complete - Ready to proceed to Phase 2: Backend API Development';
PRINT '============================================================';
