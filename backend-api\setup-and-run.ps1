# CA Portal API - Automated Setup and Run Script
# PowerShell script to set up and run the .NET Core API

Write-Host "🚀 CA Portal API - Setup and Run Script" -ForegroundColor Green
Write-Host "=======================================" -ForegroundColor Green

# Function to check if a command exists
function Test-Command($cmdname) {
    return [bool](Get-Command -Name $cmdname -ErrorAction SilentlyContinue)
}

# Function to display colored output
function Write-ColorOutput($ForegroundColor) {
    $fc = $host.UI.RawUI.ForegroundColor
    $host.UI.RawUI.ForegroundColor = $ForegroundColor
    if ($args) {
        Write-Output $args
    } else {
        $input | Write-Output
    }
    $host.UI.RawUI.ForegroundColor = $fc
}

# Step 1: Check Prerequisites
Write-Host "`n📋 Step 1: Checking Prerequisites..." -ForegroundColor Yellow

# Check .NET SDK
if (Test-Command "dotnet") {
    $dotnetVersion = dotnet --version
    Write-Host "✅ .NET SDK found: $dotnetVersion" -ForegroundColor Green
    
    # Check if it's .NET 6.0 or later
    $version = [Version]$dotnetVersion
    if ($version.Major -ge 6) {
        Write-Host "✅ .NET version is compatible" -ForegroundColor Green
    } else {
        Write-Host "❌ .NET 6.0 or later required. Current version: $dotnetVersion" -ForegroundColor Red
        Write-Host "Please download from: https://dotnet.microsoft.com/download/dotnet/6.0" -ForegroundColor Yellow
        exit 1
    }
} else {
    Write-Host "❌ .NET SDK not found" -ForegroundColor Red
    Write-Host "Please install .NET 6.0 SDK from: https://dotnet.microsoft.com/download/dotnet/6.0" -ForegroundColor Yellow
    exit 1
}

# Check SQL Server (basic check)
Write-Host "`n🗄️ Checking SQL Server..." -ForegroundColor Yellow
$sqlInstances = @(
    "localhost",
    "localhost\SQLEXPRESS",
    "(localdb)\MSSQLLocalDB"
)

$sqlFound = $false
foreach ($instance in $sqlInstances) {
    try {
        $connectionString = "Server=$instance;Database=master;Integrated Security=true;Connection Timeout=5;"
        $connection = New-Object System.Data.SqlClient.SqlConnection($connectionString)
        $connection.Open()
        $connection.Close()
        Write-Host "✅ SQL Server found at: $instance" -ForegroundColor Green
        $sqlFound = $true
        break
    } catch {
        # Continue to next instance
    }
}

if (-not $sqlFound) {
    Write-Host "⚠️ SQL Server not found or not accessible" -ForegroundColor Yellow
    Write-Host "Please ensure SQL Server is installed and running" -ForegroundColor Yellow
    Write-Host "Download from: https://www.microsoft.com/en-us/sql-server/sql-server-downloads" -ForegroundColor Yellow
}

# Step 2: Project Setup
Write-Host "`n⚙️ Step 2: Setting up the project..." -ForegroundColor Yellow

# Check if we're in the right directory
if (-not (Test-Path "CAPortalAPI.csproj")) {
    Write-Host "❌ CAPortalAPI.csproj not found in current directory" -ForegroundColor Red
    Write-Host "Please navigate to the backend-api directory first" -ForegroundColor Yellow
    exit 1
}

# Restore packages
Write-Host "📦 Restoring NuGet packages..." -ForegroundColor Cyan
try {
    dotnet restore
    Write-Host "✅ Packages restored successfully" -ForegroundColor Green
} catch {
    Write-Host "❌ Failed to restore packages" -ForegroundColor Red
    Write-Host $_.Exception.Message -ForegroundColor Red
    exit 1
}

# Build project
Write-Host "`n🔨 Building the project..." -ForegroundColor Cyan
try {
    dotnet build --no-restore
    Write-Host "✅ Project built successfully" -ForegroundColor Green
} catch {
    Write-Host "❌ Build failed" -ForegroundColor Red
    Write-Host $_.Exception.Message -ForegroundColor Red
    exit 1
}

# Step 3: Configuration Check
Write-Host "`n⚙️ Step 3: Checking configuration..." -ForegroundColor Yellow

# Check appsettings.json
if (Test-Path "appsettings.json") {
    Write-Host "✅ appsettings.json found" -ForegroundColor Green
    
    # Read and parse JSON
    $appSettings = Get-Content "appsettings.json" | ConvertFrom-Json
    
    # Check connection strings
    if ($appSettings.ConnectionStrings) {
        Write-Host "✅ Connection strings configured" -ForegroundColor Green
        
        # Display connection strings (without sensitive data)
        $appSettings.ConnectionStrings.PSObject.Properties | ForEach-Object {
            $connStr = $_.Value
            $serverPart = ($connStr -split ';')[0]
            Write-Host "   - $($_.Name): $serverPart" -ForegroundColor Cyan
        }
    } else {
        Write-Host "⚠️ Connection strings not found in appsettings.json" -ForegroundColor Yellow
    }
    
    # Check JWT settings
    if ($appSettings.JwtSettings) {
        Write-Host "✅ JWT settings configured" -ForegroundColor Green
    } else {
        Write-Host "⚠️ JWT settings not found in appsettings.json" -ForegroundColor Yellow
    }
} else {
    Write-Host "❌ appsettings.json not found" -ForegroundColor Red
    exit 1
}

# Step 4: Database Setup Reminder
Write-Host "`n🗄️ Step 4: Database Setup Reminder..." -ForegroundColor Yellow
Write-Host "Before running the API, ensure you have:" -ForegroundColor Cyan
Write-Host "1. ✅ Created TenantRegistry database (master-database-schema.sql)" -ForegroundColor White
Write-Host "2. ✅ Inserted sample tenant data (dummy-data-insert.sql)" -ForegroundColor White
Write-Host "3. ✅ Created tenant database (tenant-database-template.sql)" -ForegroundColor White
Write-Host "4. ✅ Inserted sample tenant data (tenant-sample-data.sql)" -ForegroundColor White

$dbSetup = Read-Host "`nHave you completed the database setup? (y/n)"
if ($dbSetup -ne "y" -and $dbSetup -ne "Y") {
    Write-Host "Please complete the database setup first using SQL Server Management Studio" -ForegroundColor Yellow
    Write-Host "Scripts are located in the 'database' folder" -ForegroundColor Yellow
    exit 0
}

# Step 5: Run the application
Write-Host "`n🚀 Step 5: Starting the application..." -ForegroundColor Yellow

Write-Host "Starting CA Portal API..." -ForegroundColor Cyan
Write-Host "The application will be available at:" -ForegroundColor Green
Write-Host "  🌐 Swagger UI: https://localhost:7000" -ForegroundColor White
Write-Host "  🌐 API Base:   https://localhost:7000/api" -ForegroundColor White
Write-Host "`nPress Ctrl+C to stop the application" -ForegroundColor Yellow

# Trust development certificates
Write-Host "`n🔒 Trusting development certificates..." -ForegroundColor Cyan
try {
    dotnet dev-certs https --trust
    Write-Host "✅ Development certificates trusted" -ForegroundColor Green
} catch {
    Write-Host "⚠️ Could not trust development certificates" -ForegroundColor Yellow
}

Write-Host "`n" -ForegroundColor White
Write-Host "🎯 Quick Test Instructions:" -ForegroundColor Green
Write-Host "1. Open browser to https://localhost:7000" -ForegroundColor White
Write-Host "2. Try the login endpoint with:" -ForegroundColor White
Write-Host "   Email: <EMAIL>" -ForegroundColor Cyan
Write-Host "   Password: password123" -ForegroundColor Cyan
Write-Host "   TenantKey: kumar-associates" -ForegroundColor Cyan
Write-Host "3. Use the returned JWT token for other endpoints" -ForegroundColor White
Write-Host "`n" -ForegroundColor White

# Run the application
try {
    dotnet run
} catch {
    Write-Host "`n❌ Application failed to start" -ForegroundColor Red
    Write-Host $_.Exception.Message -ForegroundColor Red
    
    Write-Host "`n🔧 Troubleshooting tips:" -ForegroundColor Yellow
    Write-Host "1. Check if port 7000 is already in use" -ForegroundColor White
    Write-Host "2. Verify database connection strings" -ForegroundColor White
    Write-Host "3. Ensure SQL Server is running" -ForegroundColor White
    Write-Host "4. Check firewall/antivirus settings" -ForegroundColor White
    
    exit 1
}

Write-Host "`n👋 Application stopped. Thank you for using CA Portal API!" -ForegroundColor Green
