// Client Service
import { apiClient } from './api';

export interface Client {
  clientId: string;
  clientCode: string;
  companyName: string;
  contactPerson?: string;
  email?: string;
  phone?: string;
  address?: string;
  city?: string;
  state?: string;
  pinCode?: string;
  gstNumber?: string;
  panNumber?: string;
  clientType: string;
  businessType?: string;
  annualTurnover?: number;
  isActive: boolean;
  createdAt: string;
  createdBy: string;
  updatedAt: string;
  updatedBy?: string;
}

export interface CreateClientRequest {
  clientCode: string;
  companyName: string;
  contactPerson?: string;
  email?: string;
  phone?: string;
  address?: string;
  city?: string;
  state?: string;
  pinCode?: string;
  gstNumber?: string;
  panNumber?: string;
  clientType: string;
  businessType?: string;
  annualTurnover?: number;
}

export interface UpdateClientRequest {
  clientId: string;
  clientCode: string;
  companyName: string;
  contactPerson?: string;
  email?: string;
  phone?: string;
  address?: string;
  city?: string;
  state?: string;
  pinCode?: string;
  gstNumber?: string;
  panNumber?: string;
  clientType: string;
  businessType?: string;
  annualTurnover?: number;
  isActive: boolean;
}

export interface ClientListRequest {
  pageNumber?: number;
  pageSize?: number;
  searchTerm?: string;
  clientType?: string;
  businessType?: string;
  isActive?: boolean;
  city?: string;
  state?: string;
}

export interface ClientListResponse {
  clients: Client[];
  totalCount: number;
  pageNumber: number;
  pageSize: number;
  totalPages: number;
}

class ClientService {
  /**
   * Get paginated list of clients
   */
  async getClients(request: ClientListRequest = {}): Promise<ClientListResponse> {
    try {
      const params = new URLSearchParams();
      
      if (request.pageNumber) params.append('pageNumber', request.pageNumber.toString());
      if (request.pageSize) params.append('pageSize', request.pageSize.toString());
      if (request.searchTerm) params.append('searchTerm', request.searchTerm);
      if (request.clientType) params.append('clientType', request.clientType);
      if (request.businessType) params.append('businessType', request.businessType);
      if (request.isActive !== undefined) params.append('isActive', request.isActive.toString());
      if (request.city) params.append('city', request.city);
      if (request.state) params.append('state', request.state);

      const response = await apiClient.get<ClientListResponse>(`/clients?${params.toString()}`);
      
      if (response.success && response.data) {
        return response.data;
      } else {
        throw new Error(response.message || 'Failed to fetch clients');
      }
    } catch (error: any) {
      console.error('Get clients error:', error);
      throw new Error(error.response?.data?.message || 'Failed to fetch clients');
    }
  }

  /**
   * Get client by ID
   */
  async getClientById(clientId: string): Promise<Client> {
    try {
      const response = await apiClient.get<Client>(`/clients/${clientId}`);
      
      if (response.success && response.data) {
        return response.data;
      } else {
        throw new Error(response.message || 'Failed to fetch client');
      }
    } catch (error: any) {
      console.error('Get client by ID error:', error);
      throw new Error(error.response?.data?.message || 'Failed to fetch client');
    }
  }

  /**
   * Create new client
   */
  async createClient(request: CreateClientRequest): Promise<Client> {
    try {
      const response = await apiClient.post<Client>('/clients', request);
      
      if (response.success && response.data) {
        return response.data;
      } else {
        throw new Error(response.message || 'Failed to create client');
      }
    } catch (error: any) {
      console.error('Create client error:', error);
      throw new Error(error.response?.data?.message || 'Failed to create client');
    }
  }

  /**
   * Update client
   */
  async updateClient(request: UpdateClientRequest): Promise<Client> {
    try {
      const response = await apiClient.put<Client>(`/clients/${request.clientId}`, request);
      
      if (response.success && response.data) {
        return response.data;
      } else {
        throw new Error(response.message || 'Failed to update client');
      }
    } catch (error: any) {
      console.error('Update client error:', error);
      throw new Error(error.response?.data?.message || 'Failed to update client');
    }
  }

  /**
   * Delete client
   */
  async deleteClient(clientId: string): Promise<void> {
    try {
      const response = await apiClient.delete(`/clients/${clientId}`);
      
      if (!response.success) {
        throw new Error(response.message || 'Failed to delete client');
      }
    } catch (error: any) {
      console.error('Delete client error:', error);
      throw new Error(error.response?.data?.message || 'Failed to delete client');
    }
  }

  /**
   * Activate client
   */
  async activateClient(clientId: string): Promise<void> {
    try {
      const response = await apiClient.post(`/clients/${clientId}/activate`);
      
      if (!response.success) {
        throw new Error(response.message || 'Failed to activate client');
      }
    } catch (error: any) {
      console.error('Activate client error:', error);
      throw new Error(error.response?.data?.message || 'Failed to activate client');
    }
  }

  /**
   * Deactivate client
   */
  async deactivateClient(clientId: string): Promise<void> {
    try {
      const response = await apiClient.post(`/clients/${clientId}/deactivate`);
      
      if (!response.success) {
        throw new Error(response.message || 'Failed to deactivate client');
      }
    } catch (error: any) {
      console.error('Deactivate client error:', error);
      throw new Error(error.response?.data?.message || 'Failed to deactivate client');
    }
  }

  /**
   * Search clients
   */
  async searchClients(searchTerm: string, limit: number = 10): Promise<Client[]> {
    try {
      const response = await apiClient.get<Client[]>(`/clients/search?searchTerm=${encodeURIComponent(searchTerm)}&limit=${limit}`);
      
      if (response.success && response.data) {
        return response.data;
      } else {
        throw new Error(response.message || 'Failed to search clients');
      }
    } catch (error: any) {
      console.error('Search clients error:', error);
      throw new Error(error.response?.data?.message || 'Failed to search clients');
    }
  }

  /**
   * Get available client types
   */
  getClientTypes(): string[] {
    return [
      'Individual',
      'Company',
      'Partnership',
      'LLP',
      'Trust',
      'HUF',
      'Proprietorship'
    ];
  }

  /**
   * Get available business types
   */
  getBusinessTypes(): string[] {
    return [
      'Manufacturing',
      'Trading',
      'Service',
      'Professional',
      'Real Estate',
      'Construction',
      'IT/Software',
      'Healthcare',
      'Education',
      'Retail',
      'Hospitality',
      'Agriculture',
      'Other'
    ];
  }

  /**
   * Get Indian states
   */
  getStates(): string[] {
    return [
      'Andhra Pradesh',
      'Arunachal Pradesh',
      'Assam',
      'Bihar',
      'Chhattisgarh',
      'Goa',
      'Gujarat',
      'Haryana',
      'Himachal Pradesh',
      'Jharkhand',
      'Karnataka',
      'Kerala',
      'Madhya Pradesh',
      'Maharashtra',
      'Manipur',
      'Meghalaya',
      'Mizoram',
      'Nagaland',
      'Odisha',
      'Punjab',
      'Rajasthan',
      'Sikkim',
      'Tamil Nadu',
      'Telangana',
      'Tripura',
      'Uttar Pradesh',
      'Uttarakhand',
      'West Bengal',
      'Delhi',
      'Jammu and Kashmir',
      'Ladakh',
      'Puducherry',
      'Chandigarh',
      'Dadra and Nagar Haveli and Daman and Diu',
      'Lakshadweep',
      'Andaman and Nicobar Islands'
    ];
  }

  /**
   * Validate GST number
   */
  validateGSTNumber(gstNumber: string): boolean {
    const gstRegex = /^[0-9]{2}[A-Z]{5}[0-9]{4}[A-Z]{1}[1-9A-Z]{1}Z[0-9A-Z]{1}$/;
    return gstRegex.test(gstNumber);
  }

  /**
   * Validate PAN number
   */
  validatePANNumber(panNumber: string): boolean {
    const panRegex = /^[A-Z]{5}[0-9]{4}[A-Z]{1}$/;
    return panRegex.test(panNumber);
  }

  /**
   * Get client statistics
   */
  async getClientStats(): Promise<{
    totalClients: number;
    activeClients: number;
    inactiveClients: number;
    clientsByType: { type: string; count: number }[];
    clientsByState: { state: string; count: number }[];
  }> {
    try {
      // This would be a separate endpoint in a real application
      const allClients = await this.getClients({ pageSize: 1000 });
      
      const totalClients = allClients.totalCount;
      const activeClients = allClients.clients.filter(c => c.isActive).length;
      const inactiveClients = totalClients - activeClients;
      
      // Group by type
      const typeGroups = allClients.clients.reduce((acc, client) => {
        acc[client.clientType] = (acc[client.clientType] || 0) + 1;
        return acc;
      }, {} as Record<string, number>);
      
      const clientsByType = Object.entries(typeGroups).map(([type, count]) => ({
        type,
        count
      }));
      
      // Group by state
      const stateGroups = allClients.clients.reduce((acc, client) => {
        const state = client.state || 'Unknown';
        acc[state] = (acc[state] || 0) + 1;
        return acc;
      }, {} as Record<string, number>);
      
      const clientsByState = Object.entries(stateGroups).map(([state, count]) => ({
        state,
        count
      }));
      
      return {
        totalClients,
        activeClients,
        inactiveClients,
        clientsByType,
        clientsByState
      };
    } catch (error: any) {
      console.error('Get client stats error:', error);
      throw new Error('Failed to fetch client statistics');
    }
  }
}

// Export singleton instance
export const clientService = new ClientService();
export default clientService;
