// Test Create Compliance Item API Endpoint
const https = require('https');

// Disable SSL certificate validation for localhost testing
process.env["NODE_TLS_REJECT_UNAUTHORIZED"] = 0;

const testPayload = {
  clientId: "EA7BE4A7-F542-41E3-BAA3-0005B145761E",
  complianceType: "API Endpoint Test",
  subType: "Node.js Test",
  description: "Testing the create compliance API endpoint via Node.js",
  dueDate: "2024-12-31T23:59:59Z",
  priority: "Medium",
  notes: "Created via Node.js API test"
};

const postData = JSON.stringify(testPayload);

const options = {
  hostname: 'localhost',
  port: 7000,
  path: '/api/compliance',
  method: 'POST',
  headers: {
    'Content-Type': 'application/json',
    'Content-Length': Buffer.byteLength(postData)
  }
};

console.log('🧪 Testing Create Compliance Item API...');
console.log('📤 Sending POST request to https://localhost:7000/api/compliance');
console.log('📋 Payload:', JSON.stringify(testPayload, null, 2));

const req = https.request(options, (res) => {
  console.log(`📊 Status Code: ${res.statusCode}`);
  console.log(`📋 Headers:`, res.headers);

  let data = '';
  res.on('data', (chunk) => {
    data += chunk;
  });

  res.on('end', () => {
    console.log('\n📥 Response:');
    try {
      const response = JSON.parse(data);
      console.log(JSON.stringify(response, null, 2));
      
      if (response.success) {
        console.log('\n✅ SUCCESS! Compliance item created successfully!');
        console.log(`🆔 New Compliance ID: ${response.data.complianceId}`);
        console.log(`📝 Type: ${response.data.complianceType}`);
        console.log(`👤 Client: ${response.data.clientName}`);
        console.log(`📅 Due Date: ${response.data.dueDate}`);
        console.log(`⚡ Status: ${response.data.status}`);
        console.log(`🔥 Priority: ${response.data.priority}`);
      } else {
        console.log('\n❌ ERROR: API returned failure response');
        console.log('Errors:', response.errors);
      }
    } catch (error) {
      console.log('\n❌ ERROR: Failed to parse JSON response');
      console.log('Raw response:', data);
    }
  });
});

req.on('error', (error) => {
  console.error('\n❌ ERROR: Request failed');
  console.error(error.message);
});

req.write(postData);
req.end();

console.log('\n⏳ Waiting for response...');
