// Environment Test Utility
// This file helps debug environment variable issues

export const testEnvironment = () => {
  console.group('🔧 Environment Variable Test');
  
  // Test different ways to access environment variables
  console.log('=== Testing Environment Variable Access ===');
  
  // 1. Test import.meta.env (Vite)
  console.log('1. import.meta.env:', typeof import.meta !== 'undefined' ? import.meta.env : 'Not available');
  
  // 2. Test process.env (Node.js/CRA)
  console.log('2. process.env:', typeof process !== 'undefined' ? process.env : 'Not available');
  
  // 3. Test window.env (Runtime config)
  console.log('3. window.env:', typeof window !== 'undefined' ? (window as any).env : 'Not available');
  
  // 4. Test globalThis.process (Global fallback)
  console.log('4. globalThis.process:', typeof globalThis !== 'undefined' ? (globalThis as any).process : 'Not available');
  
  console.log('=== Environment Variables ===');
  
  // Test specific environment variables
  const testVars = [
    'REACT_APP_API_URL',
    'REACT_APP_TENANT_KEY',
    'REACT_APP_ENVIRONMENT'
  ];
  
  testVars.forEach(varName => {
    let value = 'Not found';
    let source = 'None';
    
    // Try import.meta.env first (Vite)
    if (typeof import.meta !== 'undefined' && import.meta.env && import.meta.env[varName]) {
      value = import.meta.env[varName];
      source = 'import.meta.env';
    }
    // Try process.env (CRA)
    else if (typeof process !== 'undefined' && process.env && process.env[varName]) {
      value = process.env[varName];
      source = 'process.env';
    }
    // Try window.env (Runtime)
    else if (typeof window !== 'undefined' && (window as any).env && (window as any).env[varName]) {
      value = (window as any).env[varName];
      source = 'window.env';
    }
    
    console.log(`${varName}: "${value}" (from ${source})`);
  });
  
  console.groupEnd();
  
  return {
    hasImportMeta: typeof import.meta !== 'undefined',
    hasProcess: typeof process !== 'undefined',
    hasWindow: typeof window !== 'undefined',
    hasGlobalThis: typeof globalThis !== 'undefined'
  };
};

// Auto-run in development
if (typeof window !== 'undefined' && window.location.hostname === 'localhost') {
  testEnvironment();
}
