-- =============================================
-- Compliance Management Stored Procedures
-- =============================================

-- Create Compliance table if it doesn't exist
IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='Compliance' AND xtype='U')
BEGIN
    CREATE TABLE Compliance (
        ComplianceId UNIQUEIDENTIFIER PRIMARY KEY DEFAULT NEWID(),
        ClientId UNIQUEIDENTIFIER NOT NULL,
        ComplianceType NVARCHAR(100) NOT NULL,
        SubType NVARCHAR(100) NULL,
        Description NVARCHAR(MAX) NULL,
        DueDate DATETIME NOT NULL,
        Status NVARCHAR(50) NOT NULL DEFAULT 'Pending',
        Priority NVARCHAR(20) NOT NULL DEFAULT 'Medium',
        AssignedTo UNIQUEIDENTIFIER NULL,
        CreatedAt DATETIME NOT NULL DEFAULT GETUTCDATE(),
        CreatedBy UNIQUEIDENTIFIER NULL,
        UpdatedAt DATETIME NULL,
        UpdatedBy UNIQUEIDENTIFIER NULL,
        CompletedAt DATETIME NULL,
        CompletedBy UNIQUEIDENTIFIER NULL,
        Notes NVARCHAR(MAX) NULL,
        ReminderSent BIT NOT NULL DEFAULT 0,
        LastReminderDate DATETIME NULL,
        IsActive BIT NOT NULL DEFAULT 1,
        
        CONSTRAINT FK_Compliance_Client FOREIGN KEY (ClientId) REFERENCES Clients(ClientId),
        CONSTRAINT FK_Compliance_AssignedTo FOREIGN KEY (AssignedTo) REFERENCES Users(UserId),
        CONSTRAINT FK_Compliance_CreatedBy FOREIGN KEY (CreatedBy) REFERENCES Users(UserId),
        CONSTRAINT FK_Compliance_UpdatedBy FOREIGN KEY (UpdatedBy) REFERENCES Users(UserId),
        CONSTRAINT FK_Compliance_CompletedBy FOREIGN KEY (CompletedBy) REFERENCES Users(UserId)
    );
    
    -- Create indexes for better performance
    CREATE INDEX IX_Compliance_ClientId ON Compliance(ClientId);
    CREATE INDEX IX_Compliance_DueDate ON Compliance(DueDate);
    CREATE INDEX IX_Compliance_Status ON Compliance(Status);
    CREATE INDEX IX_Compliance_AssignedTo ON Compliance(AssignedTo);
    CREATE INDEX IX_Compliance_ComplianceType ON Compliance(ComplianceType);
END

-- =============================================
-- Procedure: sp_GetComplianceItems
-- Description: Get paginated list of compliance items
-- =============================================
CREATE OR ALTER PROCEDURE sp_GetComplianceItems
    @Payload NVARCHAR(MAX)
AS
BEGIN
    SET NOCOUNT ON;
    
    DECLARE @ClientId NVARCHAR(50)
    DECLARE @PageNumber INT
    DECLARE @PageSize INT
    
    -- Parse JSON payload
    SELECT 
        @ClientId = JSON_VALUE(@Payload, '$.ClientId'),
        @PageNumber = ISNULL(JSON_VALUE(@Payload, '$.PageNumber'), 1),
        @PageSize = ISNULL(JSON_VALUE(@Payload, '$.PageSize'), 20)
    
    DECLARE @Offset INT = (@PageNumber - 1) * @PageSize
    
    -- Get total count
    DECLARE @TotalCount INT
    SELECT @TotalCount = COUNT(*)
    FROM Compliance c
    INNER JOIN Clients cl ON c.ClientId = cl.ClientId
    LEFT JOIN Users u ON c.AssignedTo = u.UserId
    WHERE c.IsActive = 1
        AND (@ClientId IS NULL OR @ClientId = '' OR c.ClientId = @ClientId)
    
    -- Get compliance items
    SELECT 
        c.ComplianceId,
        c.ClientId,
        cl.CompanyName AS ClientName,
        c.ComplianceType,
        c.SubType,
        c.Description,
        c.DueDate,
        c.Status,
        c.Priority,
        c.AssignedTo,
        ISNULL(u.FirstName + ' ' + u.LastName, 'Unassigned') AS AssignedToName,
        c.CreatedAt,
        c.CompletedAt,
        c.Notes,
        c.ReminderSent,
        c.LastReminderDate,
        @TotalCount AS TotalCount,
        @PageNumber AS PageNumber,
        @PageSize AS PageSize,
        CEILING(CAST(@TotalCount AS FLOAT) / @PageSize) AS TotalPages
    FROM Compliance c
    INNER JOIN Clients cl ON c.ClientId = cl.ClientId
    LEFT JOIN Users u ON c.AssignedTo = u.UserId
    WHERE c.IsActive = 1
        AND (@ClientId IS NULL OR @ClientId = '' OR c.ClientId = @ClientId)
    ORDER BY 
        CASE WHEN c.Status = 'Overdue' THEN 1
             WHEN c.Status = 'Pending' THEN 2
             WHEN c.Status = 'In Progress' THEN 3
             ELSE 4 END,
        c.DueDate ASC
    OFFSET @Offset ROWS
    FETCH NEXT @PageSize ROWS ONLY
    
    FOR JSON PATH
END

-- =============================================
-- Procedure: sp_GetComplianceItemById
-- Description: Get single compliance item by ID
-- =============================================
CREATE OR ALTER PROCEDURE sp_GetComplianceItemById
    @Payload NVARCHAR(MAX)
AS
BEGIN
    SET NOCOUNT ON;
    
    DECLARE @ComplianceId UNIQUEIDENTIFIER
    SELECT @ComplianceId = JSON_VALUE(@Payload, '$.ComplianceId')
    
    SELECT 
        c.ComplianceId,
        c.ClientId,
        cl.CompanyName AS ClientName,
        c.ComplianceType,
        c.SubType,
        c.Description,
        c.DueDate,
        c.Status,
        c.Priority,
        c.AssignedTo,
        ISNULL(u.FirstName + ' ' + u.LastName, 'Unassigned') AS AssignedToName,
        c.CreatedAt,
        c.CompletedAt,
        c.Notes,
        c.ReminderSent,
        c.LastReminderDate
    FROM Compliance c
    INNER JOIN Clients cl ON c.ClientId = cl.ClientId
    LEFT JOIN Users u ON c.AssignedTo = u.UserId
    WHERE c.ComplianceId = @ComplianceId AND c.IsActive = 1
    
    FOR JSON PATH, WITHOUT_ARRAY_WRAPPER
END

-- =============================================
-- Procedure: sp_CreateComplianceItem
-- Description: Create new compliance item
-- =============================================
CREATE OR ALTER PROCEDURE sp_CreateComplianceItem
    @Payload NVARCHAR(MAX)
AS
BEGIN
    SET NOCOUNT ON;
    
    DECLARE @ComplianceId UNIQUEIDENTIFIER = NEWID()
    DECLARE @ClientId UNIQUEIDENTIFIER
    DECLARE @ComplianceType NVARCHAR(100)
    DECLARE @SubType NVARCHAR(100)
    DECLARE @Description NVARCHAR(MAX)
    DECLARE @DueDate DATETIME
    DECLARE @Priority NVARCHAR(20)
    DECLARE @AssignedTo UNIQUEIDENTIFIER
    DECLARE @Notes NVARCHAR(MAX)
    DECLARE @CreatedBy UNIQUEIDENTIFIER
    
    -- Parse JSON payload
    SELECT 
        @ClientId = JSON_VALUE(@Payload, '$.ClientId'),
        @ComplianceType = JSON_VALUE(@Payload, '$.ComplianceType'),
        @SubType = JSON_VALUE(@Payload, '$.SubType'),
        @Description = JSON_VALUE(@Payload, '$.Description'),
        @DueDate = JSON_VALUE(@Payload, '$.DueDate'),
        @Priority = ISNULL(JSON_VALUE(@Payload, '$.Priority'), 'Medium'),
        @AssignedTo = JSON_VALUE(@Payload, '$.AssignedTo'),
        @Notes = JSON_VALUE(@Payload, '$.Notes'),
        @CreatedBy = JSON_VALUE(@Payload, '$.CreatedBy')
    
    -- Validate required fields
    IF @ClientId IS NULL OR @ComplianceType IS NULL OR @DueDate IS NULL
    BEGIN
        RAISERROR('Required fields missing: ClientId, ComplianceType, and DueDate are required', 16, 1)
        RETURN
    END
    
    -- Insert new compliance item
    INSERT INTO Compliance (
        ComplianceId, ClientId, ComplianceType, SubType, Description,
        DueDate, Priority, AssignedTo, Notes, CreatedBy, CreatedAt
    )
    VALUES (
        @ComplianceId, @ClientId, @ComplianceType, @SubType, @Description,
        @DueDate, @Priority, @AssignedTo, @Notes, @CreatedBy, GETUTCDATE()
    )
    
    -- Return the created item
    EXEC sp_GetComplianceItemById @Payload = '{"ComplianceId": "' + CAST(@ComplianceId AS NVARCHAR(50)) + '"}'
END

-- =============================================
-- Procedure: sp_UpdateComplianceItem
-- Description: Update existing compliance item
-- =============================================
CREATE OR ALTER PROCEDURE sp_UpdateComplianceItem
    @Payload NVARCHAR(MAX)
AS
BEGIN
    SET NOCOUNT ON;
    
    DECLARE @ComplianceId UNIQUEIDENTIFIER
    DECLARE @ComplianceType NVARCHAR(100)
    DECLARE @SubType NVARCHAR(100)
    DECLARE @Description NVARCHAR(MAX)
    DECLARE @DueDate DATETIME
    DECLARE @Status NVARCHAR(50)
    DECLARE @Priority NVARCHAR(20)
    DECLARE @AssignedTo UNIQUEIDENTIFIER
    DECLARE @Notes NVARCHAR(MAX)
    DECLARE @UpdatedBy UNIQUEIDENTIFIER
    
    -- Parse JSON payload
    SELECT 
        @ComplianceId = JSON_VALUE(@Payload, '$.ComplianceId'),
        @ComplianceType = JSON_VALUE(@Payload, '$.ComplianceType'),
        @SubType = JSON_VALUE(@Payload, '$.SubType'),
        @Description = JSON_VALUE(@Payload, '$.Description'),
        @DueDate = JSON_VALUE(@Payload, '$.DueDate'),
        @Status = JSON_VALUE(@Payload, '$.Status'),
        @Priority = JSON_VALUE(@Payload, '$.Priority'),
        @AssignedTo = JSON_VALUE(@Payload, '$.AssignedTo'),
        @Notes = JSON_VALUE(@Payload, '$.Notes'),
        @UpdatedBy = JSON_VALUE(@Payload, '$.UpdatedBy')
    
    -- Update compliance item
    UPDATE Compliance
    SET 
        ComplianceType = ISNULL(@ComplianceType, ComplianceType),
        SubType = @SubType,
        Description = ISNULL(@Description, Description),
        DueDate = ISNULL(@DueDate, DueDate),
        Status = ISNULL(@Status, Status),
        Priority = ISNULL(@Priority, Priority),
        AssignedTo = @AssignedTo,
        Notes = @Notes,
        UpdatedBy = @UpdatedBy,
        UpdatedAt = GETUTCDATE()
    WHERE ComplianceId = @ComplianceId AND IsActive = 1
    
    -- Return the updated item
    EXEC sp_GetComplianceItemById @Payload = '{"ComplianceId": "' + CAST(@ComplianceId AS NVARCHAR(50)) + '"}'
END

-- =============================================
-- Procedure: sp_DeleteComplianceItem
-- Description: Soft delete compliance item
-- =============================================
CREATE OR ALTER PROCEDURE sp_DeleteComplianceItem
    @Payload NVARCHAR(MAX)
AS
BEGIN
    SET NOCOUNT ON;
    
    DECLARE @ComplianceId UNIQUEIDENTIFIER
    DECLARE @UpdatedBy UNIQUEIDENTIFIER
    
    SELECT 
        @ComplianceId = JSON_VALUE(@Payload, '$.ComplianceId'),
        @UpdatedBy = JSON_VALUE(@Payload, '$.UpdatedBy')
    
    UPDATE Compliance
    SET 
        IsActive = 0,
        UpdatedBy = @UpdatedBy,
        UpdatedAt = GETUTCDATE()
    WHERE ComplianceId = @ComplianceId
    
    SELECT @@ROWCOUNT AS RowsAffected
END

-- =============================================
-- Procedure: sp_GetComplianceStats
-- Description: Get compliance dashboard statistics
-- =============================================
CREATE OR ALTER PROCEDURE sp_GetComplianceStats
    @Payload NVARCHAR(MAX)
AS
BEGIN
    SET NOCOUNT ON;
    
    DECLARE @ClientId UNIQUEIDENTIFIER
    SELECT @ClientId = JSON_VALUE(@Payload, '$.ClientId')
    
    SELECT 
        COUNT(*) AS TotalItems,
        SUM(CASE WHEN Status = 'Completed' THEN 1 ELSE 0 END) AS CompletedItems,
        SUM(CASE WHEN Status = 'Pending' THEN 1 ELSE 0 END) AS PendingItems,
        SUM(CASE WHEN Status = 'Overdue' OR (Status != 'Completed' AND DueDate < GETUTCDATE()) THEN 1 ELSE 0 END) AS OverdueItems
    FROM Compliance
    WHERE IsActive = 1
        AND (@ClientId IS NULL OR ClientId = @ClientId)
    
    FOR JSON PATH, WITHOUT_ARRAY_WRAPPER
END

-- =============================================
-- Procedure: sp_GetComplianceCalendar
-- Description: Get compliance items for calendar view
-- =============================================
CREATE OR ALTER PROCEDURE sp_GetComplianceCalendar
    @Payload NVARCHAR(MAX)
AS
BEGIN
    SET NOCOUNT ON;

    DECLARE @StartDate DATETIME
    DECLARE @EndDate DATETIME
    DECLARE @ClientId UNIQUEIDENTIFIER

    SELECT
        @StartDate = JSON_VALUE(@Payload, '$.StartDate'),
        @EndDate = JSON_VALUE(@Payload, '$.EndDate'),
        @ClientId = JSON_VALUE(@Payload, '$.ClientId')

    SELECT
        c.ComplianceId,
        c.ComplianceType AS Title,
        cl.CompanyName AS ClientName,
        c.ComplianceType,
        c.DueDate,
        c.Status,
        c.Priority,
        ISNULL(u.FirstName + ' ' + u.LastName, 'Unassigned') AS AssignedToName,
        CASE
            WHEN c.Status != 'Completed' AND c.DueDate < GETUTCDATE() THEN 1
            ELSE 0
        END AS IsOverdue,
        DATEDIFF(DAY, GETUTCDATE(), c.DueDate) AS DaysUntilDue
    FROM Compliance c
    INNER JOIN Clients cl ON c.ClientId = cl.ClientId
    LEFT JOIN Users u ON c.AssignedTo = u.UserId
    WHERE c.IsActive = 1
        AND c.DueDate BETWEEN @StartDate AND @EndDate
        AND (@ClientId IS NULL OR c.ClientId = @ClientId)
    ORDER BY c.DueDate ASC

    FOR JSON PATH
END

-- =============================================
-- Procedure: sp_MarkComplianceCompleted
-- Description: Mark compliance item as completed
-- =============================================
CREATE OR ALTER PROCEDURE sp_MarkComplianceCompleted
    @Payload NVARCHAR(MAX)
AS
BEGIN
    SET NOCOUNT ON;

    DECLARE @ComplianceId UNIQUEIDENTIFIER
    DECLARE @CompletedBy UNIQUEIDENTIFIER
    DECLARE @CompletionNotes NVARCHAR(MAX)
    DECLARE @CompletedAt DATETIME

    SELECT
        @ComplianceId = JSON_VALUE(@Payload, '$.ComplianceId'),
        @CompletedBy = JSON_VALUE(@Payload, '$.CompletedBy'),
        @CompletionNotes = JSON_VALUE(@Payload, '$.CompletionNotes'),
        @CompletedAt = ISNULL(JSON_VALUE(@Payload, '$.CompletedAt'), GETUTCDATE())

    UPDATE Compliance
    SET
        Status = 'Completed',
        CompletedBy = @CompletedBy,
        CompletedAt = @CompletedAt,
        Notes = CASE
            WHEN @CompletionNotes IS NOT NULL THEN
                CASE WHEN Notes IS NULL OR Notes = '' THEN @CompletionNotes
                     ELSE Notes + CHAR(13) + CHAR(10) + 'Completion: ' + @CompletionNotes
                END
            ELSE Notes
        END,
        UpdatedBy = @CompletedBy,
        UpdatedAt = GETUTCDATE()
    WHERE ComplianceId = @ComplianceId AND IsActive = 1

    SELECT @@ROWCOUNT AS RowsAffected
END

-- =============================================
-- Procedure: sp_UpdateComplianceStatus
-- Description: Update compliance status with notes
-- =============================================
CREATE OR ALTER PROCEDURE sp_UpdateComplianceStatus
    @Payload NVARCHAR(MAX)
AS
BEGIN
    SET NOCOUNT ON;

    DECLARE @ComplianceId UNIQUEIDENTIFIER
    DECLARE @Status NVARCHAR(50)
    DECLARE @UpdatedBy UNIQUEIDENTIFIER
    DECLARE @Notes NVARCHAR(MAX)
    DECLARE @UpdatedAt DATETIME

    SELECT
        @ComplianceId = JSON_VALUE(@Payload, '$.ComplianceId'),
        @Status = JSON_VALUE(@Payload, '$.Status'),
        @UpdatedBy = JSON_VALUE(@Payload, '$.UpdatedBy'),
        @Notes = JSON_VALUE(@Payload, '$.Notes'),
        @UpdatedAt = ISNULL(JSON_VALUE(@Payload, '$.UpdatedAt'), GETUTCDATE())

    UPDATE Compliance
    SET
        Status = @Status,
        Notes = CASE
            WHEN @Notes IS NOT NULL THEN
                CASE WHEN Notes IS NULL OR Notes = '' THEN @Notes
                     ELSE Notes + CHAR(13) + CHAR(10) + 'Status Update: ' + @Notes
                END
            ELSE Notes
        END,
        UpdatedBy = @UpdatedBy,
        UpdatedAt = @UpdatedAt,
        CompletedAt = CASE WHEN @Status = 'Completed' THEN @UpdatedAt ELSE CompletedAt END,
        CompletedBy = CASE WHEN @Status = 'Completed' THEN @UpdatedBy ELSE CompletedBy END
    WHERE ComplianceId = @ComplianceId AND IsActive = 1

    SELECT @@ROWCOUNT AS RowsAffected
END

-- =============================================
-- Procedure: sp_UpdateOverdueCompliance
-- Description: Automatically update overdue compliance items
-- =============================================
CREATE OR ALTER PROCEDURE sp_UpdateOverdueCompliance
AS
BEGIN
    SET NOCOUNT ON;

    UPDATE Compliance
    SET Status = 'Overdue'
    WHERE IsActive = 1
        AND Status NOT IN ('Completed', 'Cancelled')
        AND DueDate < GETUTCDATE()
        AND Status != 'Overdue'

    SELECT @@ROWCOUNT AS UpdatedItems
END
