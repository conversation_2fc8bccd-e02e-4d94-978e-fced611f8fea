-- =============================================
-- Complete Database Setup for Compliance Management System
-- =============================================

-- Create TenantRegistry Database (Master Database)
IF NOT EXISTS (SELECT name FROM sys.databases WHERE name = 'TenantRegistry')
BEGIN
    CREATE DATABASE [TenantRegistry];
    PRINT 'TenantRegistry database created.';
END
ELSE
BEGIN
    PRINT 'TenantRegistry database already exists.';
END

USE [TenantRegistry];

-- Create Organizations table for tenant management
IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='Organizations' AND xtype='U')
BEGIN
    CREATE TABLE [dbo].[Organizations] (
        [OrganizationId] UNIQUEIDENTIFIER PRIMARY KEY DEFAULT NEWID(),
        [TenantKey] NVARCHAR(100) NOT NULL UNIQUE,
        [CompanyName] NVARCHAR(200) NOT NULL,
        [DatabaseName] NVARCHAR(100) NOT NULL,
        [ConnectionString] NVARCHAR(500) NOT NULL,
        [IsActive] BIT NOT NULL DEFAULT 1,
        [CreatedAt] DATETIME NOT NULL DEFAULT GETUTCDATE(),
        [UpdatedAt] DATETIME NULL
    );
    
    -- Insert sample tenant
    INSERT INTO [Organizations] (TenantKey, CompanyName, DatabaseName, ConnectionString)
    VALUES ('kumar-associates', 'Kumar & Associates CA', 'KumarAssociatesDB', 
            'Server=localhost;Database=KumarAssociatesDB;Integrated Security=true;TrustServerCertificate=true;');
    
    PRINT 'Organizations table created with sample tenant.';
END

-- Create tenant database
IF NOT EXISTS (SELECT name FROM sys.databases WHERE name = 'KumarAssociatesDB')
BEGIN
    CREATE DATABASE [KumarAssociatesDB];
    PRINT 'KumarAssociatesDB database created.';
END

USE [KumarAssociatesDB];

-- Create Users table
IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='Users' AND xtype='U')
BEGIN
    CREATE TABLE [dbo].[Users] (
        [UserId] UNIQUEIDENTIFIER PRIMARY KEY DEFAULT NEWID(),
        [Email] NVARCHAR(255) NOT NULL UNIQUE,
        [FirstName] NVARCHAR(100) NOT NULL,
        [LastName] NVARCHAR(100) NOT NULL,
        [PasswordHash] NVARCHAR(255) NOT NULL,
        [Role] NVARCHAR(50) NOT NULL DEFAULT 'Staff',
        [IsActive] BIT NOT NULL DEFAULT 1,
        [CreatedAt] DATETIME NOT NULL DEFAULT GETUTCDATE(),
        [LastLoginAt] DATETIME NULL
    );
    
    -- Insert sample users
    INSERT INTO [Users] (Email, FirstName, LastName, PasswordHash, Role)
    VALUES 
        ('<EMAIL>', 'Admin', 'User', 'hashed_password_here', 'Admin'),
        ('<EMAIL>', 'Manager', 'User', 'hashed_password_here', 'Manager'),
        ('<EMAIL>', 'Staff', 'User', 'hashed_password_here', 'Staff');
    
    PRINT 'Users table created with sample users.';
END

-- Create Clients table
IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='Clients' AND xtype='U')
BEGIN
    CREATE TABLE [dbo].[Clients] (
        [ClientId] UNIQUEIDENTIFIER PRIMARY KEY DEFAULT NEWID(),
        [CompanyName] NVARCHAR(200) NOT NULL,
        [ContactPerson] NVARCHAR(100) NULL,
        [Email] NVARCHAR(255) NULL,
        [Phone] NVARCHAR(20) NULL,
        [Address] NVARCHAR(500) NULL,
        [IsActive] BIT NOT NULL DEFAULT 1,
        [CreatedAt] DATETIME NOT NULL DEFAULT GETUTCDATE()
    );
    
    -- Insert sample clients
    INSERT INTO [Clients] (CompanyName, ContactPerson, Email, Phone)
    VALUES 
        ('ABC Manufacturing Ltd', 'John Smith', '<EMAIL>', '+91-9876543210'),
        ('XYZ Services Pvt Ltd', 'Jane Doe', '<EMAIL>', '+91-9876543211'),
        ('Tech Solutions Inc', 'Bob Johnson', '<EMAIL>', '+91-9876543212');
    
    PRINT 'Clients table created with sample clients.';
END

-- Create Compliance table
IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='Compliance' AND xtype='U')
BEGIN
    CREATE TABLE [dbo].[Compliance] (
        [ComplianceId] UNIQUEIDENTIFIER PRIMARY KEY DEFAULT NEWID(),
        [ClientId] UNIQUEIDENTIFIER NOT NULL,
        [ComplianceType] NVARCHAR(100) NOT NULL,
        [SubType] NVARCHAR(100) NULL,
        [Description] NVARCHAR(MAX) NULL,
        [DueDate] DATETIME NOT NULL,
        [Status] NVARCHAR(50) NOT NULL DEFAULT 'Pending',
        [Priority] NVARCHAR(20) NOT NULL DEFAULT 'Medium',
        [AssignedTo] UNIQUEIDENTIFIER NULL,
        [CreatedAt] DATETIME NOT NULL DEFAULT GETUTCDATE(),
        [CreatedBy] UNIQUEIDENTIFIER NULL,
        [UpdatedAt] DATETIME NULL,
        [UpdatedBy] UNIQUEIDENTIFIER NULL,
        [CompletedAt] DATETIME NULL,
        [CompletedBy] UNIQUEIDENTIFIER NULL,
        [Notes] NVARCHAR(MAX) NULL,
        [ReminderSent] BIT NOT NULL DEFAULT 0,
        [LastReminderDate] DATETIME NULL,
        [IsActive] BIT NOT NULL DEFAULT 1,
        
        CONSTRAINT [FK_Compliance_Client] FOREIGN KEY ([ClientId]) REFERENCES [Clients]([ClientId]),
        CONSTRAINT [FK_Compliance_AssignedTo] FOREIGN KEY ([AssignedTo]) REFERENCES [Users]([UserId]),
        CONSTRAINT [FK_Compliance_CreatedBy] FOREIGN KEY ([CreatedBy]) REFERENCES [Users]([UserId])
    );
    
    -- Create indexes
    CREATE INDEX [IX_Compliance_ClientId] ON [Compliance]([ClientId]);
    CREATE INDEX [IX_Compliance_DueDate] ON [Compliance]([DueDate]);
    CREATE INDEX [IX_Compliance_Status] ON [Compliance]([Status]);
    
    PRINT 'Compliance table created with indexes.';
END

-- Insert sample compliance data
IF NOT EXISTS (SELECT 1 FROM [Compliance])
BEGIN
    DECLARE @ClientId1 UNIQUEIDENTIFIER = (SELECT TOP 1 ClientId FROM Clients WHERE CompanyName = 'ABC Manufacturing Ltd');
    DECLARE @ClientId2 UNIQUEIDENTIFIER = (SELECT TOP 1 ClientId FROM Clients WHERE CompanyName = 'XYZ Services Pvt Ltd');
    DECLARE @ClientId3 UNIQUEIDENTIFIER = (SELECT TOP 1 ClientId FROM Clients WHERE CompanyName = 'Tech Solutions Inc');
    DECLARE @UserId UNIQUEIDENTIFIER = (SELECT TOP 1 UserId FROM Users WHERE Role = 'Staff');
    
    INSERT INTO [Compliance] (
        [ClientId], [ComplianceType], [SubType], [Description], [DueDate], 
        [Status], [Priority], [AssignedTo], [CreatedBy], [Notes]
    )
    VALUES 
        (@ClientId1, 'Tax Filing', 'Annual Return', 'Annual income tax return filing for FY 2023-24', 
         DATEADD(DAY, 30, GETUTCDATE()), 'Pending', 'High', @UserId, @UserId, 
         'Client requires assistance with new tax regulations'),
        
        (@ClientId1, 'GST Return', 'GSTR-1', 'Monthly GST return filing for December 2024', 
         DATEADD(DAY, 15, GETUTCDATE()), 'In Progress', 'High', @UserId, @UserId, 
         'Documents received, processing in progress'),
        
        (@ClientId2, 'Audit', 'Internal Audit', 'Quarterly internal audit review', 
         DATEADD(DAY, 45, GETUTCDATE()), 'Pending', 'Medium', @UserId, @UserId, 
         'Scheduled for next month'),
        
        (@ClientId2, 'ROC Filing', 'Annual Filing', 'Annual return filing with ROC', 
         DATEADD(DAY, -5, GETUTCDATE()), 'Overdue', 'High', @UserId, @UserId, 
         'Urgent: Filing deadline passed, need to file immediately'),
        
        (@ClientId3, 'TDS Return', 'Quarterly Return', 'TDS return for Q3 FY 2023-24', 
         DATEADD(DAY, 20, GETUTCDATE()), 'Pending', 'Medium', @UserId, @UserId, 
         'Awaiting TDS certificates from client'),
        
        (@ClientId3, 'ESI Return', 'Monthly Return', 'ESI monthly return filing', 
         DATEADD(DAY, 10, GETUTCDATE()), 'Pending', 'Low', @UserId, @UserId, 
         'Regular monthly filing'),
        
        (@ClientId1, 'PF Return', 'Monthly Return', 'Provident Fund monthly return', 
         DATEADD(DAY, 8, GETUTCDATE()), 'Completed', 'Medium', @UserId, @UserId, 
         'Successfully filed on time'),
        
        (@ClientId2, 'Income Tax', 'Advance Tax', 'Quarterly advance tax payment', 
         DATEADD(DAY, 25, GETUTCDATE()), 'Pending', 'High', @UserId, @UserId, 
         'Calculate and file advance tax for Q4');
    
    PRINT 'Sample compliance data inserted successfully.';
END

-- Create a view for easy reporting
CREATE OR ALTER VIEW [vw_ComplianceReport] AS
SELECT 
    c.[ComplianceId],
    c.[ComplianceType],
    c.[SubType],
    c.[Description],
    c.[DueDate],
    c.[Status],
    c.[Priority],
    cl.[CompanyName] AS [ClientName],
    ISNULL(u.[FirstName] + ' ' + u.[LastName], 'Unassigned') AS [AssignedToName],
    c.[CreatedAt],
    c.[CompletedAt],
    c.[Notes],
    CASE 
        WHEN c.[Status] != 'Completed' AND c.[DueDate] < GETUTCDATE() THEN 1
        ELSE 0
    END AS [IsOverdue],
    DATEDIFF(DAY, GETUTCDATE(), c.[DueDate]) AS [DaysUntilDue]
FROM [Compliance] c
INNER JOIN [Clients] cl ON c.[ClientId] = cl.[ClientId]
LEFT JOIN [Users] u ON c.[AssignedTo] = u.[UserId]
WHERE c.[IsActive] = 1;

PRINT 'Compliance reporting view created.';

-- Summary
PRINT '';
PRINT '============================================================';
PRINT 'Database Setup Complete!';
PRINT '============================================================';
PRINT 'Databases Created:';
PRINT '  - TenantRegistry (Master database)';
PRINT '  - KumarAssociatesDB (Tenant database)';
PRINT '';
PRINT 'Tables Created:';
PRINT '  - Organizations (1 tenant)';
PRINT '  - Users (3 sample users)';
PRINT '  - Clients (3 sample clients)';
PRINT '  - Compliance (8 sample compliance items)';
PRINT '';
PRINT 'Sample Data Summary:';
SELECT 
    'Total Compliance Items' AS [Metric],
    COUNT(*) AS [Count]
FROM [Compliance]
UNION ALL
SELECT 
    'Pending Items',
    COUNT(*)
FROM [Compliance]
WHERE [Status] = 'Pending'
UNION ALL
SELECT 
    'Overdue Items',
    COUNT(*)
FROM [Compliance]
WHERE [Status] = 'Overdue'
UNION ALL
SELECT 
    'Completed Items',
    COUNT(*)
FROM [Compliance]
WHERE [Status] = 'Completed';

PRINT '============================================================';
