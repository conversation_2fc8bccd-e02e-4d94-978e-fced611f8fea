using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using CAPortalAPI.Models.DTOs;
using CAPortalAPI.Models.Common;
using CAPortalAPI.Services;
using CAPortalAPI.Authorization;
using Newtonsoft.Json;

namespace CAPortalAPI.Controllers
{
    [ApiController]
    [Route("api/[controller]")]
    [Authorize]
    public class ClientsController : ControllerBase
    {
        private readonly IDbService _dbService;
        private readonly ILogger<ClientsController> _logger;

        public ClientsController(IDbService dbService, ILogger<ClientsController> logger)
        {
            _dbService = dbService;
            _logger = logger;
        }

        [HttpGet]
        [AllowAnonymous] // Temporarily disable auth for testing
        public async Task<IActionResult> GetClients([FromQuery] ClientListRequestDto request)
        {
            try
            {
                var tenantKey = GetCurrentTenantKey();
                if (string.IsNullOrEmpty(tenantKey))
                {
                    return BadRequest(ApiResponse.ErrorResponse("Tenant context required"));
                }

                var payload = JsonConvert.SerializeObject(request);
                var connectionName = $"CA_Portal_{tenantKey.Replace("-", "_")}Connection";

                var result = await _dbService.ExecuteStoredProcedureAsync<ClientListResponseDto>(
                    "sp_GetClients", 
                    payload, 
                    connectionName);

                if (result == null)
                {
                    return Ok(ApiResponse<ClientListResponseDto>.SuccessResponse(new ClientListResponseDto()));
                }

                return Ok(ApiResponse<ClientListResponseDto>.SuccessResponse(result));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting clients");
                return StatusCode(500, ApiResponse.ErrorResponse("An error occurred while retrieving clients"));
            }
        }

        [HttpGet("{id}")]
        public async Task<IActionResult> GetClient(Guid id)
        {
            try
            {
                var tenantKey = GetCurrentTenantKey();
                if (string.IsNullOrEmpty(tenantKey))
                {
                    return BadRequest(ApiResponse.ErrorResponse("Tenant context required"));
                }

                var payload = JsonConvert.SerializeObject(new { ClientId = id });
                var connectionName = $"CA_Portal_{tenantKey.Replace("-", "_")}Connection";

                var client = await _dbService.ExecuteStoredProcedureAsync<ClientDto>(
                    "sp_GetClientById", 
                    payload, 
                    connectionName);

                if (client == null)
                {
                    return NotFound(ApiResponse.ErrorResponse("Client not found"));
                }

                return Ok(ApiResponse<ClientDto>.SuccessResponse(client));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting client {ClientId}", id);
                return StatusCode(500, ApiResponse.ErrorResponse("An error occurred while retrieving client"));
            }
        }

        [HttpPost]
        [Authorize(Policy = AuthorizationPolicies.ClientManagement)]
        public async Task<IActionResult> CreateClient([FromBody] CreateClientRequestDto request)
        {
            try
            {
                if (!ModelState.IsValid)
                {
                    return BadRequest(ApiResponse.ErrorResponse("Invalid request data",
                        ModelState.Values.SelectMany(v => v.Errors.Select(e => e.ErrorMessage)).ToList()));
                }

                var tenantKey = GetCurrentTenantKey();
                var currentUserId = GetCurrentUserId();
                
                if (string.IsNullOrEmpty(tenantKey) || currentUserId == null)
                {
                    return BadRequest(ApiResponse.ErrorResponse("Invalid context"));
                }

                var payload = JsonConvert.SerializeObject(new
                {
                    request.ClientCode,
                    request.CompanyName,
                    request.ContactPerson,
                    request.Email,
                    request.Phone,
                    request.Address,
                    request.City,
                    request.State,
                    request.PinCode,
                    request.GSTNumber,
                    request.PANNumber,
                    request.ClientType,
                    request.BusinessType,
                    request.AnnualTurnover,
                    CreatedBy = currentUserId
                });

                var connectionName = $"CA_Portal_{tenantKey.Replace("-", "_")}Connection";

                var result = await _dbService.ExecuteStoredProcedureAsync<ClientDto>(
                    "sp_CreateClient", 
                    payload, 
                    connectionName);

                if (result == null)
                {
                    return BadRequest(ApiResponse.ErrorResponse("Failed to create client"));
                }

                _logger.LogInformation("Client created successfully: {ClientCode} by {CreatedBy}", request.ClientCode, currentUserId);
                return CreatedAtAction(nameof(GetClient), new { id = result.ClientId }, 
                    ApiResponse<ClientDto>.SuccessResponse(result, "Client created successfully"));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error creating client {ClientCode}", request.ClientCode);
                return StatusCode(500, ApiResponse.ErrorResponse("An error occurred while creating client"));
            }
        }

        [HttpPut("{id}")]
        [Authorize(Policy = AuthorizationPolicies.ClientManagement)]
        public async Task<IActionResult> UpdateClient(Guid id, [FromBody] UpdateClientRequestDto request)
        {
            try
            {
                if (id != request.ClientId)
                {
                    return BadRequest(ApiResponse.ErrorResponse("Client ID mismatch"));
                }

                if (!ModelState.IsValid)
                {
                    return BadRequest(ApiResponse.ErrorResponse("Invalid request data",
                        ModelState.Values.SelectMany(v => v.Errors.Select(e => e.ErrorMessage)).ToList()));
                }

                var tenantKey = GetCurrentTenantKey();
                var currentUserId = GetCurrentUserId();
                
                if (string.IsNullOrEmpty(tenantKey) || currentUserId == null)
                {
                    return BadRequest(ApiResponse.ErrorResponse("Invalid context"));
                }

                var payload = JsonConvert.SerializeObject(new
                {
                    request.ClientId,
                    request.ClientCode,
                    request.CompanyName,
                    request.ContactPerson,
                    request.Email,
                    request.Phone,
                    request.Address,
                    request.City,
                    request.State,
                    request.PinCode,
                    request.GSTNumber,
                    request.PANNumber,
                    request.ClientType,
                    request.BusinessType,
                    request.AnnualTurnover,
                    request.IsActive,
                    UpdatedBy = currentUserId
                });

                var connectionName = $"CA_Portal_{tenantKey.Replace("-", "_")}Connection";

                var result = await _dbService.ExecuteStoredProcedureAsync<ClientDto>(
                    "sp_UpdateClient", 
                    payload, 
                    connectionName);

                if (result == null)
                {
                    return NotFound(ApiResponse.ErrorResponse("Client not found"));
                }

                _logger.LogInformation("Client updated successfully: {ClientId} by {UpdatedBy}", id, currentUserId);
                return Ok(ApiResponse<ClientDto>.SuccessResponse(result, "Client updated successfully"));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating client {ClientId}", id);
                return StatusCode(500, ApiResponse.ErrorResponse("An error occurred while updating client"));
            }
        }

        [HttpDelete("{id}")]
        [Authorize(Policy = AuthorizationPolicies.ManagerOrAdmin)]
        public async Task<IActionResult> DeleteClient(Guid id)
        {
            try
            {
                var tenantKey = GetCurrentTenantKey();
                var currentUserId = GetCurrentUserId();
                
                if (string.IsNullOrEmpty(tenantKey) || currentUserId == null)
                {
                    return BadRequest(ApiResponse.ErrorResponse("Invalid context"));
                }

                var payload = JsonConvert.SerializeObject(new
                {
                    ClientId = id,
                    DeletedBy = currentUserId
                });

                var connectionName = $"CA_Portal_{tenantKey.Replace("-", "_")}Connection";

                var rowsAffected = await _dbService.ExecuteStoredProcedureNonQueryAsync(
                    "sp_DeleteClient", 
                    payload, 
                    connectionName);

                if (rowsAffected == 0)
                {
                    return NotFound(ApiResponse.ErrorResponse("Client not found"));
                }

                _logger.LogInformation("Client deleted successfully: {ClientId} by {DeletedBy}", id, currentUserId);
                return Ok(ApiResponse.SuccessResponse("Client deleted successfully"));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error deleting client {ClientId}", id);
                return StatusCode(500, ApiResponse.ErrorResponse("An error occurred while deleting client"));
            }
        }

        [HttpPost("{id}/activate")]
        [Authorize(Policy = AuthorizationPolicies.ClientManagement)]
        public async Task<IActionResult> ActivateClient(Guid id)
        {
            try
            {
                var tenantKey = GetCurrentTenantKey();
                var currentUserId = GetCurrentUserId();
                
                if (string.IsNullOrEmpty(tenantKey) || currentUserId == null)
                {
                    return BadRequest(ApiResponse.ErrorResponse("Invalid context"));
                }

                var payload = JsonConvert.SerializeObject(new
                {
                    ClientId = id,
                    IsActive = true,
                    UpdatedBy = currentUserId
                });

                var connectionName = $"CA_Portal_{tenantKey.Replace("-", "_")}Connection";

                var rowsAffected = await _dbService.ExecuteStoredProcedureNonQueryAsync(
                    "sp_UpdateClientStatus", 
                    payload, 
                    connectionName);

                if (rowsAffected == 0)
                {
                    return NotFound(ApiResponse.ErrorResponse("Client not found"));
                }

                _logger.LogInformation("Client activated: {ClientId} by {UpdatedBy}", id, currentUserId);
                return Ok(ApiResponse.SuccessResponse("Client activated successfully"));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error activating client {ClientId}", id);
                return StatusCode(500, ApiResponse.ErrorResponse("An error occurred while activating client"));
            }
        }

        [HttpPost("{id}/deactivate")]
        [Authorize(Policy = AuthorizationPolicies.ClientManagement)]
        public async Task<IActionResult> DeactivateClient(Guid id)
        {
            try
            {
                var tenantKey = GetCurrentTenantKey();
                var currentUserId = GetCurrentUserId();
                
                if (string.IsNullOrEmpty(tenantKey) || currentUserId == null)
                {
                    return BadRequest(ApiResponse.ErrorResponse("Invalid context"));
                }

                var payload = JsonConvert.SerializeObject(new
                {
                    ClientId = id,
                    IsActive = false,
                    UpdatedBy = currentUserId
                });

                var connectionName = $"CA_Portal_{tenantKey.Replace("-", "_")}Connection";

                var rowsAffected = await _dbService.ExecuteStoredProcedureNonQueryAsync(
                    "sp_UpdateClientStatus", 
                    payload, 
                    connectionName);

                if (rowsAffected == 0)
                {
                    return NotFound(ApiResponse.ErrorResponse("Client not found"));
                }

                _logger.LogInformation("Client deactivated: {ClientId} by {UpdatedBy}", id, currentUserId);
                return Ok(ApiResponse.SuccessResponse("Client deactivated successfully"));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error deactivating client {ClientId}", id);
                return StatusCode(500, ApiResponse.ErrorResponse("An error occurred while deactivating client"));
            }
        }

        [HttpGet("search")]
        public async Task<IActionResult> SearchClients([FromQuery] string searchTerm, [FromQuery] int limit = 10)
        {
            try
            {
                var tenantKey = GetCurrentTenantKey();
                if (string.IsNullOrEmpty(tenantKey))
                {
                    return BadRequest(ApiResponse.ErrorResponse("Tenant context required"));
                }

                var payload = JsonConvert.SerializeObject(new
                {
                    SearchTerm = searchTerm,
                    Limit = limit
                });

                var connectionName = $"CA_Portal_{tenantKey.Replace("-", "_")}Connection";

                var clients = await _dbService.ExecuteStoredProcedureListAsync<ClientDto>(
                    "sp_SearchClients", 
                    payload, 
                    connectionName);

                return Ok(ApiResponse<List<ClientDto>>.SuccessResponse(clients));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error searching clients with term {SearchTerm}", searchTerm);
                return StatusCode(500, ApiResponse.ErrorResponse("An error occurred while searching clients"));
            }
        }

        private Guid? GetCurrentUserId()
        {
            var userIdClaim = User.FindFirst(System.Security.Claims.ClaimTypes.NameIdentifier)?.Value;
            return Guid.TryParse(userIdClaim, out var userId) ? userId : null;
        }

        private string? GetCurrentTenantKey()
        {
            return User.FindFirst("TenantKey")?.Value;
        }
    }
}
