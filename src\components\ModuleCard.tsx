
import { LucideIcon } from "lucide-react";

interface Module {
  id: number;
  title: string;
  description: string;
  icon: LucideIcon;
  status: string;
  timeSaved: string;
  color: string;
}

interface ModuleCardProps {
  module: Module;
  onClick: () => void;
}

export const ModuleCard = ({ module, onClick }: ModuleCardProps) => {
  const Icon = module.icon;

  return (
    <div
      onClick={onClick}
      className="bg-white rounded-lg shadow-sm border border-gray-200 p-6 hover:shadow-md transition-all duration-200 cursor-pointer group"
    >
      <div className="flex items-start space-x-3">
        <div className={`p-3 rounded-lg ${module.color} bg-opacity-10 group-hover:bg-opacity-20 transition-all duration-200`}>
          <Icon className={`h-6 w-6 ${module.color.replace('bg-', 'text-')}`} />
        </div>
        <div className="flex-1 min-w-0">
          <h3 className="text-lg font-semibold text-gray-900 mb-2 group-hover:text-blue-600 transition-colors duration-200">
            {module.title}
          </h3>
          <p className="text-sm text-gray-600 mb-3 line-clamp-2">
            {module.description}
          </p>
          <div className="flex items-center justify-between">
            <span className={`
              inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium
              ${module.status === 'active' 
                ? 'bg-green-100 text-green-800' 
                : 'bg-yellow-100 text-yellow-800'
              }
            `}>
              {module.status === 'active' ? '✓ Active' : '⚠ Coming Soon'}
            </span>
          </div>
          <div className="mt-3 p-3 bg-gray-50 rounded-md">
            <p className="text-xs text-gray-600 font-medium mb-1">Time Saved:</p>
            <p className="text-xs text-gray-500">{module.timeSaved}</p>
          </div>
        </div>
      </div>
    </div>
  );
};
