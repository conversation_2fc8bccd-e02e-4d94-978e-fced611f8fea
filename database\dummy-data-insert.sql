-- =====================================================
-- TenantRegistry Database - Dummy Data Insert Script
-- =====================================================

USE TenantRegistry;
GO

-- Clear existing data (for testing purposes)
DELETE FROM OrganizationDomains;
DELETE FROM Organizations;
DELETE FROM DatabaseTemplates;
GO

-- =====================================================
-- Insert Database Templates
-- =====================================================

INSERT INTO DatabaseTemplates (TemplateId, TemplateName, Version, SchemaScript, SeedDataScript, IsActive)
VALUES 
(
    NEWID(),
    'CA Portal Standard',
    '1.0.0',
    '-- Standard CA Portal Database Schema
    CREATE TABLE Users (
        UserId UNIQUEIDENTIFIER PRIMARY KEY DEFAULT NEWID(),
        Email NVARCHAR(255) UNIQUE NOT NULL,
        PasswordHash NVARCHAR(255) NOT NULL,
        FirstName NVARCHAR(100) NOT NULL,
        LastName NVARCHAR(100) NOT NULL,
        Role NVARCHAR(50) NOT NULL,
        IsActive BIT DEFAULT 1,
        CreatedAt DATETIME2 DEFAULT GETUTCDATE(),
        LastLoginAt DATETIME2,
        TenantId UNIQUEIDENTIFIER NOT NULL
    );
    
    CREATE TABLE Clients (
        ClientId UNIQUEIDENTIFIER PRIMARY KEY DEFAULT NEWID(),
        ClientCode NVARCHAR(50) UNIQUE NOT NULL,
        CompanyName NVARCHAR(255) NOT NULL,
        ContactPerson NVARCHAR(255),
        Email NVARCHAR(255),
        Phone NVARCHAR(20),
        Address NVARCHAR(500),
        GSTNumber NVARCHAR(15),
        PANNumber NVARCHAR(10),
        ClientType NVARCHAR(50),
        IsActive BIT DEFAULT 1,
        CreatedAt DATETIME2 DEFAULT GETUTCDATE(),
        CreatedBy UNIQUEIDENTIFIER
    );',
    '-- Default seed data for new tenants
    INSERT INTO Users (Email, PasswordHash, FirstName, LastName, Role, TenantId)
    VALUES (''<EMAIL>'', ''$2b$10$defaulthash'', ''Admin'', ''User'', ''Admin'', ''TENANT_ID_PLACEHOLDER'');',
    1
),
(
    NEWID(),
    'CA Portal Premium',
    '1.0.0',
    '-- Premium CA Portal Database Schema with additional features
    -- (Same as standard plus advanced modules)',
    '-- Premium seed data with additional features',
    1
);

-- =====================================================
-- Insert Organizations (Tenants)
-- =====================================================

DECLARE @Org1Id UNIQUEIDENTIFIER = NEWID();
DECLARE @Org2Id UNIQUEIDENTIFIER = NEWID();
DECLARE @Org3Id UNIQUEIDENTIFIER = NEWID();
DECLARE @Org4Id UNIQUEIDENTIFIER = NEWID();
DECLARE @Org5Id UNIQUEIDENTIFIER = NEWID();
DECLARE @Org6Id UNIQUEIDENTIFIER = NEWID();

INSERT INTO Organizations (
    OrganizationId, 
    TenantKey, 
    OrganizationName, 
    Domain, 
    DatabaseName, 
    ConnectionString, 
    IsActive, 
    CreatedAt, 
    UpdatedAt, 
    SubscriptionTier, 
    MaxUsers, 
    StorageLimit
)
VALUES 
-- Organization 1: Kumar & Associates
(
    @Org1Id,
    'kumar-associates',
    'Kumar & Associates Chartered Accountants',
    'kumar-ca.com',
    'CA_Portal_kumar_associates',
    'Server=localhost;Database=CA_Portal_kumar_associates;Integrated Security=true;TrustServerCertificate=true;',
    1,
    DATEADD(DAY, -90, GETUTCDATE()),
    GETUTCDATE(),
    'Premium',
    25,
    *********** -- 20GB
),

-- Organization 2: Sharma Financial Services
(
    @Org2Id,
    'sharma-financial',
    'Sharma Financial Services Pvt Ltd',
    'sharmafinancial.in',
    'CA_Portal_sharma_financial',
    'Server=localhost;Database=CA_Portal_sharma_financial;Integrated Security=true;TrustServerCertificate=true;',
    1,
    DATEADD(DAY, -60, GETUTCDATE()),
    GETUTCDATE(),
    'Standard',
    15,
    *********** -- 10GB
),

-- Organization 3: Metro Tax Consultants
(
    @Org3Id,
    'metro-tax',
    'Metro Tax Consultants',
    'metrotax.co.in',
    'CA_Portal_metro_tax',
    'Server=localhost;Database=CA_Portal_metro_tax;Integrated Security=true;TrustServerCertificate=true;',
    1,
    DATEADD(DAY, -45, GETUTCDATE()),
    GETUTCDATE(),
    'Basic',
    10,
    ********** -- 5GB
),

-- Organization 4: Global Audit Partners
(
    @Org4Id,
    'global-audit',
    'Global Audit Partners LLP',
    'globalaudit.com',
    'CA_Portal_global_audit',
    'Server=localhost;Database=CA_Portal_global_audit;Integrated Security=true;TrustServerCertificate=true;',
    1,
    DATEADD(DAY, -30, GETUTCDATE()),
    GETUTCDATE(),
    'Enterprise',
    50,
    **********0 -- 50GB
),

-- Organization 5: Patel & Co
(
    @Org5Id,
    'patel-co',
    'Patel & Co Chartered Accountants',
    'patelca.org',
    'CA_Portal_patel_co',
    'Server=localhost;Database=CA_Portal_patel_co;Integrated Security=true;TrustServerCertificate=true;',
    1,
    DATEADD(DAY, -15, GETUTCDATE()),
    GETUTCDATE(),
    'Standard',
    12,
    *********** -- 10GB
),

-- Organization 6: TechCA Solutions (Demo/Test Account)
(
    @Org6Id,
    'demo-techca',
    'TechCA Solutions - Demo Account',
    'demo.techca.com',
    'CA_Portal_demo_techca',
    'Server=localhost;Database=CA_Portal_demo_techca;Integrated Security=true;TrustServerCertificate=true;',
    1,
    DATEADD(DAY, -5, GETUTCDATE()),
    GETUTCDATE(),
    'Trial',
    5,
    ********** -- 2GB
);

-- =====================================================
-- Insert Additional Domains for Organizations
-- =====================================================

INSERT INTO OrganizationDomains (DomainId, OrganizationId, Domain, IsVerified, CreatedAt)
VALUES
-- Kumar & Associates additional domains
(NEWID(), @Org1Id, 'www.kumar-ca.com', 1, DATEADD(DAY, -85, GETUTCDATE())),
(NEWID(), @Org1Id, 'portal.kumar-ca.com', 1, DATEADD(DAY, -80, GETUTCDATE())),
(NEWID(), @Org1Id, 'kumarassociates.net', 0, DATEADD(DAY, -10, GETUTCDATE())),

-- Sharma Financial additional domains
(NEWID(), @Org2Id, 'www.sharmafinancial.in', 1, DATEADD(DAY, -55, GETUTCDATE())),
(NEWID(), @Org2Id, 'app.sharmafinancial.in', 1, DATEADD(DAY, -50, GETUTCDATE())),

-- Metro Tax additional domains
(NEWID(), @Org3Id, 'www.metrotax.co.in', 1, DATEADD(DAY, -40, GETUTCDATE())),
(NEWID(), @Org3Id, 'client.metrotax.co.in', 1, DATEADD(DAY, -35, GETUTCDATE())),

-- Global Audit additional domains
(NEWID(), @Org4Id, 'www.globalaudit.com', 1, DATEADD(DAY, -25, GETUTCDATE())),
(NEWID(), @Org4Id, 'portal.globalaudit.com', 1, DATEADD(DAY, -20, GETUTCDATE())),
(NEWID(), @Org4Id, 'api.globalaudit.com', 1, DATEADD(DAY, -15, GETUTCDATE())),
(NEWID(), @Org4Id, 'globalauditpartners.in', 0, DATEADD(DAY, -5, GETUTCDATE())),

-- Patel & Co additional domains
(NEWID(), @Org5Id, 'www.patelca.org', 1, DATEADD(DAY, -12, GETUTCDATE())),
(NEWID(), @Org5Id, 'patelconsultants.com', 0, DATEADD(DAY, -3, GETUTCDATE())),

-- Demo account additional domains
(NEWID(), @Org6Id, 'www.demo.techca.com', 1, DATEADD(DAY, -4, GETUTCDATE())),
(NEWID(), @Org6Id, 'test.techca.com', 1, DATEADD(DAY, -2, GETUTCDATE()));

-- =====================================================
-- Display inserted data for verification
-- =====================================================

PRINT '=====================================================';
PRINT 'INSERTED DATA SUMMARY';
PRINT '=====================================================';

PRINT 'Organizations:';
SELECT
    TenantKey,
    OrganizationName,
    Domain,
    SubscriptionTier,
    MaxUsers,
    CAST(StorageLimit / **********.0 AS DECIMAL(10,1)) AS StorageGB,
    FORMAT(CreatedAt, 'yyyy-MM-dd') AS CreatedDate
FROM Organizations
ORDER BY CreatedAt;

PRINT '';
PRINT 'Organization Domains:';
SELECT
    o.TenantKey,
    od.Domain,
    CASE WHEN od.IsVerified = 1 THEN 'Verified' ELSE 'Pending' END AS Status,
    FORMAT(od.CreatedAt, 'yyyy-MM-dd') AS CreatedDate
FROM OrganizationDomains od
JOIN Organizations o ON od.OrganizationId = o.OrganizationId
ORDER BY o.TenantKey, od.CreatedAt;

PRINT '';
PRINT 'Database Templates:';
SELECT
    TemplateName,
    Version,
    CASE WHEN IsActive = 1 THEN 'Active' ELSE 'Inactive' END AS Status
FROM DatabaseTemplates
ORDER BY TemplateName;

PRINT '';
PRINT '=====================================================';
PRINT 'TENANT ROUTING EXAMPLES:';
PRINT '=====================================================';
PRINT 'Subdomain routing examples:';
PRINT '- https://kumar-associates.yourapp.com';
PRINT '- https://sharma-financial.yourapp.com';
PRINT '- https://metro-tax.yourapp.com';
PRINT '';
PRINT 'Custom domain routing examples:';
PRINT '- https://kumar-ca.com';
PRINT '- https://sharmafinancial.in';
PRINT '- https://metrotax.co.in';
PRINT '';
PRINT 'API Header routing example:';
PRINT 'X-Tenant-ID: kumar-associates';
PRINT '=====================================================';

GO
