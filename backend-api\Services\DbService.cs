using System.Data;
using Microsoft.Data.SqlClient;
using System.Text;
using Newtonsoft.Json;

namespace CAPortalAPI.Services
{
    public class DbService : IDbService
    {
        private readonly IConfiguration _configuration;
        private readonly ILogger<DbService> _logger;

        public DbService(IConfiguration configuration, ILogger<DbService> logger)
        {
            _configuration = configuration;
            _logger = logger;
        }

        public async Task<string> ExecuteStoredProcedureAsync(string storedProcedureName, string jsonPayload, string connectionName)
        {
            try
            {
                var connectionString = _configuration.GetConnectionString(connectionName);
                if (string.IsNullOrEmpty(connectionString))
                {
                    throw new ArgumentException($"Connection string '{connectionName}' not found.");
                }

                using var connection = new SqlConnection(connectionString);
                using var command = new SqlCommand(storedProcedureName, connection)
                {
                    CommandType = CommandType.StoredProcedure,
                    CommandTimeout = 30
                };

                command.Parameters.AddWithValue("@JsonPayload", jsonPayload ?? "{}");

                await connection.OpenAsync();
                using var reader = await command.ExecuteReaderAsync();

                var result = new StringBuilder();
                while (await reader.ReadAsync())
                {
                    result.Append(reader.GetString(0));
                }

                return result.ToString();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error executing stored procedure {StoredProcedure} with payload {Payload}", 
                    storedProcedureName, jsonPayload);
                throw;
            }
        }

        public async Task<T?> ExecuteStoredProcedureAsync<T>(string storedProcedureName, string jsonPayload, string connectionName) where T : class
        {
            try
            {
                var jsonResult = await ExecuteStoredProcedureAsync(storedProcedureName, jsonPayload, connectionName);
                
                if (string.IsNullOrEmpty(jsonResult))
                    return null;

                return JsonConvert.DeserializeObject<T>(jsonResult);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error deserializing result from stored procedure {StoredProcedure}", storedProcedureName);
                throw;
            }
        }

        public async Task<List<T>> ExecuteStoredProcedureListAsync<T>(string storedProcedureName, string jsonPayload, string connectionName) where T : class
        {
            try
            {
                var jsonResult = await ExecuteStoredProcedureAsync(storedProcedureName, jsonPayload, connectionName);
                
                if (string.IsNullOrEmpty(jsonResult))
                    return new List<T>();

                var result = JsonConvert.DeserializeObject<List<T>>(jsonResult);
                return result ?? new List<T>();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error deserializing list result from stored procedure {StoredProcedure}", storedProcedureName);
                throw;
            }
        }

        public async Task<string> ExecuteStoredProcedureAsync(string storedProcedureName, string connectionName)
        {
            return await ExecuteStoredProcedureAsync(storedProcedureName, "{}", connectionName);
        }

        public async Task<int> ExecuteStoredProcedureNonQueryAsync(string storedProcedureName, string jsonPayload, string connectionName)
        {
            try
            {
                var connectionString = _configuration.GetConnectionString(connectionName);
                if (string.IsNullOrEmpty(connectionString))
                {
                    throw new ArgumentException($"Connection string '{connectionName}' not found.");
                }

                using var connection = new SqlConnection(connectionString);
                using var command = new SqlCommand(storedProcedureName, connection)
                {
                    CommandType = CommandType.StoredProcedure,
                    CommandTimeout = 30
                };

                command.Parameters.AddWithValue("@JsonPayload", jsonPayload ?? "{}");

                await connection.OpenAsync();
                return await command.ExecuteNonQueryAsync();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error executing non-query stored procedure {StoredProcedure} with payload {Payload}", 
                    storedProcedureName, jsonPayload);
                throw;
            }
        }

        public async Task<T?> ExecuteStoredProcedureScalarAsync<T>(string storedProcedureName, string jsonPayload, string connectionName)
        {
            try
            {
                var connectionString = _configuration.GetConnectionString(connectionName);
                if (string.IsNullOrEmpty(connectionString))
                {
                    throw new ArgumentException($"Connection string '{connectionName}' not found.");
                }

                using var connection = new SqlConnection(connectionString);
                using var command = new SqlCommand(storedProcedureName, connection)
                {
                    CommandType = CommandType.StoredProcedure,
                    CommandTimeout = 30
                };

                command.Parameters.AddWithValue("@JsonPayload", jsonPayload ?? "{}");

                await connection.OpenAsync();
                var result = await command.ExecuteScalarAsync();

                if (result == null || result == DBNull.Value)
                    return default(T);

                return (T)Convert.ChangeType(result, typeof(T));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error executing scalar stored procedure {StoredProcedure} with payload {Payload}", 
                    storedProcedureName, jsonPayload);
                throw;
            }
        }

        // Methods that accept connection string directly (for dynamic tenant connections)

        public async Task<T?> ExecuteStoredProcedureWithConnectionStringAsync<T>(string storedProcedureName, string jsonPayload, string connectionString) where T : class
        {
            try
            {
                var jsonResult = await ExecuteStoredProcedureWithConnectionStringAsync(storedProcedureName, jsonPayload, connectionString);

                if (string.IsNullOrEmpty(jsonResult))
                    return null;

                return JsonConvert.DeserializeObject<T>(jsonResult);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error deserializing result from stored procedure {StoredProcedure}", storedProcedureName);
                throw;
            }
        }

        public async Task<string> ExecuteStoredProcedureWithConnectionStringAsync(string storedProcedureName, string jsonPayload, string connectionString)
        {
            try
            {
                using var connection = new SqlConnection(connectionString);
                using var command = new SqlCommand(storedProcedureName, connection)
                {
                    CommandType = CommandType.StoredProcedure,
                    CommandTimeout = 30
                };

                command.Parameters.AddWithValue("@JsonPayload", jsonPayload ?? "{}");

                await connection.OpenAsync();
                using var reader = await command.ExecuteReaderAsync();

                var result = new StringBuilder();
                while (await reader.ReadAsync())
                {
                    result.Append(reader.GetString(0));
                }

                return result.ToString();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error executing stored procedure {StoredProcedure} with payload {Payload}",
                    storedProcedureName, jsonPayload);
                throw;
            }
        }

        public async Task<int> ExecuteStoredProcedureNonQueryWithConnectionStringAsync(string storedProcedureName, string jsonPayload, string connectionString)
        {
            try
            {
                using var connection = new SqlConnection(connectionString);
                using var command = new SqlCommand(storedProcedureName, connection)
                {
                    CommandType = CommandType.StoredProcedure,
                    CommandTimeout = 30
                };

                command.Parameters.AddWithValue("@JsonPayload", jsonPayload ?? "{}");

                await connection.OpenAsync();
                return await command.ExecuteNonQueryAsync();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error executing non-query stored procedure {StoredProcedure} with payload {Payload}",
                    storedProcedureName, jsonPayload);
                throw;
            }
        }

        public async Task<List<T>> ExecuteStoredProcedureListWithConnectionStringAsync<T>(string storedProcedureName, string jsonPayload, string connectionString) where T : class
        {
            try
            {
                var jsonResult = await ExecuteStoredProcedureWithConnectionStringAsync(storedProcedureName, jsonPayload, connectionString);

                if (string.IsNullOrEmpty(jsonResult))
                    return new List<T>();

                var result = JsonConvert.DeserializeObject<List<T>>(jsonResult);
                return result ?? new List<T>();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error deserializing list result from stored procedure {StoredProcedure}", storedProcedureName);
                throw;
            }
        }
    }
}
