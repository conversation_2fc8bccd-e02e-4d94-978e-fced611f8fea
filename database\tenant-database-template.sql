-- =====================================================
-- Tenant Database Template with Sample Data
-- =====================================================
-- This script creates a complete tenant database with sample data
-- Replace {TENANT_KEY} and {TENANT_ID} with actual values

-- Create the tenant database
-- CREATE DATABASE CA_Portal_{TENANT_KEY};
-- GO

-- USE CA_Portal_{TENANT_KEY};
-- GO

-- =====================================================
-- Create Tables
-- =====================================================

-- Users table
CREATE TABLE Users (
    UserId UNIQUEIDENTIFIER PRIMARY KEY DEFAULT NEWID(),
    Email NVARCHAR(255) UNIQUE NOT NULL,
    PasswordHash NVARCHAR(255) NOT NULL,
    FirstName NVARCHAR(100) NOT NULL,
    LastName NVARCHAR(100) NOT NULL,
    Role NVARCHAR(50) NOT NULL, -- Admin, Manager, Senior Associate, Junior Associate
    IsActive BIT DEFAULT 1,
    CreatedAt DATETIME2 DEFAULT GETUTCDATE(),
    LastLoginAt DATETIME2,
    TenantId UNIQUEIDENTIFIER NOT NULL,
    Phone NVARCHAR(20),
    Department NVARCHAR(100),
    EmployeeCode NVARCHAR(20)
);

-- Clients table
CREATE TABLE Clients (
    ClientId UNIQUEIDENTIFIER PRIMARY KEY DEFAULT NEWID(),
    ClientCode NVARCHAR(50) UNIQUE NOT NULL,
    CompanyName NVARCHAR(255) NOT NULL,
    ContactPerson NVARCHAR(255),
    Email NVARCHAR(255),
    Phone NVARCHAR(20),
    Address NVARCHAR(500),
    City NVARCHAR(100),
    State NVARCHAR(100),
    PinCode NVARCHAR(10),
    GSTNumber NVARCHAR(15),
    PANNumber NVARCHAR(10),
    ClientType NVARCHAR(50), -- Individual, Company, Partnership, LLP, Trust
    BusinessType NVARCHAR(100),
    AnnualTurnover DECIMAL(18,2),
    IsActive BIT DEFAULT 1,
    CreatedAt DATETIME2 DEFAULT GETUTCDATE(),
    CreatedBy UNIQUEIDENTIFIER REFERENCES Users(UserId),
    UpdatedAt DATETIME2 DEFAULT GETUTCDATE(),
    UpdatedBy UNIQUEIDENTIFIER REFERENCES Users(UserId)
);

-- Compliance Items table
CREATE TABLE ComplianceItems (
    ComplianceId UNIQUEIDENTIFIER PRIMARY KEY DEFAULT NEWID(),
    ClientId UNIQUEIDENTIFIER REFERENCES Clients(ClientId),
    ComplianceType NVARCHAR(100) NOT NULL, -- GST, TDS, ITR, ROC, ESI, PF, etc.
    SubType NVARCHAR(100), -- GSTR-1, GSTR-3B, ITR-1, ITR-4, etc.
    DueDate DATE NOT NULL,
    Description NVARCHAR(500),
    Status NVARCHAR(50) DEFAULT 'Pending', -- Pending, InProgress, Completed, Overdue
    Priority NVARCHAR(20) DEFAULT 'Medium', -- Low, Medium, High, Critical
    AssignedTo UNIQUEIDENTIFIER REFERENCES Users(UserId),
    CreatedAt DATETIME2 DEFAULT GETUTCDATE(),
    CompletedAt DATETIME2,
    Notes NVARCHAR(1000),
    ReminderSent BIT DEFAULT 0,
    LastReminderDate DATETIME2
);

-- Documents table
CREATE TABLE Documents (
    DocumentId UNIQUEIDENTIFIER PRIMARY KEY DEFAULT NEWID(),
    ClientId UNIQUEIDENTIFIER REFERENCES Clients(ClientId),
    FileName NVARCHAR(255) NOT NULL,
    OriginalFileName NVARCHAR(255) NOT NULL,
    FileSize BIGINT NOT NULL,
    FileType NVARCHAR(50),
    StoragePath NVARCHAR(500) NOT NULL,
    Category NVARCHAR(100), -- Invoice, Return, Certificate, Agreement, etc.
    SubCategory NVARCHAR(100),
    UploadedBy UNIQUEIDENTIFIER REFERENCES Users(UserId),
    UploadedAt DATETIME2 DEFAULT GETUTCDATE(),
    IsDeleted BIT DEFAULT 0,
    DeletedAt DATETIME2,
    DeletedBy UNIQUEIDENTIFIER,
    Tags NVARCHAR(500),
    Description NVARCHAR(500)
);

-- Communication Logs table
CREATE TABLE CommunicationLogs (
    LogId UNIQUEIDENTIFIER PRIMARY KEY DEFAULT NEWID(),
    ClientId UNIQUEIDENTIFIER REFERENCES Clients(ClientId),
    CommunicationType NVARCHAR(50), -- Email, WhatsApp, SMS, Call, Meeting
    Subject NVARCHAR(255),
    Content NVARCHAR(MAX),
    SentBy UNIQUEIDENTIFIER REFERENCES Users(UserId),
    SentAt DATETIME2 DEFAULT GETUTCDATE(),
    Status NVARCHAR(50) DEFAULT 'Sent', -- Sent, Failed, Pending, Delivered, Read
    RecipientEmail NVARCHAR(255),
    RecipientPhone NVARCHAR(20),
    TemplateUsed NVARCHAR(100),
    ResponseReceived BIT DEFAULT 0,
    ResponseDate DATETIME2,
    ResponseContent NVARCHAR(MAX)
);

-- Tasks table
CREATE TABLE Tasks (
    TaskId UNIQUEIDENTIFIER PRIMARY KEY DEFAULT NEWID(),
    ClientId UNIQUEIDENTIFIER REFERENCES Clients(ClientId),
    Title NVARCHAR(255) NOT NULL,
    Description NVARCHAR(1000),
    Priority NVARCHAR(20) DEFAULT 'Medium', -- Low, Medium, High, Critical
    Status NVARCHAR(50) DEFAULT 'Open', -- Open, InProgress, Completed, Cancelled, OnHold
    AssignedTo UNIQUEIDENTIFIER REFERENCES Users(UserId),
    CreatedBy UNIQUEIDENTIFIER REFERENCES Users(UserId),
    DueDate DATETIME2,
    CreatedAt DATETIME2 DEFAULT GETUTCDATE(),
    UpdatedAt DATETIME2 DEFAULT GETUTCDATE(),
    CompletedAt DATETIME2,
    EstimatedHours DECIMAL(5,2),
    ActualHours DECIMAL(5,2),
    Category NVARCHAR(100), -- Compliance, Advisory, Audit, Tax Planning, etc.
    Tags NVARCHAR(500)
);

-- Reports table
CREATE TABLE Reports (
    ReportId UNIQUEIDENTIFIER PRIMARY KEY DEFAULT NEWID(),
    ClientId UNIQUEIDENTIFIER REFERENCES Clients(ClientId),
    ReportType NVARCHAR(100) NOT NULL, -- Financial, Tax, Compliance, KPI, etc.
    ReportName NVARCHAR(255) NOT NULL,
    GeneratedBy UNIQUEIDENTIFIER REFERENCES Users(UserId),
    GeneratedAt DATETIME2 DEFAULT GETUTCDATE(),
    ReportPeriod NVARCHAR(100), -- Monthly, Quarterly, Yearly, Custom
    PeriodStart DATE,
    PeriodEnd DATE,
    FilePath NVARCHAR(500),
    Status NVARCHAR(50) DEFAULT 'Generated', -- Generated, Sent, Reviewed
    Notes NVARCHAR(1000)
);

-- Communication Templates table
CREATE TABLE CommunicationTemplates (
    TemplateId UNIQUEIDENTIFIER PRIMARY KEY DEFAULT NEWID(),
    TemplateName NVARCHAR(255) NOT NULL,
    TemplateType NVARCHAR(50) NOT NULL, -- Email, SMS, WhatsApp
    Subject NVARCHAR(255),
    Content NVARCHAR(MAX) NOT NULL,
    Category NVARCHAR(100), -- Reminder, Welcome, Invoice, etc.
    IsActive BIT DEFAULT 1,
    CreatedBy UNIQUEIDENTIFIER REFERENCES Users(UserId),
    CreatedAt DATETIME2 DEFAULT GETUTCDATE(),
    UpdatedAt DATETIME2 DEFAULT GETUTCDATE(),
    UsageCount INT DEFAULT 0
);

-- =====================================================
-- Create Indexes for Performance
-- =====================================================

-- Users indexes
CREATE INDEX IX_Users_Email ON Users(Email);
CREATE INDEX IX_Users_TenantId ON Users(TenantId);
CREATE INDEX IX_Users_Role ON Users(Role);

-- Clients indexes
CREATE INDEX IX_Clients_ClientCode ON Clients(ClientCode);
CREATE INDEX IX_Clients_Email ON Clients(Email);
CREATE INDEX IX_Clients_GSTNumber ON Clients(GSTNumber);
CREATE INDEX IX_Clients_PANNumber ON Clients(PANNumber);
CREATE INDEX IX_Clients_CreatedBy ON Clients(CreatedBy);

-- Compliance indexes
CREATE INDEX IX_ComplianceItems_ClientId ON ComplianceItems(ClientId);
CREATE INDEX IX_ComplianceItems_DueDate ON ComplianceItems(DueDate);
CREATE INDEX IX_ComplianceItems_Status ON ComplianceItems(Status);
CREATE INDEX IX_ComplianceItems_AssignedTo ON ComplianceItems(AssignedTo);
CREATE INDEX IX_ComplianceItems_ComplianceType ON ComplianceItems(ComplianceType);

-- Documents indexes
CREATE INDEX IX_Documents_ClientId ON Documents(ClientId);
CREATE INDEX IX_Documents_Category ON Documents(Category);
CREATE INDEX IX_Documents_UploadedBy ON Documents(UploadedBy);
CREATE INDEX IX_Documents_UploadedAt ON Documents(UploadedAt);

-- Tasks indexes
CREATE INDEX IX_Tasks_ClientId ON Tasks(ClientId);
CREATE INDEX IX_Tasks_AssignedTo ON Tasks(AssignedTo);
CREATE INDEX IX_Tasks_Status ON Tasks(Status);
CREATE INDEX IX_Tasks_DueDate ON Tasks(DueDate);
CREATE INDEX IX_Tasks_CreatedBy ON Tasks(CreatedBy);

-- Communication logs indexes
CREATE INDEX IX_CommunicationLogs_ClientId ON CommunicationLogs(ClientId);
CREATE INDEX IX_CommunicationLogs_SentBy ON CommunicationLogs(SentBy);
CREATE INDEX IX_CommunicationLogs_SentAt ON CommunicationLogs(SentAt);
CREATE INDEX IX_CommunicationLogs_CommunicationType ON CommunicationLogs(CommunicationType);

GO
