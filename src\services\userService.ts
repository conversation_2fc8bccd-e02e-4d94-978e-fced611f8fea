// User Service
import { apiClient, User, PaginatedResponse } from './api';

export interface CreateUserRequest {
  email: string;
  firstName: string;
  lastName: string;
  role: string;
  phone?: string;
  department?: string;
  employeeCode?: string;
  password: string;
}

export interface UpdateUserRequest {
  userId: string;
  email: string;
  firstName: string;
  lastName: string;
  role: string;
  isActive: boolean;
  phone?: string;
  department?: string;
  employeeCode?: string;
}

export interface UserListRequest {
  pageNumber?: number;
  pageSize?: number;
  searchTerm?: string;
  role?: string;
  isActive?: boolean;
  department?: string;
}

export interface UserListResponse {
  users: User[];
  totalCount: number;
  pageNumber: number;
  pageSize: number;
  totalPages: number;
}

class UserService {
  /**
   * Get paginated list of users
   */
  async getUsers(request: UserListRequest = {}): Promise<UserListResponse> {
    try {
      const params = new URLSearchParams();
      
      if (request.pageNumber) params.append('pageNumber', request.pageNumber.toString());
      if (request.pageSize) params.append('pageSize', request.pageSize.toString());
      if (request.searchTerm) params.append('searchTerm', request.searchTerm);
      if (request.role) params.append('role', request.role);
      if (request.isActive !== undefined) params.append('isActive', request.isActive.toString());
      if (request.department) params.append('department', request.department);

      const response = await apiClient.get<UserListResponse>(`/users?${params.toString()}`);
      
      if (response.success && response.data) {
        return response.data;
      } else {
        throw new Error(response.message || 'Failed to fetch users');
      }
    } catch (error: any) {
      console.error('Get users error:', error);
      throw new Error(error.response?.data?.message || 'Failed to fetch users');
    }
  }

  /**
   * Get user by ID
   */
  async getUserById(userId: string): Promise<User> {
    try {
      const response = await apiClient.get<User>(`/users/${userId}`);
      
      if (response.success && response.data) {
        return response.data;
      } else {
        throw new Error(response.message || 'Failed to fetch user');
      }
    } catch (error: any) {
      console.error('Get user by ID error:', error);
      throw new Error(error.response?.data?.message || 'Failed to fetch user');
    }
  }

  /**
   * Create new user
   */
  async createUser(request: CreateUserRequest): Promise<User> {
    try {
      const response = await apiClient.post<User>('/users', request);
      
      if (response.success && response.data) {
        return response.data;
      } else {
        throw new Error(response.message || 'Failed to create user');
      }
    } catch (error: any) {
      console.error('Create user error:', error);
      throw new Error(error.response?.data?.message || 'Failed to create user');
    }
  }

  /**
   * Update user
   */
  async updateUser(request: UpdateUserRequest): Promise<User> {
    try {
      const response = await apiClient.put<User>(`/users/${request.userId}`, request);
      
      if (response.success && response.data) {
        return response.data;
      } else {
        throw new Error(response.message || 'Failed to update user');
      }
    } catch (error: any) {
      console.error('Update user error:', error);
      throw new Error(error.response?.data?.message || 'Failed to update user');
    }
  }

  /**
   * Delete user
   */
  async deleteUser(userId: string): Promise<void> {
    try {
      const response = await apiClient.delete(`/users/${userId}`);
      
      if (!response.success) {
        throw new Error(response.message || 'Failed to delete user');
      }
    } catch (error: any) {
      console.error('Delete user error:', error);
      throw new Error(error.response?.data?.message || 'Failed to delete user');
    }
  }

  /**
   * Activate user
   */
  async activateUser(userId: string): Promise<void> {
    try {
      const response = await apiClient.post(`/users/${userId}/activate`);
      
      if (!response.success) {
        throw new Error(response.message || 'Failed to activate user');
      }
    } catch (error: any) {
      console.error('Activate user error:', error);
      throw new Error(error.response?.data?.message || 'Failed to activate user');
    }
  }

  /**
   * Deactivate user
   */
  async deactivateUser(userId: string): Promise<void> {
    try {
      const response = await apiClient.post(`/users/${userId}/deactivate`);
      
      if (!response.success) {
        throw new Error(response.message || 'Failed to deactivate user');
      }
    } catch (error: any) {
      console.error('Deactivate user error:', error);
      throw new Error(error.response?.data?.message || 'Failed to deactivate user');
    }
  }

  /**
   * Get available roles
   */
  getRoles(): string[] {
    return ['Admin', 'Manager', 'Senior Associate', 'Junior Associate', 'Intern'];
  }

  /**
   * Get available departments
   */
  getDepartments(): string[] {
    return [
      'Audit & Assurance',
      'Tax & Compliance',
      'Advisory Services',
      'Accounting',
      'Administration',
      'IT Support'
    ];
  }

  /**
   * Search users
   */
  async searchUsers(searchTerm: string, limit: number = 10): Promise<User[]> {
    try {
      const response = await apiClient.get<User[]>(`/users/search?searchTerm=${encodeURIComponent(searchTerm)}&limit=${limit}`);
      
      if (response.success && response.data) {
        return response.data;
      } else {
        throw new Error(response.message || 'Failed to search users');
      }
    } catch (error: any) {
      console.error('Search users error:', error);
      throw new Error(error.response?.data?.message || 'Failed to search users');
    }
  }

  /**
   * Get user statistics
   */
  async getUserStats(): Promise<{
    totalUsers: number;
    activeUsers: number;
    inactiveUsers: number;
    usersByRole: { role: string; count: number }[];
    usersByDepartment: { department: string; count: number }[];
  }> {
    try {
      // This would be a separate endpoint in a real application
      const allUsers = await this.getUsers({ pageSize: 1000 });
      
      const totalUsers = allUsers.totalCount;
      const activeUsers = allUsers.users.filter(u => u.isActive).length;
      const inactiveUsers = totalUsers - activeUsers;
      
      // Group by role
      const roleGroups = allUsers.users.reduce((acc, user) => {
        acc[user.role] = (acc[user.role] || 0) + 1;
        return acc;
      }, {} as Record<string, number>);
      
      const usersByRole = Object.entries(roleGroups).map(([role, count]) => ({
        role,
        count
      }));
      
      // Group by department
      const deptGroups = allUsers.users.reduce((acc, user) => {
        const dept = user.department || 'Unassigned';
        acc[dept] = (acc[dept] || 0) + 1;
        return acc;
      }, {} as Record<string, number>);
      
      const usersByDepartment = Object.entries(deptGroups).map(([department, count]) => ({
        department,
        count
      }));
      
      return {
        totalUsers,
        activeUsers,
        inactiveUsers,
        usersByRole,
        usersByDepartment
      };
    } catch (error: any) {
      console.error('Get user stats error:', error);
      throw new Error('Failed to fetch user statistics');
    }
  }
}

// Export singleton instance
export const userService = new UserService();
export default userService;
