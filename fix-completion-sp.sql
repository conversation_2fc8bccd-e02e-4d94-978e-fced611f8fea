-- Fix sp_MarkComplianceCompleted to handle already completed items
USE [CA_Portal_kumar_associates];
GO

-- Drop if exists
IF EXISTS (SELECT * FROM sys.procedures WHERE name = 'sp_MarkComplianceCompleted')
    DROP PROCEDURE sp_MarkComplianceCompleted;
GO

-- <PERSON><PERSON> improved sp_MarkComplianceCompleted
CREATE PROCEDURE sp_MarkComplianceCompleted
    @Payload NVARCHAR(MAX)
AS
BEGIN
    SET NOCOUNT ON;
    
    DECLARE @ComplianceId UNIQUEIDENTIFIER
    DECLARE @CompletedBy UNIQUEIDENTIFIER
    DECLARE @CompletionNotes NVARCHAR(MAX)
    DECLARE @CompletedAt DATETIME
    DECLARE @ItemExists BIT = 0
    DECLARE @AlreadyCompleted BIT = 0
    
    -- Parse JSON payload
    SELECT 
        @ComplianceId = TRY_CAST(JSON_VALUE(@Payload, '$.ComplianceId') AS UNIQUEIDENTIFIER),
        @CompletedBy = TRY_CAST(JSON_VALUE(@Payload, '$.CompletedBy') AS UNIQUEIDENTIFIER),
        @CompletionNotes = JSON_VALUE(@Payload, '$.CompletionNotes'),
        @CompletedAt = TRY_CAST(JSON_VALUE(@Payload, '$.CompletedAt') AS DATETIME)
    
    -- Validate required fields
    IF @ComplianceId IS NULL
    BEGIN
        RAISERROR('ComplianceId is required', 16, 1)
        RETURN
    END
    
    -- Set default completed date if not provided
    IF @CompletedAt IS NULL
        SET @CompletedAt = GETUTCDATE()
    
    -- Check if the compliance item exists and its current status
    SELECT 
        @ItemExists = 1,
        @AlreadyCompleted = CASE WHEN Status = 'Completed' THEN 1 ELSE 0 END
    FROM ComplianceItems 
    WHERE ComplianceId = @ComplianceId
    
    -- If item doesn't exist, return error
    IF @ItemExists = 0
    BEGIN
        RAISERROR('Compliance item not found', 16, 1)
        RETURN
    END
    
    -- If already completed, just update notes if provided
    IF @AlreadyCompleted = 1
    BEGIN
        IF @CompletionNotes IS NOT NULL AND @CompletionNotes != ''
        BEGIN
            UPDATE ComplianceItems
            SET Notes = ISNULL(Notes, '') + CASE WHEN ISNULL(Notes, '') = '' THEN '' ELSE CHAR(13) + CHAR(10) END + 'Additional Notes: ' + @CompletionNotes
            WHERE ComplianceId = @ComplianceId
        END
        
        -- Return 1 to indicate success (item was already completed)
        SELECT 1 AS RowsAffected, 'Already completed' AS Message
        FOR JSON PATH, WITHOUT_ARRAY_WRAPPER
        RETURN
    END
    
    -- Update the compliance item to mark as completed
    UPDATE ComplianceItems
    SET 
        Status = 'Completed',
        CompletedAt = @CompletedAt,
        Notes = CASE 
            WHEN @CompletionNotes IS NOT NULL AND @CompletionNotes != '' 
            THEN ISNULL(Notes, '') + CASE WHEN ISNULL(Notes, '') = '' THEN '' ELSE CHAR(13) + CHAR(10) END + 'Completion Notes: ' + @CompletionNotes
            ELSE Notes
        END
    WHERE ComplianceId = @ComplianceId
    
    -- Return the result as JSON
    SELECT
        @@ROWCOUNT AS RowsAffected,
        'Marked as completed' AS Message
    FOR JSON PATH, WITHOUT_ARRAY_WRAPPER
END
GO

PRINT '✅ sp_MarkComplianceCompleted updated successfully!';
PRINT 'Now handles already completed items gracefully';
