// Environment Configuration Utility
// This handles environment variables across different React setups (Vite, CRA, etc.)

interface EnvironmentConfig {
  API_URL: string;
  TENANT_KEY: string;
  TENANT_NAME: string;
  APP_NAME: string;
  APP_VERSION: string;
  ENVIRONMENT: string;
  ENABLE_NOTIFICATIONS: boolean;
  ENABLE_ANALYTICS: boolean;
  ENABLE_DEBUG: boolean;
  MAX_FILE_SIZE: number;
  ALLOWED_FILE_TYPES: string;
  DEFAULT_PAGE_SIZE: number;
  MAX_PAGE_SIZE: number;
}

// Helper function to get environment variable from multiple sources
const getEnvVar = (key: string, defaultValue: string = ''): string => {
  // Try different sources for environment variables

  // 1. Vite environment variables (import.meta.env) - try both VITE_ prefixed and regular
  if (typeof import.meta !== 'undefined' && import.meta.env) {
    // Try VITE_ prefixed version first
    const viteKey = key.startsWith('VITE_') ? key : `VITE_${key}`;
    const viteValue = import.meta.env[viteKey];
    if (viteValue !== undefined) return viteValue;

    // Try regular key
    const regularValue = import.meta.env[key];
    if (regularValue !== undefined) return regularValue;
  }

  // 2. Create React App environment variables (process.env)
  if (typeof process !== 'undefined' && process.env) {
    const craValue = process.env[key];
    if (craValue !== undefined) return craValue;
  }

  // 3. Window object (for runtime configuration)
  if (typeof window !== 'undefined' && (window as any).env) {
    const windowValue = (window as any).env[key];
    if (windowValue !== undefined) return windowValue;
  }

  // 4. Global process object (fallback)
  if (typeof globalThis !== 'undefined' && (globalThis as any).process?.env) {
    const globalValue = (globalThis as any).process.env[key];
    if (globalValue !== undefined) return globalValue;
  }

  return defaultValue;
};

// Helper function to get boolean environment variable
const getEnvBool = (key: string, defaultValue: boolean = false): boolean => {
  const value = getEnvVar(key, defaultValue.toString()).toLowerCase();
  return value === 'true' || value === '1' || value === 'yes';
};

// Helper function to get number environment variable
const getEnvNumber = (key: string, defaultValue: number = 0): number => {
  const value = getEnvVar(key, defaultValue.toString());
  const parsed = parseInt(value, 10);
  return isNaN(parsed) ? defaultValue : parsed;
};

// Environment configuration object
export const env: EnvironmentConfig = {
  // API Configuration
  API_URL: getEnvVar('REACT_APP_API_URL', 'https://localhost:7000/api'),
  
  // Tenant Configuration
  TENANT_KEY: getEnvVar('REACT_APP_TENANT_KEY', 'kumar-associates'),
  TENANT_NAME: getEnvVar('REACT_APP_TENANT_NAME', 'Kumar & Associates'),
  
  // Application Configuration
  APP_NAME: getEnvVar('REACT_APP_NAME', 'CA Portal'),
  APP_VERSION: getEnvVar('REACT_APP_VERSION', '1.0.0'),
  ENVIRONMENT: getEnvVar('REACT_APP_ENVIRONMENT', 'development'),
  
  // Feature Flags
  ENABLE_NOTIFICATIONS: getEnvBool('REACT_APP_ENABLE_NOTIFICATIONS', true),
  ENABLE_ANALYTICS: getEnvBool('REACT_APP_ENABLE_ANALYTICS', false),
  ENABLE_DEBUG: getEnvBool('REACT_APP_ENABLE_DEBUG', true),
  
  // File Upload Configuration
  MAX_FILE_SIZE: getEnvNumber('REACT_APP_MAX_FILE_SIZE', 10485760), // 10MB
  ALLOWED_FILE_TYPES: getEnvVar('REACT_APP_ALLOWED_FILE_TYPES', '.pdf,.doc,.docx,.xls,.xlsx,.jpg,.jpeg,.png'),
  
  // Pagination Configuration
  DEFAULT_PAGE_SIZE: getEnvNumber('REACT_APP_DEFAULT_PAGE_SIZE', 20),
  MAX_PAGE_SIZE: getEnvNumber('REACT_APP_MAX_PAGE_SIZE', 100),
};

// Development helpers
export const isDevelopment = env.ENVIRONMENT === 'development';
export const isProduction = env.ENVIRONMENT === 'production';
export const isDebugEnabled = env.ENABLE_DEBUG && isDevelopment;

// Validation function
export const validateEnvironment = (): { isValid: boolean; errors: string[] } => {
  const errors: string[] = [];
  
  // Required environment variables
  if (!env.API_URL) {
    errors.push('REACT_APP_API_URL is required');
  }
  
  if (!env.TENANT_KEY) {
    errors.push('REACT_APP_TENANT_KEY is required');
  }
  
  // Validate API URL format
  try {
    new URL(env.API_URL);
  } catch {
    errors.push('REACT_APP_API_URL must be a valid URL');
  }
  
  // Validate file size
  if (env.MAX_FILE_SIZE <= 0) {
    errors.push('REACT_APP_MAX_FILE_SIZE must be a positive number');
  }
  
  // Validate page sizes
  if (env.DEFAULT_PAGE_SIZE <= 0 || env.DEFAULT_PAGE_SIZE > env.MAX_PAGE_SIZE) {
    errors.push('REACT_APP_DEFAULT_PAGE_SIZE must be between 1 and MAX_PAGE_SIZE');
  }
  
  return {
    isValid: errors.length === 0,
    errors
  };
};

// Log environment configuration in development
if (isDevelopment && isDebugEnabled) {
  console.group('🔧 Environment Configuration');
  console.log('API URL:', env.API_URL);
  console.log('Tenant Key:', env.TENANT_KEY);
  console.log('Environment:', env.ENVIRONMENT);
  console.log('Debug Enabled:', env.ENABLE_DEBUG);
  
  const validation = validateEnvironment();
  if (!validation.isValid) {
    console.warn('⚠️ Environment validation errors:', validation.errors);
  } else {
    console.log('✅ Environment configuration is valid');
  }
  console.groupEnd();
}

export default env;
