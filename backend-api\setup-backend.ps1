# =============================================
# Backend API Setup Script for Compliance Management System
# =============================================

Write-Host "🚀 Starting Backend API Setup for Compliance Management System" -ForegroundColor Green
Write-Host "============================================================" -ForegroundColor Yellow

# Step 1: Check .NET SDK
Write-Host "📋 Step 1: Checking .NET SDK..." -ForegroundColor Cyan
try {
    $dotnetVersion = dotnet --version
    Write-Host "✅ .NET SDK found: $dotnetVersion" -ForegroundColor Green
} catch {
    Write-Host "❌ .NET SDK not found!" -ForegroundColor Red
    Write-Host "Please install .NET SDK 6.0 or higher from: https://dotnet.microsoft.com/download" -ForegroundColor Yellow
    Write-Host "After installation, restart this script." -ForegroundColor Yellow
    exit 1
}

# Step 2: Navigate to backend directory
Write-Host "`n📋 Step 2: Setting up project..." -ForegroundColor Cyan
$backendPath = "D:\DataOps Sync\Projects\CA-Portal-main\backend-api"
if (Test-Path $backendPath) {
    Set-Location $backendPath
    Write-Host "✅ Navigated to backend directory" -ForegroundColor Green
} else {
    Write-Host "❌ Backend directory not found: $backendPath" -ForegroundColor Red
    exit 1
}

# Step 3: Restore packages
Write-Host "`n📋 Step 3: Restoring NuGet packages..." -ForegroundColor Cyan
try {
    dotnet restore
    Write-Host "✅ NuGet packages restored successfully" -ForegroundColor Green
} catch {
    Write-Host "❌ Failed to restore packages" -ForegroundColor Red
    Write-Host "Error: $_" -ForegroundColor Red
    exit 1
}

# Step 4: Build project
Write-Host "`n📋 Step 4: Building project..." -ForegroundColor Cyan
try {
    dotnet build --configuration Release
    Write-Host "✅ Project built successfully" -ForegroundColor Green
} catch {
    Write-Host "❌ Build failed" -ForegroundColor Red
    Write-Host "Error: $_" -ForegroundColor Red
    exit 1
}

# Step 5: Check SQL Server
Write-Host "`n📋 Step 5: Checking SQL Server connectivity..." -ForegroundColor Cyan
try {
    # Test SQL Server connection
    $connectionString = "Server=localhost;Integrated Security=true;TrustServerCertificate=true;"
    $connection = New-Object System.Data.SqlClient.SqlConnection($connectionString)
    $connection.Open()
    $connection.Close()
    Write-Host "✅ SQL Server connection successful" -ForegroundColor Green
} catch {
    Write-Host "⚠️  SQL Server not accessible with Integrated Security" -ForegroundColor Yellow
    Write-Host "   This is normal if SQL Server is not installed locally" -ForegroundColor Yellow
    Write-Host "   The API will use mock data until database is set up" -ForegroundColor Yellow
}

# Step 6: Database setup instructions
Write-Host "`n📋 Step 6: Database Setup Instructions" -ForegroundColor Cyan
Write-Host "To set up the database:" -ForegroundColor White
Write-Host "1. Install SQL Server (Express or Developer edition)" -ForegroundColor White
Write-Host "2. Open SQL Server Management Studio (SSMS)" -ForegroundColor White
Write-Host "3. Run the script: Database/setup-complete-database.sql" -ForegroundColor White
Write-Host "4. Run the script: Database/compliance-stored-procedures.sql" -ForegroundColor White
Write-Host "5. Restart the API for full functionality" -ForegroundColor White

# Step 7: Configuration check
Write-Host "`n📋 Step 7: Checking configuration..." -ForegroundColor Cyan
if (Test-Path "appsettings.json") {
    Write-Host "✅ appsettings.json found" -ForegroundColor Green
    $config = Get-Content "appsettings.json" | ConvertFrom-Json
    
    # Check CORS settings
    if ($config.ApiSettings.AllowedOrigins -contains "http://localhost:8080") {
        Write-Host "✅ CORS configured for frontend (localhost:8080)" -ForegroundColor Green
    } else {
        Write-Host "⚠️  CORS may need configuration for localhost:8080" -ForegroundColor Yellow
    }
    
    # Check JWT settings
    if ($config.JwtSettings.Key) {
        Write-Host "✅ JWT settings configured" -ForegroundColor Green
    } else {
        Write-Host "⚠️  JWT settings may need configuration" -ForegroundColor Yellow
    }
} else {
    Write-Host "❌ appsettings.json not found" -ForegroundColor Red
}

# Step 8: Start the API
Write-Host "`n📋 Step 8: Starting the API..." -ForegroundColor Cyan
Write-Host "The API will start on:" -ForegroundColor White
Write-Host "  - HTTPS: https://localhost:7000" -ForegroundColor Green
Write-Host "  - HTTP:  http://localhost:5000" -ForegroundColor Green
Write-Host "  - Swagger: https://localhost:7000/swagger" -ForegroundColor Green

Write-Host "`nPress Ctrl+C to stop the API when needed" -ForegroundColor Yellow
Write-Host "============================================================" -ForegroundColor Yellow

# Start the API
try {
    dotnet run
} catch {
    Write-Host "❌ Failed to start API" -ForegroundColor Red
    Write-Host "Error: $_" -ForegroundColor Red
    exit 1
}
