import React, { useState, useEffect } from 'react';
import { Card, Row, Col, Statistic, Table, Tag, Button, DatePicker, Select, Space, Alert, Spin } from 'antd';
import { 
  CalendarOutlined, 
  CheckCircleOutlined, 
  ClockCircleOutlined, 
  ExclamationCircleOutlined,
  PlusOutlined,
  FilterOutlined
} from '@ant-design/icons';
import { complianceService, ComplianceItem, ComplianceDashboardDto } from '../../services/complianceService';
import { useAuth } from '../../contexts/AuthContext';
import dayjs from 'dayjs';

const { RangePicker } = DatePicker;
const { Option } = Select;

interface ComplianceDashboardProps {
  onCreateCompliance?: () => void;
  onViewCompliance?: (compliance: ComplianceItem) => void;
}

const ComplianceDashboard: React.FC<ComplianceDashboardProps> = ({
  onCreateCompliance,
  onViewCompliance
}) => {
  const { user } = useAuth();
  const [loading, setLoading] = useState(true);
  const [dashboardData, setDashboardData] = useState<ComplianceDashboardDto | null>(null);
  const [complianceItems, setComplianceItems] = useState<ComplianceItem[]>([]);
  const [selectedClient, setSelectedClient] = useState<string>('');
  const [dateRange, setDateRange] = useState<[dayjs.Dayjs, dayjs.Dayjs] | null>(null);
  const [error, setError] = useState<string>('');

  const [selectedStatus, setSelectedStatus] = useState<string>('');

  useEffect(() => {
    loadDashboardData();
  }, [selectedClient, selectedStatus]);

  const loadDashboardData = async () => {
    try {
      setLoading(true);
      setError('');

      // Load dashboard statistics
      const stats = await complianceService.getComplianceStats(selectedClient);
      setDashboardData(stats);

      // Load recent compliance items
      const itemsResponse = await complianceService.getComplianceItems(selectedClient, 1, 10, selectedStatus);
      setComplianceItems(itemsResponse.complianceItems);

    } catch (err: any) {
      setError(err.message || 'Failed to load dashboard data');
      console.error('Dashboard load error:', err);
    } finally {
      setLoading(false);
    }
  };

  const getStatusColor = (status: string) => {
    switch (status.toLowerCase()) {
      case 'completed': return 'green';
      case 'in progress': return 'blue';
      case 'overdue': return 'red';
      case 'pending': return 'orange';
      default: return 'default';
    }
  };

  const getPriorityColor = (priority: string) => {
    switch (priority.toLowerCase()) {
      case 'critical': return 'red';
      case 'high': return 'orange';
      case 'medium': return 'blue';
      case 'low': return 'green';
      default: return 'default';
    }
  };

  const columns = [
    {
      title: 'Compliance Type',
      dataIndex: 'complianceType',
      key: 'complianceType',
      render: (text: string) => <strong>{text}</strong>
    },
    {
      title: 'Client',
      dataIndex: 'clientName',
      key: 'clientName',
    },
    {
      title: 'Due Date',
      dataIndex: 'dueDate',
      key: 'dueDate',
      render: (date: string) => dayjs(date).format('MMM DD, YYYY'),
      sorter: (a: ComplianceItem, b: ComplianceItem) => 
        dayjs(a.dueDate).unix() - dayjs(b.dueDate).unix()
    },
    {
      title: 'Status',
      dataIndex: 'status',
      key: 'status',
      render: (status: string) => (
        <Tag color={getStatusColor(status)}>{status}</Tag>
      )
    },
    {
      title: 'Priority',
      dataIndex: 'priority',
      key: 'priority',
      render: (priority: string) => (
        <Tag color={getPriorityColor(priority)}>{priority}</Tag>
      )
    },
    {
      title: 'Assigned To',
      dataIndex: 'assignedToName',
      key: 'assignedToName',
    },
    {
      title: 'Actions',
      key: 'actions',
      render: (record: ComplianceItem) => (
        <Button 
          type="link" 
          onClick={() => onViewCompliance?.(record)}
        >
          View Details
        </Button>
      )
    }
  ];

  if (loading) {
    return (
      <div style={{ textAlign: 'center', padding: '50px' }}>
        <Spin size="large" />
        <p style={{ marginTop: '16px' }}>Loading compliance dashboard...</p>
      </div>
    );
  }

  return (
    <div style={{ padding: '24px' }}>
      {/* Header */}
      <div style={{ marginBottom: '24px', display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
        <div>
          <h1 style={{ margin: 0 }}>Compliance Dashboard</h1>
          <p style={{ margin: '4px 0 0 0', color: '#666' }}>
            Monitor and manage compliance requirements
          </p>
        </div>
        <Button 
          type="primary" 
          icon={<PlusOutlined />}
          onClick={onCreateCompliance}
        >
          Add Compliance Item
        </Button>
      </div>

      {/* Filters */}
      <Card style={{ marginBottom: '24px' }}>
        <Space wrap>
          <Select
            placeholder="Select Client"
            style={{ width: 200 }}
            value={selectedClient}
            onChange={setSelectedClient}
            allowClear
          >
            <Option value="">All Clients</Option>
            {/* Add client options here */}
          </Select>
          
          <Select
            placeholder="Status"
            style={{ width: 150 }}
            value={selectedStatus}
            onChange={setSelectedStatus}
            allowClear
          >
            <Option value="">All Statuses</Option>
            <Option value="Pending">Pending</Option>
            <Option value="Completed">Completed</Option>
            <Option value="Overdue">Overdue</Option>
            <Option value="In Progress">In Progress</Option>
          </Select>
          
          <RangePicker
            value={dateRange}
            onChange={setDateRange}
            placeholder={['Start Date', 'End Date']}
          />
          
          <Button icon={<FilterOutlined />}>
            More Filters
          </Button>
        </Space>
      </Card>

      {error && (
        <Alert
          message="Error"
          description={error}
          type="error"
          showIcon
          closable
          style={{ marginBottom: '24px' }}
        />
      )}

      {/* Statistics Cards */}
      <Row gutter={[16, 16]} style={{ marginBottom: '24px' }}>
        <Col xs={24} sm={12} md={6}>
          <Card>
            <Statistic
              title="Total Items"
              value={dashboardData?.totalItems || 0}
              prefix={<CalendarOutlined />}
              valueStyle={{ color: '#1890ff' }}
            />
          </Card>
        </Col>
        <Col xs={24} sm={12} md={6}>
          <Card>
            <Statistic
              title="Completed"
              value={dashboardData?.completedItems || 0}
              prefix={<CheckCircleOutlined />}
              valueStyle={{ color: '#52c41a' }}
            />
          </Card>
        </Col>
        <Col xs={24} sm={12} md={6}>
          <Card>
            <Statistic
              title="Pending"
              value={dashboardData?.pendingItems || 0}
              prefix={<ClockCircleOutlined />}
              valueStyle={{ color: '#faad14' }}
            />
          </Card>
        </Col>
        <Col xs={24} sm={12} md={6}>
          <Card>
            <Statistic
              title="Overdue"
              value={dashboardData?.overdueItems || 0}
              prefix={<ExclamationCircleOutlined />}
              valueStyle={{ color: '#ff4d4f' }}
            />
          </Card>
        </Col>
      </Row>

      {/* Recent Compliance Items */}
      <Card 
        title="Recent Compliance Items" 
        extra={
          <Button type="link">
            View All
          </Button>
        }
      >
        <Table
          columns={columns}
          dataSource={complianceItems}
          rowKey="complianceId"
          pagination={{
            pageSize: 10,
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total, range) => 
              `${range[0]}-${range[1]} of ${total} items`
          }}
          scroll={{ x: 800 }}
        />
      </Card>
    </div>
  );
};

export default ComplianceDashboard;
