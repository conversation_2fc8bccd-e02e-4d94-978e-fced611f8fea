-- Simple fix for Authentication Stored Procedures
USE [CA_Portal_kumar_associates];

-- Drop and recreate sp_AuthenticateUser
DROP PROCEDURE IF EXISTS sp_AuthenticateUser;
GO

CREATE PROCEDURE sp_AuthenticateUser
    @Payload NVARCHAR(MAX)
AS
BEGIN
    SET NOCOUNT ON;
    
    DECLARE @Email NVARCHAR(255)
    DECLARE @Password NVARCHAR(255)
    
    -- Parse JSON payload
    SELECT 
        @Email = JSON_VALUE(@Payload, '$.Email'),
        @Password = JSON_VALUE(@Payload, '$.Password')
    
    -- Authenticate user
    SELECT 
        u.UserId,
        u.FirstName,
        u.LastName,
        u.Email,
        u.Role,
        u.IsActive,
        u.CreatedAt,
        u.LastLoginAt
    FROM Users u
    WHERE u.Email = @Email 
        AND u.PasswordHash = @Password
        AND u.IsActive = 1
    
    FOR JSON PATH, WITHOUT_ARRAY_WRAPPER
END
GO

-- Drop and recreate sp_UpdateUserLastLogin
DROP PROCEDURE IF EXISTS sp_UpdateUserLastLogin;
GO

CREATE PROCEDURE sp_UpdateUserLastLogin
    @Payload NVARCHAR(MAX)
AS
BEGIN
    SET NOCOUNT ON;
    
    DECLARE @UserId UNIQUEIDENTIFIER
    DECLARE @LastLoginAt DATETIME
    
    -- Parse JSON payload
    SELECT 
        @UserId = TRY_CAST(JSON_VALUE(@Payload, '$.UserId') AS UNIQUEIDENTIFIER),
        @LastLoginAt = TRY_CAST(JSON_VALUE(@Payload, '$.LastLoginAt') AS DATETIME)
    
    -- Update last login time
    UPDATE Users
    SET LastLoginAt = @LastLoginAt
    WHERE UserId = @UserId AND IsActive = 1
    
    SELECT @@ROWCOUNT AS RowsAffected
END
GO

PRINT '✅ Authentication stored procedures fixed successfully!';
