-- =============================================
-- Step 4: Verification Script
-- =============================================

USE [KumarAssociatesDB];

PRINT '============================================================';
PRINT 'Phase 1 Database Verification';
PRINT '============================================================';

-- Check table existence and record counts
PRINT '';
PRINT 'TABLE VERIFICATION:';
PRINT '-------------------';

IF EXISTS (SELECT * FROM sysobjects WHERE name='Users' AND xtype='U')
BEGIN
    DECLARE @UserCount INT = (SELECT COUNT(*) FROM Users);
    PRINT '✅ Users table exists with ' + CAST(@UserCount AS NVARCHAR) + ' records';
END

IF EXISTS (SELECT * FROM sysobjects WHERE name='Clients' AND xtype='U')
BEGIN
    DECLARE @ClientCount INT = (SELECT COUNT(*) FROM Clients);
    PRINT '✅ Clients table exists with ' + CAST(@ClientCount AS NVARCHAR) + ' records';
END

IF EXISTS (SELECT * FROM sysobjects WHERE name='ComplianceTypes' AND xtype='U')
BEGIN
    DECLARE @TypeCount INT = (SELECT COUNT(*) FROM ComplianceTypes);
    PRINT '✅ ComplianceTypes table exists with ' + CAST(@TypeCount AS NVARCHAR) + ' records';
END

IF EXISTS (SELECT * FROM sysobjects WHERE name='Compliance' AND xtype='U')
BEGIN
    DECLARE @ComplianceCount INT = (SELECT COUNT(*) FROM Compliance);
    PRINT '✅ Compliance table exists with ' + CAST(@ComplianceCount AS NVARCHAR) + ' records';
END

IF EXISTS (SELECT * FROM sysobjects WHERE name='ComplianceHistory' AND xtype='U')
BEGIN
    DECLARE @HistoryCount INT = (SELECT COUNT(*) FROM ComplianceHistory);
    PRINT '✅ ComplianceHistory table exists with ' + CAST(@HistoryCount AS NVARCHAR) + ' records';
END

-- Check indexes
PRINT '';
PRINT 'INDEX VERIFICATION:';
PRINT '-------------------';

IF EXISTS (SELECT * FROM sys.indexes WHERE name = 'IX_Compliance_ClientId')
    PRINT '✅ IX_Compliance_ClientId exists';

IF EXISTS (SELECT * FROM sys.indexes WHERE name = 'IX_Compliance_DueDate')
    PRINT '✅ IX_Compliance_DueDate exists';

IF EXISTS (SELECT * FROM sys.indexes WHERE name = 'IX_Compliance_Status')
    PRINT '✅ IX_Compliance_Status exists';

-- Data quality checks
PRINT '';
PRINT 'DATA QUALITY VERIFICATION:';
PRINT '---------------------------';

DECLARE @NullClientIds INT = (SELECT COUNT(*) FROM Compliance WHERE ClientId IS NULL);
DECLARE @NullDueDates INT = (SELECT COUNT(*) FROM Compliance WHERE DueDate IS NULL);

IF @NullClientIds = 0
    PRINT '✅ All compliance items have valid ClientId';
ELSE
    PRINT '❌ Found ' + CAST(@NullClientIds AS NVARCHAR) + ' items with NULL ClientId';

IF @NullDueDates = 0
    PRINT '✅ All compliance items have valid DueDate';
ELSE
    PRINT '❌ Found ' + CAST(@NullDueDates AS NVARCHAR) + ' items with NULL DueDate';

-- Performance test
PRINT '';
PRINT 'PERFORMANCE TEST:';
PRINT '-----------------';

DECLARE @StartTime DATETIME2 = GETDATE();

SELECT COUNT(*) AS TestCount
FROM Compliance c
INNER JOIN Clients cl ON c.ClientId = cl.ClientId
WHERE c.Status = 'Pending';

DECLARE @QueryTime INT = DATEDIFF(MILLISECOND, @StartTime, GETDATE());
PRINT 'Query execution time: ' + CAST(@QueryTime AS NVARCHAR) + 'ms';

-- Final summary
PRINT '';
PRINT 'SUMMARY STATISTICS:';
PRINT '-------------------';

SELECT 
    Status,
    COUNT(*) AS Count
FROM Compliance
GROUP BY Status
ORDER BY Count DESC;

PRINT '';
PRINT '============================================================';
PRINT '✅ Phase 1 Complete - Database Ready for Phase 2!';
PRINT '============================================================';
