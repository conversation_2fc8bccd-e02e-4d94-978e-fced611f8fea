import React, { useState } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { authService } from '@/services/authService';
import { userService } from '@/services/userService';
import { clientService } from '@/services/clientService';
import { getCurrentUser, getCurrentTenant, isAuthenticated } from '@/services/api';
import { Loader2, CheckCircle, XCircle, User, Building, Server } from 'lucide-react';
import { env } from '@/config/env';

const ApiTest: React.FC = () => {
  const [testResults, setTestResults] = useState<Record<string, any>>({});
  const [isLoading, setIsLoading] = useState<Record<string, boolean>>({});

  const runTest = async (testName: string, testFunction: () => Promise<any>) => {
    setIsLoading(prev => ({ ...prev, [testName]: true }));
    try {
      const result = await testFunction();
      setTestResults(prev => ({ 
        ...prev, 
        [testName]: { success: true, data: result, error: null } 
      }));
    } catch (error: any) {
      setTestResults(prev => ({ 
        ...prev, 
        [testName]: { success: false, data: null, error: error.message } 
      }));
    } finally {
      setIsLoading(prev => ({ ...prev, [testName]: false }));
    }
  };

  const tests = [
    {
      name: 'authStatus',
      title: 'Authentication Status',
      description: 'Check if user is authenticated',
      icon: <User className="w-4 h-4" />,
      test: async () => {
        return {
          isAuthenticated: isAuthenticated(),
          currentUser: getCurrentUser(),
          currentTenant: getCurrentTenant(),
          userRole: authService.getUserRole()
        };
      }
    },
    {
      name: 'getCurrentUser',
      title: 'Get Current User',
      description: 'Fetch current user from API',
      icon: <User className="w-4 h-4" />,
      test: async () => {
        return await authService.getCurrentUser();
      }
    },
    {
      name: 'getUsers',
      title: 'Get Users List',
      description: 'Fetch paginated users list',
      icon: <User className="w-4 h-4" />,
      test: async () => {
        return await userService.getUsers({ pageSize: 5 });
      }
    },
    {
      name: 'getClients',
      title: 'Get Clients List',
      description: 'Fetch paginated clients list',
      icon: <Building className="w-4 h-4" />,
      test: async () => {
        return await clientService.getClients({ pageSize: 5 });
      }
    },
    {
      name: 'apiHealth',
      title: 'API Health Check',
      description: 'Test API connectivity',
      icon: <Server className="w-4 h-4" />,
      test: async () => {
        // Simple API health check
        const baseUrl = env.API_URL.replace('/api', '');
        const response = await fetch(baseUrl);
        return {
          status: response.status,
          statusText: response.statusText,
          apiUrl: env.API_URL
        };
      }
    }
  ];

  const renderTestResult = (testName: string) => {
    const result = testResults[testName];
    const loading = isLoading[testName];

    if (loading) {
      return <Loader2 className="w-4 h-4 animate-spin text-blue-500" />;
    }

    if (!result) {
      return <Badge variant="secondary">Not Run</Badge>;
    }

    if (result.success) {
      return (
        <div className="flex items-center space-x-2">
          <CheckCircle className="w-4 h-4 text-green-500" />
          <Badge variant="default" className="bg-green-100 text-green-800">Success</Badge>
        </div>
      );
    } else {
      return (
        <div className="flex items-center space-x-2">
          <XCircle className="w-4 h-4 text-red-500" />
          <Badge variant="destructive">Failed</Badge>
        </div>
      );
    }
  };

  const renderTestData = (testName: string) => {
    const result = testResults[testName];
    if (!result) return null;

    return (
      <div className="mt-2 p-2 bg-gray-50 rounded text-xs">
        {result.success ? (
          <pre className="text-green-700 overflow-auto max-h-32">
            {JSON.stringify(result.data, null, 2)}
          </pre>
        ) : (
          <div className="text-red-700">
            <strong>Error:</strong> {result.error}
          </div>
        )}
      </div>
    );
  };

  const runAllTests = async () => {
    for (const test of tests) {
      await runTest(test.name, test.test);
      // Small delay between tests
      await new Promise(resolve => setTimeout(resolve, 500));
    }
  };

  return (
    <div className="p-6 max-w-4xl mx-auto">
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <Server className="w-5 h-5" />
            <span>API Integration Test</span>
          </CardTitle>
          <CardDescription>
            Test the integration between React frontend and .NET Core API
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <div className="flex space-x-2">
              <Button onClick={runAllTests} className="flex items-center space-x-2">
                <Server className="w-4 h-4" />
                <span>Run All Tests</span>
              </Button>
            </div>

            <div className="grid gap-4">
              {tests.map((test) => (
                <Card key={test.name} className="border-l-4 border-l-blue-500">
                  <CardContent className="pt-4">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center space-x-3">
                        {test.icon}
                        <div>
                          <h4 className="font-medium">{test.title}</h4>
                          <p className="text-sm text-gray-600">{test.description}</p>
                        </div>
                      </div>
                      <div className="flex items-center space-x-2">
                        {renderTestResult(test.name)}
                        <Button
                          size="sm"
                          variant="outline"
                          onClick={() => runTest(test.name, test.test)}
                          disabled={isLoading[test.name]}
                        >
                          {isLoading[test.name] ? 'Running...' : 'Test'}
                        </Button>
                      </div>
                    </div>
                    {renderTestData(test.name)}
                  </CardContent>
                </Card>
              ))}
            </div>

            <div className="mt-6 p-4 bg-blue-50 rounded-lg">
              <h4 className="font-medium text-blue-800 mb-2">Environment Configuration</h4>
              <div className="text-sm text-blue-700 space-y-1">
                <div><strong>API URL:</strong> {env.API_URL}</div>
                <div><strong>Tenant Key:</strong> {env.TENANT_KEY}</div>
                <div><strong>Environment:</strong> {env.ENVIRONMENT}</div>
                <div><strong>Debug Mode:</strong> {env.ENABLE_DEBUG ? 'Enabled' : 'Disabled'}</div>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default ApiTest;
