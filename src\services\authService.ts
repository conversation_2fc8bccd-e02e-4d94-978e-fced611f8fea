// Authentication Service
import { apiClient, TokenManager, LoginRequest, LoginResponse, User, ApiResponse } from './api';

export interface ChangePasswordRequest {
  currentPassword: string;
  newPassword: string;
  confirmPassword: string;
}

export interface ForgotPasswordRequest {
  email: string;
  tenantKey?: string;
}

export interface ResetPasswordRequest {
  token: string;
  email: string;
  newPassword: string;
  confirmPassword: string;
}

class AuthService {
  /**
   * Login user
   */
  async login(credentials: LoginRequest): Promise<LoginResponse> {
    try {
      const response = await apiClient.post<LoginResponse>('/auth/login', credentials);
      
      if (response.success && response.data) {
        // Store authentication data
        TokenManager.setToken(response.data.token);
        TokenManager.setRefreshToken(response.data.refreshToken);
        TokenManager.setUser(response.data.user);
        TokenManager.setTenant(response.data.tenant);
        
        return response.data;
      } else {
        throw new Error(response.message || 'Login failed');
      }
    } catch (error: any) {
      console.error('Login error:', error);
      throw new Error(error.response?.data?.message || 'Login failed');
    }
  }

  /**
   * Logout user
   */
  async logout(): Promise<void> {
    try {
      // Call logout endpoint to invalidate token on server
      await apiClient.post('/auth/logout');
    } catch (error) {
      console.error('Logout error:', error);
    } finally {
      // Clear local storage regardless of API call result
      TokenManager.clearAll();
    }
  }

  /**
   * Get current user info
   */
  async getCurrentUser(): Promise<User> {
    try {
      const response = await apiClient.get<User>('/auth/me');
      
      if (response.success && response.data) {
        // Update stored user data
        TokenManager.setUser(response.data);
        return response.data;
      } else {
        throw new Error(response.message || 'Failed to get user info');
      }
    } catch (error: any) {
      console.error('Get current user error:', error);
      throw new Error(error.response?.data?.message || 'Failed to get user info');
    }
  }

  /**
   * Change password
   */
  async changePassword(request: ChangePasswordRequest): Promise<void> {
    try {
      const response = await apiClient.post('/auth/change-password', request);
      
      if (!response.success) {
        throw new Error(response.message || 'Failed to change password');
      }
    } catch (error: any) {
      console.error('Change password error:', error);
      throw new Error(error.response?.data?.message || 'Failed to change password');
    }
  }

  /**
   * Forgot password
   */
  async forgotPassword(request: ForgotPasswordRequest): Promise<void> {
    try {
      const response = await apiClient.post('/auth/forgot-password', request);
      
      if (!response.success) {
        throw new Error(response.message || 'Failed to send reset email');
      }
    } catch (error: any) {
      console.error('Forgot password error:', error);
      throw new Error(error.response?.data?.message || 'Failed to send reset email');
    }
  }

  /**
   * Reset password
   */
  async resetPassword(request: ResetPasswordRequest): Promise<void> {
    try {
      const response = await apiClient.post('/auth/reset-password', request);
      
      if (!response.success) {
        throw new Error(response.message || 'Failed to reset password');
      }
    } catch (error: any) {
      console.error('Reset password error:', error);
      throw new Error(error.response?.data?.message || 'Failed to reset password');
    }
  }

  /**
   * Check if user is authenticated
   */
  isAuthenticated(): boolean {
    const token = TokenManager.getToken();
    if (!token) return false;
    
    return !TokenManager.isTokenExpired(token);
  }

  /**
   * Get stored user
   */
  getStoredUser(): User | null {
    return TokenManager.getUser();
  }

  /**
   * Get stored tenant
   */
  getStoredTenant() {
    return TokenManager.getTenant();
  }

  /**
   * Refresh token
   */
  async refreshToken(): Promise<string> {
    try {
      const refreshToken = TokenManager.getRefreshToken();
      if (!refreshToken) {
        throw new Error('No refresh token available');
      }

      const response = await apiClient.post<{ token: string; refreshToken: string }>('/auth/refresh-token', {
        refreshToken
      });

      if (response.success && response.data) {
        TokenManager.setToken(response.data.token);
        TokenManager.setRefreshToken(response.data.refreshToken);
        return response.data.token;
      } else {
        throw new Error(response.message || 'Failed to refresh token');
      }
    } catch (error: any) {
      console.error('Refresh token error:', error);
      // Clear tokens and redirect to login
      TokenManager.clearAll();
      throw new Error('Session expired. Please login again.');
    }
  }

  /**
   * Get user role
   */
  getUserRole(): string | null {
    const user = this.getStoredUser();
    return user?.role || null;
  }

  /**
   * Check if user has specific role
   */
  hasRole(role: string): boolean {
    const userRole = this.getUserRole();
    return userRole === role;
  }

  /**
   * Check if user has any of the specified roles
   */
  hasAnyRole(roles: string[]): boolean {
    const userRole = this.getUserRole();
    return userRole ? roles.includes(userRole) : false;
  }

  /**
   * Check if user is admin
   */
  isAdmin(): boolean {
    return this.hasRole('Admin');
  }

  /**
   * Check if user is manager or admin
   */
  isManagerOrAdmin(): boolean {
    return this.hasAnyRole(['Admin', 'Manager']);
  }
}

// Export singleton instance
export const authService = new AuthService();
export default authService;
